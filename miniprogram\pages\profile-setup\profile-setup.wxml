<!--pages/profile-setup/profile-setup.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header" style="padding-top: {{statusBarHeight + 8}}px; height: {{menuButtonInfo.height}}px;">
    <view class="back-btn" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    <view class="title">{{isEditing ? '编辑资料' : '完善资料'}}</view>
    <view class="skip-btn" bindtap="skipSetup" wx:if="{{!isEditing}}">跳过</view>
    <view class="skip-btn-placeholder" wx:else></view>
  </view>

  <!-- 资料设置区域 -->
  <view class="profile-section">
    <view class="section-title">{{isEditing ? '修改你的个人资料' : '设置你的个人资料'}}</view>
    <view class="section-desc">{{isEditing ? '更新头像和昵称信息' : '设置头像和昵称，让其他用户更好地认识你'}}</view>

    <!-- 头像设置 -->
    <view class="avatar-section">
      <view class="avatar-container">
        <button 
          class="avatar-btn" 
          open-type="chooseAvatar" 
          bind:chooseavatar="onChooseAvatar"
        >
          <image 
            class="avatar" 
            src="{{avatarUrl}}" 
            mode="aspectFill"
            binderror="onAvatarError"
          />
          <view class="avatar-mask">
            <view class="avatar-icon">📷</view>
            <view class="avatar-tip">点击更换</view>
          </view>
        </button>
        <view class="avatar-label">头像</view>
      </view>
    </view>

    <!-- 昵称设置 -->
    <view class="nickname-section">
      <view class="input-group">
        <view class="input-container">
          <view class="nickname-label">昵称</view>
          <input 
            class="nickname-input"
            type="nickname"
            placeholder="请输入昵称（2-8字符）"
            value="{{nickName}}"
            maxlength="8"
            bindinput="onNickNameInput"
          />
          <view class="input-counter">{{nickName.length}}/8</view>
        </view>
        <view class="input-tip">昵称将显示在排行榜和游戏记录中</view>
      </view>
    </view>
  </view>

  <!-- 保存按钮 -->
  <view class="save-section">
    <button 
      class="save-btn {{!canSave ? 'disabled' : ''}}"
      bindtap="saveProfile"
      disabled="{{!canSave}}"
    >
      <text wx:if="{{loading}}">保存中...</text>
      <text wx:else>{{isEditing ? '保存修改' : '完成设置'}}</text>
    </button>
  </view>

  <!-- 提示信息 -->
  <view class="tips-section">
    <view class="tips-card">
      <view class="tips-header">
        <text class="tips-icon">💡</text>
        <text class="tips-title">温馨提示</text>
      </view>
      <view class="tips-content">
        <view class="tips-item">
          <text class="tip-dot">•</text>
          <text class="tip-text">头像支持自动压缩，提升上传速度</text>
        </view>
        <view class="tips-item">
          <text class="tip-dot">•</text>
          <text class="tip-text">昵称可随时在个人中心修改</text>
        </view>
        <view class="tips-item">
          <text class="tip-dot">•</text>
          <text class="tip-text">建议使用微信头像，增强游戏体验</text>
        </view>
      </view>
    </view>
  </view>
</view> 