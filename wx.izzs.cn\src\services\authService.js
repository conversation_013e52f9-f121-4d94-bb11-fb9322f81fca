const https = require('https');
const mysql = require('mysql2/promise');
const dbConfig = require('../config/db');
const wxConfig = require('../config/wechat');

// 微信登录
exports.wxLogin = async (code) => {
  try {
    // 验证微信配置
    wxConfig.validate();
    
    // 1. 调用微信接口获取 openid
    const wxResult = await getOpenIdFromWechat(code);
    
    if (wxResult.errcode) {
      return {
        success: false,
        error: `微信接口错误: ${wxResult.errmsg}`
      };
    }

    const { openid, session_key } = wxResult;

    // 2. 检查用户是否存在，不存在则创建
    await ensureUserExists(openid);

    // 3. 记录登录日志
    await recordLoginLog(openid);

    return {
      success: true,
      openid,
      sessionKey: session_key
    };

  } catch (error) {
    console.error('微信登录服务失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 验证登录状态
exports.verifyLogin = async (openid) => {
  try {
    const conn = await mysql.createConnection(dbConfig);
    
    // 查询用户信息
    const [userRows] = await conn.execute(`
      SELECT 
        id,
        nickname,
        avatar_url,
        total_score,
        total_answers,
        correct_answers,
        created_at,
        updated_at
      FROM users 
      WHERE id = ?
    `, [openid]);

    await conn.end();

    if (userRows.length === 0) {
      return {
        isValid: false,
        userInfo: null
      };
    }

    return {
      isValid: true,
      userInfo: userRows[0]
    };

  } catch (error) {
    console.error('验证登录状态失败:', error);
    return {
      isValid: false,
      userInfo: null
    };
  }
};

// 刷新登录状态
exports.refreshLogin = async (openid) => {
  try {
    const conn = await mysql.createConnection(dbConfig);
    
    // 更新最后活跃时间
    await conn.execute(`
      UPDATE users 
      SET updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `, [openid]);

    await conn.end();

    return {
      refreshTime: new Date().toISOString()
    };

  } catch (error) {
    console.error('刷新登录状态失败:', error);
    throw error;
  }
};

// ==================== 私有函数 ====================

// 调用微信接口获取 openid
function getOpenIdFromWechat(code) {
  return new Promise((resolve, reject) => {
    const { appId, appSecret } = wxConfig.miniProgram;
    const url = `${wxConfig.api.code2Session}?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`;

    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve(result);
        } catch (error) {
          reject(new Error('解析微信接口响应失败'));
        }
      });
      
    }).on('error', (error) => {
      reject(new Error('调用微信接口失败: ' + error.message));
    });
  });
}

// 确保用户存在
async function ensureUserExists(openid) {
  const conn = await mysql.createConnection(dbConfig);
  
  try {
    // 检查用户是否存在
    const [existingRows] = await conn.execute(
      'SELECT id FROM users WHERE id = ?',
      [openid]
    );

    // 如果用户不存在，创建新用户
    if (existingRows.length === 0) {
      // 生成随机用户名
      const randomNickname = generateRandomNickname();
      
      await conn.execute(`
        INSERT INTO users (
          id, 
          nickname, 
          avatar_url, 
          total_score, 
          total_answers, 
          correct_answers,
          created_at,
          updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [openid, randomNickname, '', 0, 0, 0]);
    }

  } finally {
    await conn.end();
  }
}

// 生成随机昵称
function generateRandomNickname() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let suffix = '';
  for (let i = 0; i < 4; i++) {
    suffix += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return `用户${suffix}`;
}

// 记录登录日志
async function recordLoginLog(openid) {
  try {
    const conn = await mysql.createConnection(dbConfig);
    
    await conn.execute(`
      INSERT INTO login_logs (
        user_id, 
        login_time, 
        ip_address, 
        user_agent
      ) VALUES (?, CURRENT_TIMESTAMP, ?, ?)
    `, [openid, '', '']);

    await conn.end();
  } catch (error) {
    // 登录日志记录失败不影响登录流程
    console.warn('记录登录日志失败:', error);
  }
} 