-- 竞技模式游戏表
CREATE TABLE IF NOT EXISTS `comp_games` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `game_id` varchar(50) NOT NULL COMMENT '游戏唯一ID',
  `types` varchar(255) NOT NULL COMMENT '题目类型，逗号分隔',
  `quantities` text NOT NULL COMMENT '每种类型的数量，JSON格式',
  `total_questions` int(11) NOT NULL DEFAULT 0 COMMENT '总题目数量',
  `participant_count` int(11) NOT NULL DEFAULT 0 COMMENT '参与人数',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `game_id` (`game_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='竞技模式游戏信息表';

-- 竞技模式成绩表
CREATE TABLE IF NOT EXISTS `comp_scores` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `game_id` varchar(50) NOT NULL COMMENT '游戏ID',
  `user_id` varchar(50) NOT NULL COMMENT '用户ID',
  `score` int(11) NOT NULL DEFAULT 0 COMMENT '得分',
  `correct_count` int(11) NOT NULL DEFAULT 0 COMMENT '答对题数',
  `total_questions` int(11) NOT NULL DEFAULT 0 COMMENT '总题目数',
  `accuracy` int(11) NOT NULL DEFAULT 0 COMMENT '正确率(百分比)',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `game_user` (`game_id`, `user_id`),
  KEY `game_id` (`game_id`),
  KEY `user_id` (`user_id`),
  KEY `score` (`score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='竞技模式成绩表'; 