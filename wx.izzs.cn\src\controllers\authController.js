const authService = require('../services/authService');

// 微信登录
exports.wxLogin = async (req, res) => {
  try {
    const { code } = req.body;
    
    if (!code) {
      return res.json({
        code: 1,
        msg: '登录凭证不能为空'
      });
    }

    const result = await authService.wxLogin(code);
    
    if (result.success) {
      res.json({
        code: 0,
        data: {
          openid: result.openid,
          session_key: result.sessionKey
        },
        msg: '登录成功'
      });
    } else {
      res.json({
        code: 1,
        msg: result.error || '登录失败'
      });
    }
    
  } catch (error) {
    console.error('微信登录失败:', error);
    res.json({
      code: 1,
      msg: '服务器内部错误'
    });
  }
};

// 验证登录状态
exports.verifyLogin = async (req, res) => {
  try {
    const { openid } = req.query;
    
    if (!openid) {
      return res.json({
        code: 1,
        msg: 'openid不能为空'
      });
    }

    const result = await authService.verifyLogin(openid);
    
    res.json({
      code: 0,
      data: {
        isValid: result.isValid,
        userInfo: result.userInfo
      }
    });
    
  } catch (error) {
    console.error('验证登录状态失败:', error);
    res.json({
      code: 1,
      msg: '验证失败'
    });
  }
};

// 刷新登录状态
exports.refreshLogin = async (req, res) => {
  try {
    const { openid } = req.body;
    
    if (!openid) {
      return res.json({
        code: 1,
        msg: 'openid不能为空'
      });
    }

    const result = await authService.refreshLogin(openid);
    
    res.json({
      code: 0,
      data: result,
      msg: '刷新成功'
    });
    
  } catch (error) {
    console.error('刷新登录状态失败:', error);
    res.json({
      code: 1,
      msg: '刷新失败'
    });
  }
}; 