const authManager = require('../../utils/auth')
const env = require('../../env.js')
const { processAvatarUrl, generateFallbackAvatar } = require('../../utils/avatarHelper')

Page({
  data: {
    avatarUrl: '', // 改为空字符串，后续根据情况设置
    nickName: '',
    loading: false,
    hasChanged: false,
    isEditing: false, // 是否为编辑模式
    statusBarHeight: 0, // 状态栏高度
    menuButtonInfo: {}, // 胶囊按钮信息
    canSave: false // 是否可以保存
  },

  onLoad(options) {
    // 获取系统信息
    this.getSystemInfo()
    
    // 检查登录状态
    const status = authManager.checkLoginStatus()
    if (!status.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      wx.navigateBack()
      return
    }

    // 检查是否为编辑模式
    const isEditing = options.edit === 'true'
    this.setData({ isEditing })

    if (isEditing && status.hasUserInfo) {
      // 编辑模式，加载现有资料
      const userInfo = status.userInfo
      let avatarUrl = userInfo.avatarUrl || userInfo.avatar_url
      
      // 处理头像URL
      if (avatarUrl) {
        // 如果是服务器头像URL，添加时间戳避免缓存
        if (avatarUrl.includes('/static/pic/')) {
          const timestamp = Date.now()
          avatarUrl = avatarUrl.includes('?') 
            ? `${avatarUrl}&t=${timestamp}` 
            : `${avatarUrl}?t=${timestamp}`
        }
        
        // 处理头像URL，确保是完整的URL
        avatarUrl = processAvatarUrl(avatarUrl, userInfo.id || userInfo.openid, { avoidCache: true })
      } else {
        // 如果没有头像，生成一个基于用户ID的默认头像
        avatarUrl = generateFallbackAvatar(userInfo.id || userInfo.openid)
      }
      
      this.setData({
        nickName: userInfo.nickName || userInfo.nickname || this.generateRandomNickname(),
        avatarUrl: avatarUrl
      })
    } else {
      // 新建模式，生成随机昵称和默认头像
      const app = getApp()
      const openid = app.globalData.openid || wx.getStorageSync('openid')
      const defaultAvatar = generateFallbackAvatar(openid || 'default')
      
      this.setData({
        nickName: this.generateRandomNickname(),
        avatarUrl: defaultAvatar
      })
    }
    
    // 更新保存按钮状态
    this.updateCanSave()
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
      
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight,
        menuButtonInfo: menuButtonInfo
      })
    } catch (e) {
      console.error('获取系统信息失败:', e)
    }
  },

  // 生成随机昵称
  generateRandomNickname() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let suffix = ''
    for (let i = 0; i < 4; i++) {
      suffix += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return `用户${suffix}`
  },

  // 选择头像
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail
    
    // 立即在预览中显示选择的头像
    this.setData({
      avatarUrl: avatarUrl,
      hasChanged: true
    })
    
    // 更新保存按钮状态
    this.updateCanSave()
    
    // 显示选择成功提示
    wx.showToast({
      title: '头像已选择',
      icon: 'success',
      duration: 1500
    })
    
    // 异步尝试上传到服务器（不阻塞预览显示）
    this.uploadAvatarInBackground(avatarUrl)
  },

  // 后台上传头像（不影响预览显示）
  async uploadAvatarInBackground(avatarUrl) {
    try {
      // 先压缩头像
      const compressedPath = await this.compressImage(avatarUrl)
      
      // 尝试上传到服务器
      const serverAvatarUrl = await this.uploadAvatarToServer(compressedPath)
      
      // 上传成功，更新为服务器URL（添加时间戳避免缓存）
      const timestamp = Date.now()
      const avatarUrlWithTimestamp = serverAvatarUrl.includes('?') 
        ? `${serverAvatarUrl}&t=${timestamp}` 
        : `${serverAvatarUrl}?t=${timestamp}`
      
      this.setData({
        avatarUrl: avatarUrlWithTimestamp
      })
      
      console.log('头像后台上传成功:', serverAvatarUrl)
      
    } catch (error) {
      console.log('头像后台上传失败，将在保存时重试:', error.message)
      // 不显示错误提示，保持当前预览，保存时会重试上传
    }
  },

  // 上传头像到服务器
  uploadAvatarToServer(filePath) {
    return new Promise((resolve, reject) => {
      const app = getApp()
      const openid = app.globalData.openid || wx.getStorageSync('openid')
      
      if (!openid) {
        reject(new Error('用户未登录'))
        return
      }

      wx.uploadFile({
        url: `${env.API_BASE_URL}/api/user/upload-avatar`,
        filePath: filePath,
        name: 'avatar',
        formData: {
          openid: openid
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (data.code === 0) {
              resolve(data.data.avatarUrl)
            } else {
              reject(new Error(data.msg || '上传失败'))
            }
          } catch (e) {
            reject(new Error('服务器响应格式错误'))
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败: ' + error.errMsg))
        }
      })
    })
  },

  // 压缩图片
  compressImage(src) {
    return new Promise((resolve, reject) => {
      wx.compressImage({
        src: src,
        quality: 80, // 压缩质量
        success: (res) => {
          resolve(res.tempFilePath)
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  },

  // 输入昵称
  onNickNameInput(e) {
    const nickName = e.detail.value
    this.setData({
      nickName,
      hasChanged: true
    })
    
    // 更新保存按钮状态
    this.updateCanSave()
  },

  // 保存资料
  async saveProfile() {
    console.log('saveProfile 被调用')
    const { avatarUrl, nickName, loading } = this.data
    console.log('当前数据:', { avatarUrl, nickName, loading })

    if (loading) {
      console.log('正在加载中，跳过保存')
      return
    }

    // 验证输入
    if (!nickName.trim()) {
      console.log('昵称为空')
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    if (nickName.trim().length > 8) {
      console.log('昵称超过8个字符')
      wx.showToast({
        title: '昵称不能超过8个字符',
        icon: 'none'
      })
      return
    }

    if (nickName.trim().length < 2) {
      console.log('昵称少于2个字符')
      wx.showToast({
        title: '昵称至少需要2个字符',
        icon: 'none'
      })
      return
    }

    console.log('验证通过，开始保存')
    this.setData({ loading: true })
    this.updateCanSave()

    try {
      wx.showLoading({ title: '保存中...' })

      let finalAvatarUrl = avatarUrl

      // 检查是否需要上传头像到服务器
      const needUpload = avatarUrl && (
        avatarUrl.startsWith('http://tmp/') || 
        avatarUrl.startsWith('wxfile://') ||
        avatarUrl.startsWith('blob:') ||
        avatarUrl.includes('weixin://') ||
        (!avatarUrl.startsWith('http://') && !avatarUrl.startsWith('https://'))
      )

      if (needUpload) {
        console.log('检测到需要上传的头像，开始上传...', avatarUrl)
        wx.showLoading({ title: '上传头像中...' })
        
        try {
          finalAvatarUrl = await this.uploadAvatarToServer(avatarUrl)
          console.log('头像上传成功:', finalAvatarUrl)
        } catch (uploadError) {
          console.error('头像上传失败:', uploadError)
          
          // 尝试先压缩再上传
          try {
            const compressedPath = await this.compressImage(avatarUrl)
            finalAvatarUrl = await this.uploadAvatarToServer(compressedPath)
            console.log('压缩后头像上传成功:', finalAvatarUrl)
          } catch (compressUploadError) {
            console.error('压缩后头像上传也失败:', compressUploadError)
            wx.showToast({
              title: '头像上传失败，请重试',
              icon: 'none'
            })
            return
          }
        }
        
        // 添加时间戳避免缓存问题
        const timestamp = Date.now()
        const finalAvatarUrlWithTimestamp = finalAvatarUrl.includes('?') 
          ? `${finalAvatarUrl}&t=${timestamp}` 
          : `${finalAvatarUrl}?t=${timestamp}`
        
        finalAvatarUrl = finalAvatarUrlWithTimestamp
      }

      const result = await authManager.saveUserProfile({
        nickName: nickName.trim(),
        avatarUrl: finalAvatarUrl
      })

      console.log('保存结果:', result)

      if (result.success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 延迟一下再跳转，让用户看到成功提示
        setTimeout(() => {
          if (this.data.isEditing) {
            // 编辑模式返回个人中心
            wx.navigateBack()
          } else {
            // 新建模式跳转到首页
            wx.switchTab({
              url: '/pages/index/index'
            })
          }
        }, 1500)
      } else {
        console.error('保存失败:', result.error)
        wx.showToast({
          title: result.error || '保存失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('保存资料失败:', error)
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
      this.setData({ loading: false })
      this.updateCanSave()
    }
  },

  // 跳过设置
  skipSetup() {
    if (this.data.isEditing) {
      // 编辑模式不允许跳过
      wx.navigateBack()
      return
    }

    wx.showModal({
      title: '跳过设置',
      content: '跳过后将使用默认资料，后续可在个人中心修改',
      success: async (res) => {
        if (res.confirm) {
          await this.saveProfile()
        }
      }
    })
  },

  // 返回上一页
  goBack() {
    if (this.data.hasChanged) {
      wx.showModal({
        title: '提示',
        content: '资料尚未保存，确定要离开吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack()
          }
        }
      })
    } else {
      wx.navigateBack()
    }
  },

  // 更新保存按钮状态
  updateCanSave() {
    const { nickName, loading } = this.data
    const trimmedName = (nickName || '').trim()
    const canSave = !loading && trimmedName.length >= 2 && trimmedName.length <= 8
    
    this.setData({ canSave })
  },

  // 头像加载错误处理
  onAvatarError(e) {
    console.error('头像加载失败:', e.detail)
    
    // 使用基于用户ID的默认头像
    const app = getApp()
    const openid = app.globalData.openid || wx.getStorageSync('openid')
    const fallbackAvatar = generateFallbackAvatar(openid || 'default')
    
    this.setData({
      avatarUrl: fallbackAvatar
    })
    
    wx.showToast({
      title: '头像加载失败，已使用默认头像',
      icon: 'none',
      duration: 2000
    })
  }
}) 