const userService = require('../services/userService');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 配置multer用于头像上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../static/pic');
    
    // 确保目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const { openid } = req.body;
    if (!openid) {
      return cb(new Error('OpenID不能为空'));
    }
    
    // 获取文件扩展名
    const ext = path.extname(file.originalname).toLowerCase();
    const allowedExts = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    
    if (!allowedExts.includes(ext)) {
      return cb(new Error('不支持的图片格式'));
    }
    
    // 使用openid作为文件名
    cb(null, `${openid}${ext}`);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB限制
  },
  fileFilter: function (req, file, cb) {
    // 检查文件类型
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('只能上传图片文件'));
    }
  }
});

// 获取排行榜
exports.getRanking = async (req, res) => {
  try {
    const data = await userService.getRanking();
    res.json({ code: 0, data });
  } catch (err) {
    res.json({ code: 1, msg: '获取排行榜失败', error: err.message });
  }
};

// 获取个人信息
exports.getProfile = async (req, res) => {
  try {
    const { openid } = req.query;
    const data = await userService.getProfile(openid);
    res.json({ code: 0, data });
  } catch (err) {
    res.json({ code: 1, msg: '获取个人信息失败', error: err.message });
  }
};

// 获取用户资料
exports.getUserProfile = async (req, res) => {
  try {
    const { openid } = req.query;
    if (!openid) {
      return res.json({ code: 1, msg: '用户ID不能为空' });
    }
    
    const data = await userService.getUserProfile(openid);
    if (data) {
      res.json({ code: 0, data });
    } else {
      res.json({ code: 1, msg: '用户不存在' });
    }
  } catch (err) {
    console.error('获取用户资料失败:', err);
    res.json({ 
      code: 1, 
      msg: '获取用户资料失败', 
      error: err.message 
    });
  }
};

// 获取用户统计信息
exports.getUserStats = async (req, res) => {
  try {
    const { openid } = req.query;
    if (!openid) {
      return res.json({ code: 1, msg: '用户ID不能为空' });
    }
    
    const data = await userService.getUserStats(openid);
    res.json({ code: 0, data });
  } catch (err) {
    console.error('获取用户统计失败:', err);
    res.json({ 
      code: 1, 
      msg: '获取用户统计失败', 
      error: err.message 
    });
  }
};

// 保存用户信息
exports.saveUser = async (req, res) => {
  try {
    const userData = req.body;
    if (!userData.openid) {
      return res.json({ code: 1, msg: '用户openid不能为空' });
    }
    
    const data = await userService.saveUser(userData);
    res.json({ code: 0, data, msg: '保存成功' });
  } catch (err) {
    console.error('保存用户信息失败:', err);
    res.json({ 
      code: 1, 
      msg: '保存用户信息失败', 
      error: err.message 
    });
  }
};

// 上传头像
exports.uploadAvatar = [
  upload.single('avatar'),
  async (req, res) => {
    try {
      const { openid } = req.body;
      
      if (!openid) {
        return res.json({ code: 1, msg: 'OpenID不能为空' });
      }
      
      if (!req.file) {
        return res.json({ code: 1, msg: '未收到文件' });
      }
      
      // 构建完整的头像URL
      const baseUrl = process.env.BASE_URL || 'https://wx.izzs.cn';
      const avatarUrl = `${baseUrl}/static/pic/${req.file.filename}`;
      
      // 更新用户表中的头像URL（仍存储相对路径，便于域名变更）
      const relativePath = `/static/pic/${req.file.filename}`;
      const result = await userService.updateUserAvatar(openid, relativePath);
      
      if (result.success) {
        res.json({
          code: 0,
          data: { avatarUrl }, // 返回完整URL给前端
          msg: '头像上传成功'
        });
      } else {
        // 如果数据库更新失败，删除已上传的文件
        fs.unlink(req.file.path, (err) => {
          if (err) console.error('删除文件失败:', err);
        });
        
        res.json({
          code: 1,
          msg: '头像保存失败: ' + result.error
        });
      }
      
    } catch (err) {
      console.error('头像上传失败:', err);
      
      // 删除已上传的文件（如果存在）
      if (req.file && req.file.path) {
        fs.unlink(req.file.path, (unlinkErr) => {
          if (unlinkErr) console.error('删除文件失败:', unlinkErr);
        });
      }
      
      res.json({
        code: 1,
        msg: '头像上传失败: ' + err.message
      });
    }
  }
]; 