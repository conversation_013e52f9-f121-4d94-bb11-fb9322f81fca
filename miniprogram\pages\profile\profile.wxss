/* 页面容器 */
.container {
  min-height: 100vh;
  background: #f5f6fa;
  padding-bottom: calc(env(safe-area-inset-bottom) + 30rpx); /* 原有安全区域 + 20px(40rpx) */
}

/* 用户信息头部 */
.user-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 30rpx 40rpx;
  position: relative;
}

/* 登录区域 */
.login-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}

.login-avatar {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.8;
}

.login-info {
  text-align: center;
  margin-bottom: 40rpx;
}

.login-title {
  display: block;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.login-desc {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
}

.login-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.login-btn:disabled {
  opacity: 0.6;
}

/* 用户信息 */
.user-info {
  color: white;
}

.user-main {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 30rpx;
}

.user-details {
  flex: 1;
}

.user-nickname {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.user-rank {
  display: flex;
  align-items: center;
}

.rank-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.rank-text {
  font-size: 26rpx;
  opacity: 0.9;
}

.edit-profile {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.edit-icon {
  font-size: 28rpx;
}

.total-score {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 30rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.score-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.score-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #e0e0e0;
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 26rpx;
}

/* 主要内容区域 */
.profile-content {
  padding: 0 30rpx;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin: -40rpx 0 40rpx;
  position: relative;
  z-index: 10;
}

.stat-card {
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 标签页 */
.tabs-container {
  margin-bottom: 8rpx;
}

.tabs {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 12rpx;
  transition: all 0.3s;
}

.tab-item.active {
  background: #667eea;
  color: white;
  font-weight: 500;
}

/* 标签页内容容器 - 固定大小，常驻显示 */
.tab-content-container {
  margin-bottom: 0;
}

/* 标签页内容 */
.tab-content {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  height: 720rpx; /* 固定高度 */
  display: flex;
  flex-direction: column;
}

/* 内容区域 - 确保固定高度 */
.content-area {
  flex: 1; /* 改为flex: 1，让内容区域自适应剩余空间 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出 */
}

/* 区域样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-top: 30rpx; /* 向下移动文字 */
}

.achievement-count {
  font-size: 24rpx;
  color: #666;
}

/* 连胜区域 */
.streak-section {
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.streak-info {
  display: flex;
  gap: 40rpx;
}

.streak-item {
  flex: 1;
  text-align: center;
}

.streak-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.streak-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
}

.streak-value.current {
  color: #ff6b6b;
}

.streak-value.best {
  color: #4ecdc4;
}

/* 每日统计 */
.daily-stats-section {
  padding: 30rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.daily-stats {
  max-height: 300rpx;
  overflow-y: auto;
}

.daily-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.daily-stat-item:last-child {
  border-bottom: none;
}

.stat-date {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.stat-details {
  display: flex;
  gap: 20rpx;
  font-size: 24rpx;
  color: #666;
}

/* 成就网格 */
.achievements-section {
  padding: 24rpx 24rpx;
  height: calc(4 * 80rpx + 3 * 16rpx + 0rpx);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
  margin-top: 10rpx;
  width: 100%;
  height: calc(4 * 80rpx + 3 * 16rpx);
}

.achievement-item {
  background: #f8f9fa;
  padding: 8rpx 0;
  border-radius: 10rpx;
  text-align: center;
  border: 2rpx solid transparent;
  position: relative;
  min-height: 70rpx;
  height: 70rpx;
  font-size: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.achievement-item.earned {
  background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
  color: white;
}

.achievement-item.locked {
  background: #f0f0f0;
  color: #bbb;
  opacity: 0.6;
}

.achievement-item.empty {
  background: transparent;
  border: none;
  opacity: 0;
  pointer-events: none;
}

.achievement-icon {
  font-size: 28rpx;
  margin-bottom: 2rpx;
}

.achievement-name {
  font-size: 18rpx;
  font-weight: 500;
  margin-bottom: 0;
}

.achievement-date {
  font-size: 14rpx;
  opacity: 0.7;
}

.achievement-lock {
  position: absolute;
  right: 6rpx;
  top: 6rpx;
  font-size: 18rpx;
  opacity: 0.7;
}

/* 答题记录 */
.records-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden; /* 防止内容溢出 */
  padding: 16rpx 24rpx;
}

.records-list {
  flex: 1; /* 让列表占据剩余空间 */
  overflow-y: auto; /* 允许垂直滚动 */
  -webkit-overflow-scrolling: touch; /* 增加弹性滚动效果 */
  padding: 8rpx 0;
  box-sizing: border-box;
}

.record-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  padding: 18rpx 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  transition: box-shadow 0.2s;
  width: 100%;
  box-sizing: border-box;
}

.record-item:last-child {
  margin-bottom: 0;
}

.record-item.correct {
  border-left: 8rpx solid #07c160;
}

.record-item.wrong {
  border-left: 8rpx solid #ff6b6b;
}

.record-result {
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.result-icon {
  font-size: 32rpx;
  display: block;
}

.record-content {
  flex: 1;
  min-width: 0;
  margin-right: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
}

.record-question {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 4rpx;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  box-sizing: border-box;
}

.record-details {
  display: flex;
  gap: 16rpx;
  font-size: 22rpx;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.record-score {
  text-align: center;
  min-width: 100rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: baseline;
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  flex-shrink: 0;
}

.score-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-right: 4rpx;
}

.score-unit {
  font-size: 20rpx;
  color: #999;
}

/* 设置区域 */
.settings-section {
  margin-top: 30rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-title-with-icon {
  display: flex;
  align-items: center;
}

.section-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  margin-left: 30rpx;
  margin-top: 30rpx;
}

.settings-list {
  padding: 0;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: #f8f9fa;
}

.setting-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-left .setting-icon {
  font-size: 24rpx;
  margin-right: 20rpx;
  width: 32rpx;
  text-align: center;
}

.setting-label {
  font-size: 28rpx;
  color: #333;

}

.setting-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
}

.arrow-icon {
  font-size: 24rpx;
  color: #999;
  font-weight: bold;
}

/* 空状态 - 使用图片 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 30rpx;
  flex: 1;
  min-height: 200rpx;
}

.empty-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.empty-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

/* 未登录状态 */
.not-login-content {
  text-align: center;
  padding: 100rpx 30rpx;
}

.not-login-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.not-login-title {
  display: block;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.not-login-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
} 