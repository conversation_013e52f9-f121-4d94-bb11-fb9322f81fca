/**
 * 头像处理工具函数
 */

// 生成备用头像URL
function generateFallbackAvatar(userId) {
  // 基于用户ID生成1-75之间的固定随机数，确保同一用户始终使用同一个备用头像
  let hash = 0;
  if (userId) {
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
  }
  
  // 将hash转换为1-75之间的正数
  const avatarIndex = Math.abs(hash % 75) + 1;
  
  return `/static/avatar/face (${avatarIndex}).jpg`;
}

// 处理用户头像，返回可用的头像URL
function processUserAvatar(user) {
  if (!user) return user;
  
  // 如果用户有真实头像且不为空字符串，使用真实头像
  if (user.avatar_url && user.avatar_url.trim() !== '') {
    // 如果已经是完整URL，直接使用；否则补充服务器域名
    if (user.avatar_url.startsWith('http://') || user.avatar_url.startsWith('https://')) {
      return {
        ...user,
        avatar_url: user.avatar_url
      };
    } else {
      // 相对路径转换为完整URL
      return {
        ...user,
        avatar_url: `${user.avatar_url.startsWith('/') ? '' : '/'}${user.avatar_url}`
      };
    }
  }
  
  // 否则使用备用头像
  return {
    ...user,
    avatar_url: generateFallbackAvatar(user.id || user.user_id || '')
  };
}

// 批量处理用户头像
function processUsersAvatars(users) {
  if (!Array.isArray(users)) return users;
  
  return users.map(user => processUserAvatar(user));
}

module.exports = {
  generateFallbackAvatar,
  processUserAvatar,
  processUsersAvatars
}; 