const mysql = require('mysql2/promise');
const dbConfig = require('../config/db');

// 类型与表名映射
const typeTableMap = {
  'riddle': 'word_riddles',
  'idiom': 'idiom_riddles',
  'xiehouyu': 'xiehouyu_riddles',
  'animal': 'animal_riddles',
  'fruit': 'fruit_riddles',
  'place': 'place_riddles',
  'person': 'person_riddles',
  'tang_poetry': 'tang_poetry_riddles',
  'song_poetry': 'song_poetry_riddles',
  'flag': 'flag_riddles',
  'map_outline': 'map_outline_riddles',
  'car_logo': 'car_logo_riddles',
  'brand_logo': 'brand_logo_riddles',
  'movie_still': 'movie_still_riddles',
  'app_icon': 'app_icon_riddles',
  'audio_song': 'audio_song_riddles',
  'audio_animal': 'audio_animal_riddles',
  'text': 'word_riddles'
};

// 获取随机题目
exports.getRandomRiddles = async (type, count) => {
  console.log('请求题目类型:', type, '对应表名:', typeTableMap[type] || 'word_riddles');
  
  const table = typeTableMap[type] || 'idiom_riddles';
  const conn = await mysql.createConnection(dbConfig);
  try {
    const [rows] = await conn.execute(`SELECT *, '${type}' as riddle_type FROM \`${table}\` WHERE status=1 ORDER BY RAND() LIMIT ?`, [count]);
    return rows;
  } finally {
    await conn.end();
  }
}; 