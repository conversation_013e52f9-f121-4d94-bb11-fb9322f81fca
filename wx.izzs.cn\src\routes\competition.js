const express = require('express');
const router = express.Router();
const db = require('../utils/db');
const { validateOpenid } = require('../middleware/auth');

// 创建竞技模式游戏
router.post('/create', async (req, res) => {
  const { gameId, types, quantities, totalQuestions } = req.body;
  
  console.log('创建竞技模式游戏:', {
    gameId,
    types,
    quantities: typeof quantities === 'string' ? JSON.parse(quantities) : quantities,
    totalQuestions
  });
  
  if (!gameId || !types) {
    return res.json({ code: 1, message: '参数不完整' });
  }

  try {
    // 将游戏信息保存到数据库
    const [result] = await db.execute(
      `INSERT INTO competition_games 
       (game_id, types, quantities, total_questions, created_at) 
       VALUES (?, ?, ?, ?, NOW())
       ON DUPLICATE KEY UPDATE 
       types = VALUES(types),
       quantities = VALUES(quantities),
       total_questions = VALUES(total_questions)`,
      [gameId, types, JSON.stringify(typeof quantities === 'string' ? JSON.parse(quantities) : quantities), totalQuestions]
    );
    
    console.log('竞技模式游戏创建成功:', result);
    
    res.json({
      code: 0,
      data: {
        gameId
      }
    });
    
  } catch (err) {
    console.error('创建竞技模式游戏失败:', err);
    res.json({ 
      code: 1, 
      message: '创建游戏失败', 
      error: err.message 
    });
  }
});

// 获取竞技模式题目
router.post('/questions', async (req, res) => {
  const { gameId, types, quantities } = req.body;
  
  console.log('收到竞技模式题目请求:', {
    gameId,
    types,
    quantities: typeof quantities === 'string' ? JSON.parse(quantities) : quantities
  });
  
  if (!gameId || !types) {
    return res.json({ code: 1, message: '参数不完整' });
  }

  try {
    // 解析题目类型和数量
    const typeArray = types.split(',');
    const quantitiesObj = typeof quantities === 'string' ? JSON.parse(quantities) : quantities;
    
    // 存储所有题目
    const allQuestions = [];
    
    // 对每种类型获取指定数量的题目
    for (const type of typeArray) {
      // 检查该类型是否有指定数量
      const quantity = quantitiesObj[type] || 0;
      if (quantity <= 0) continue;
      
      console.log(`获取${type}类型题目${quantity}个`);
      
      // 确定查询的表名
      let tableName = 'riddles';
      if (type.startsWith('audio_')) {
        tableName = 'audio_riddles';
      } else if (type === 'flag' || type === 'map_outline' || type === 'car_logo' || type === 'brand_logo' || type === 'movie_still' || type === 'app_icon') {
        tableName = 'image_riddles';
      }
      
      // 查询指定数量的随机题目
      const [questions] = await db.execute(
        `SELECT * FROM ${tableName} 
         WHERE riddle_type = ? OR type = ?
         ORDER BY RAND() 
         LIMIT ?`,
        [type, type, quantity]
      );
      
      // 处理题目数据，统一格式
      const processedQuestions = questions.map(q => ({
        id: q.id,
        type: q.riddle_type || q.type,
        content: q.question || q.content,
        answer: q.answer,
        hint: q.hint || '',
        image_url: q.image_url || null,
        audio_url: q.audio_url || null,
        difficulty: q.difficulty || 1
      }));
      
      allQuestions.push(...processedQuestions);
    }
    
    // 将游戏信息保存到数据库
    const [result] = await db.execute(
      `INSERT INTO competition_games 
       (game_id, types, quantities, total_questions, created_at) 
       VALUES (?, ?, ?, ?, NOW())
       ON DUPLICATE KEY UPDATE 
       types = VALUES(types),
       quantities = VALUES(quantities),
       total_questions = VALUES(total_questions)`,
      [gameId, types, JSON.stringify(quantitiesObj), allQuestions.length]
    );
    
    console.log('竞技模式游戏创建成功:', result);
    
    // 随机打乱题目顺序
    allQuestions.sort(() => Math.random() - 0.5);
    
    res.json({
      code: 0,
      data: {
        gameId,
        questions: allQuestions,
        totalQuestions: allQuestions.length
      }
    });
    
  } catch (err) {
    console.error('获取竞技模式题目失败:', err);
    res.json({ 
      code: 1, 
      message: '获取题目失败', 
      error: err.message 
    });
  }
});

// 提交竞技模式成绩
router.post('/score', validateOpenid, async (req, res) => {
  const { gameId, openid, score, correctCount, totalQuestions, types, quantities } = req.body;
  
  console.log('收到竞技模式成绩提交:', {
    gameId,
    openid,
    score,
    correctCount,
    totalQuestions,
    types,
    quantities: typeof quantities === 'string' ? JSON.parse(quantities) : quantities
  });
  
  if (!gameId || !openid || score === undefined || correctCount === undefined || !totalQuestions) {
    return res.json({ code: 1, message: '参数不完整' });
  }

  const connection = await db.pool.getConnection();
  
  try {
    await connection.beginTransaction();
    
    // 计算正确率
    const accuracy = Math.round((correctCount / totalQuestions) * 100);
    
    // 检查用户是否已提交过该游戏的成绩
    const [existingScores] = await connection.execute(
      `SELECT * FROM competition_scores WHERE game_id = ? AND user_id = ?`,
      [gameId, openid]
    );
    
    if (existingScores.length > 0) {
      // 如果已存在，更新成绩（如果新成绩更高）
      if (score > existingScores[0].score) {
        await connection.execute(
          `UPDATE competition_scores 
           SET score = ?, correct_count = ?, accuracy = ?, updated_at = NOW()
           WHERE game_id = ? AND user_id = ?`,
          [score, correctCount, accuracy, gameId, openid]
        );
      }
    } else {
      // 如果不存在，插入新成绩
      await connection.execute(
        `INSERT INTO competition_scores 
         (game_id, user_id, score, correct_count, total_questions, accuracy, created_at) 
         VALUES (?, ?, ?, ?, ?, ?, NOW())`,
        [gameId, openid, score, correctCount, totalQuestions, accuracy]
      );
    }
    
    // 更新游戏参与人数
    await connection.execute(
      `UPDATE competition_games 
       SET participant_count = (
         SELECT COUNT(DISTINCT user_id) 
         FROM competition_scores 
         WHERE game_id = ?
       )
       WHERE game_id = ?`,
      [gameId, gameId]
    );
    
    await connection.commit();
    
    res.json({
      code: 0,
      data: {
        gameId,
        score,
        accuracy
      }
    });
    
  } catch (err) {
    await connection.rollback();
    console.error('提交竞技模式成绩失败:', err);
    res.json({ 
      code: 1, 
      message: '提交成绩失败', 
      error: err.message 
    });
  } finally {
    connection.release();
  }
});

// 获取竞技模式排行榜
router.get('/ranking', async (req, res) => {
  const { gameId } = req.query;
  const { openid } = req.query;
  
  console.log('获取竞技模式排行榜:', {
    gameId,
    openid
  });
  
  if (!gameId) {
    return res.json({ code: 1, message: '参数不完整' });
  }

  try {
    // 获取排行榜数据
    const [rankings] = await db.execute(
      `SELECT 
        cs.user_id,
        u.nickname,
        u.avatar_url,
        cs.score,
        cs.correct_count,
        cs.total_questions,
        cs.accuracy,
        RANK() OVER (ORDER BY cs.score DESC, cs.accuracy DESC) as rank
       FROM competition_scores cs
       JOIN users u ON cs.user_id = u.id
       WHERE cs.game_id = ?
       ORDER BY cs.score DESC, cs.accuracy DESC
       LIMIT 50`,
      [gameId]
    );
    
    // 处理排行榜数据
    const rankingList = rankings.map(item => ({
      userId: item.user_id,
      nickName: item.nickname,
      avatarUrl: item.avatar_url,
      score: item.score,
      correctCount: item.correct_count,
      totalQuestions: item.total_questions,
      accuracy: item.accuracy,
      rank: item.rank,
      isCurrentUser: item.user_id === openid
    }));
    
    // 如果当前用户不在前50名，获取用户排名
    let userRank = null;
    if (openid && !rankingList.some(item => item.userId === openid)) {
      const [userData] = await db.execute(
        `SELECT 
          cs.user_id,
          u.nickname,
          u.avatar_url,
          cs.score,
          cs.correct_count,
          cs.total_questions,
          cs.accuracy,
          (
            SELECT COUNT(*) + 1
            FROM competition_scores cs2
            WHERE cs2.game_id = ?
            AND (cs2.score > cs.score OR (cs2.score = cs.score AND cs2.accuracy > cs.accuracy))
          ) as rank
         FROM competition_scores cs
         JOIN users u ON cs.user_id = u.id
         WHERE cs.game_id = ? AND cs.user_id = ?`,
        [gameId, gameId, openid]
      );
      
      if (userData.length > 0) {
        userRank = {
          userId: userData[0].user_id,
          nickName: userData[0].nickname,
          avatarUrl: userData[0].avatar_url,
          score: userData[0].score,
          correctCount: userData[0].correct_count,
          totalQuestions: userData[0].total_questions,
          accuracy: userData[0].accuracy,
          rank: userData[0].rank,
          isCurrentUser: true
        };
        
        // 将用户排名添加到列表末尾
        rankingList.push(userRank);
      }
    }
    
    // 获取游戏信息
    const [gameInfo] = await db.execute(
      `SELECT * FROM competition_games WHERE game_id = ?`,
      [gameId]
    );
    
    res.json({
      code: 0,
      data: rankingList,
      gameInfo: gameInfo.length > 0 ? gameInfo[0] : null
    });
    
  } catch (err) {
    console.error('获取竞技模式排行榜失败:', err);
    res.json({ 
      code: 1, 
      message: '获取排行榜失败', 
      error: err.message 
    });
  }
});

module.exports = router; 