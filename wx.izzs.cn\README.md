# 猜谜游戏后端服务

## 简介
这是一个基于 Node.js + Express + MySQL 的猜谜游戏后端服务，支持微信小程序登录、用户管理、排行榜、个人统计等功能。

## 功能特性
- 🔐 微信小程序官方登录
- 👤 用户信息管理
- 🏆 排行榜系统
- 📊 个人数据统计
- 🎯 答题记录追踪
- 🏅 成就系统
- 🖼️ 智能头像处理

## 技术栈
- **后端**: Node.js + Express
- **数据库**: MySQL
- **认证**: 微信小程序登录
- **其他**: CORS、Helmet、Morgan
- **配置管理**: dotenv

## 快速开始

### 1. 环境要求
- Node.js >= 14.0.0
- MySQL >= 5.7
- 微信小程序账号

### 2. 安装依赖
```bash
npm install
```

### 3. 数据库配置
1. 创建 MySQL 数据库
```sql
CREATE DATABASE guessing_game CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行数据库初始化脚本
```bash
# 按顺序执行以下SQL文件
mysql -u root -p guessing_game < database/create_tables.sql
mysql -u root -p guessing_game < database/sample_data.sql
```

### 4. 环境变量配置
1. 复制环境变量文件
```bash
cp env.example .env
```

2. 编辑 `.env` 文件，填入真实配置
```bash
# 服务器配置
NODE_ENV=development
PORT=3003

# 数据库配置
DB_HOST=localhost
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=guessing_game
DB_PORT=3306
DB_CHARSET=utf8mb4

# 微信小程序配置
WX_APPID=your_miniprogram_appid
WX_APPSECRET=your_miniprogram_appsecret

# CORS 配置（可选）
ALLOWED_ORIGINS=https://your-domain.com,http://localhost:3003
```

### 5. 微信小程序配置
1. 在微信公众平台获取小程序的 AppID 和 AppSecret
2. 将 AppID 和 AppSecret 填入 `.env` 文件中的 `WX_APPID` 和 `WX_APPSECRET`
3. 在小程序后台配置服务器域名：
   - request合法域名：`https://your-domain.com`
   - 如果是开发环境，可在小程序开发工具中关闭域名校验

### 6. 启动服务
```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

服务将运行在配置的端口（默认3003）

## 环境变量说明

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| NODE_ENV | 否 | development | 运行环境 |
| PORT | 否 | 3003 | 服务器端口 |
| DB_HOST | 是 | localhost | 数据库主机地址 |
| DB_USER | 是 | root | 数据库用户名 |
| DB_PASSWORD | 是 | - | 数据库密码 |
| DB_NAME | 是 | guessing_game | 数据库名称 |
| DB_PORT | 否 | 3306 | 数据库端口 |
| DB_CHARSET | 否 | utf8mb4 | 数据库字符集 |
| WX_APPID | 是 | - | 微信小程序AppID |
| WX_APPSECRET | 是 | - | 微信小程序AppSecret |
| ALLOWED_ORIGINS | 否 | - | 允许的CORS源地址 |

## API 接口

### 认证相关
- `POST /api/auth/login` - 微信登录
- `GET /api/auth/verify` - 验证登录状态
- `POST /api/auth/refresh` - 刷新登录状态

### 用户相关
- `POST /api/user/save` - 保存用户信息
- `GET /api/user/stats` - 获取用户统计

### 排行榜
- `GET /api/ranking` - 获取排行榜

### 个人信息
- `GET /api/profile/*` - 获取个人信息

## 前端集成

### 1. 安装前端依赖
前端使用微信小程序原生开发，无需额外安装依赖。

### 2. 配置环境变量
编辑 `miniprogram/env.js`：
```javascript
module.exports = {
  API_BASE_URL: 'https://your-domain.com'  // 后端服务地址
}
```

### 3. 登录集成示例
```javascript
const authManager = require('../../utils/auth')

// 完整登录（登录 + 获取用户信息）
const result = await authManager.fullLogin()
if (result.success) {
  console.log('登录成功', result.userInfo)
} else {
  console.log('登录失败', result.error)
}

// 静默登录（仅获取openid）
const loginResult = await authManager.silentLogin()
if (loginResult.success) {
  console.log('静默登录成功', loginResult.openid)
}

// 检查登录状态
const status = authManager.checkLoginStatus()
if (status.isLoggedIn) {
  console.log('已登录', status.userInfo)
}
```

## 部署指南

### 1. 服务器要求
- Linux/Windows 服务器
- Node.js 运行环境
- MySQL 数据库
- 域名和 SSL 证书（微信小程序要求 HTTPS）

### 2. 生产环境部署
```bash
# 1. 克隆代码
git clone <repository-url>
cd guessing-game-backend

# 2. 安装依赖
npm install --production

# 3. 配置环境变量
cp env.example .env
# 编辑 .env 文件填入生产环境配置

# 4. 数据库初始化
mysql -u root -p guessing_game < database/create_tables.sql

# 5. 使用 PM2 管理进程
npm install -g pm2
pm2 start app.js --name guessing-game

# 6. 配置开机自启
pm2 startup
pm2 save
```

### 3. 环境变量安全
- ⚠️ **绝对不要** 将 `.env` 文件提交到版本控制系统
- 生产环境建议使用服务器的环境变量设置
- 定期轮换敏感信息如数据库密码和微信密钥

### 4. Nginx 配置示例
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:3003;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 数据库结构

### 主要表结构
- `users` - 用户基本信息
- `user_answers` - 用户答题记录
- `user_stats` - 用户统计数据
- `achievements` - 成就系统
- `user_achievements` - 用户成就关联
- `login_logs` - 登录日志

详细结构参见 `database/` 目录下的 SQL 文件。

## 开发指南

### 项目结构
```
wx.izzs.cn/
├── app.js              # 应用入口
├── package.json        # 依赖配置
├── env.example         # 环境变量示例
├── database/           # 数据库脚本
├── static/             # 静态资源
└── src/
    ├── routes/         # 路由定义
    ├── controllers/    # 控制器
    ├── services/       # 业务逻辑
    ├── utils/          # 工具函数
    └── config/         # 配置文件
```

### 添加新功能
1. 在 `src/routes/` 添加路由
2. 在 `src/controllers/` 添加控制器
3. 在 `src/services/` 添加业务逻辑
4. 在主路由文件中注册新路由

## 故障排除

### 常见问题

1. **微信登录失败**
   - 检查 `.env` 文件中的 `WX_APPID` 和 `WX_APPSECRET` 是否正确
   - 确认小程序已发布或在开发者工具中测试
   - 检查服务器域名配置

2. **数据库连接失败**
   - 检查 `.env` 文件中的数据库配置信息
   - 确认数据库服务正在运行
   - 检查防火墙设置

3. **环境变量未生效**
   - 确认 `.env` 文件位于项目根目录
   - 检查文件名是否正确（不是 `.env.txt`）
   - 重启应用服务

4. **头像显示问题**
   - 确认静态文件服务正常
   - 检查头像文件路径
   - 验证备用头像逻辑

### 日志查看
```bash
# PM2 日志
pm2 logs guessing-game

# 实时日志
pm2 logs guessing-game --lines 100 -f
```

## 贡献指南
1. Fork 项目
2. 创建功能分支
3. 提交变更
4. 发起 Pull Request

## 许可证
MIT License

## 支持
如有问题，请提交 Issue 或联系开发团队。 