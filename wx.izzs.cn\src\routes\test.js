const express = require('express');
const router = express.Router();
const { processUserAvatar, generateFallbackAvatar } = require('../utils/avatarHelper');

// 测试头像处理
router.get('/avatar', (req, res) => {
  const { userId } = req.query;
  
  if (!userId) {
    return res.json({
      code: 1,
      msg: '请提供用户ID'
    });
  }
  
  // 测试没有头像的用户
  const userWithoutAvatar = {
    id: userId,
    nickname: '测试用户',
    avatar_url: null
  };
  
  // 测试有头像的用户
  const userWithAvatar = {
    id: userId,
    nickname: '有头像用户',
    avatar_url: 'https://thirdwx.qlogo.cn/mmopen/vi_32/test.jpg'
  };
  
  // 测试相对路径头像的用户
  const userWithRelativeAvatar = {
    id: userId,
    nickname: '相对路径用户',
    avatar_url: '/static/test/avatar.jpg'
  };
  
  const processedUsers = [
    processUserAvatar(userWithoutAvatar),
    processUserAvatar(userWithAvatar),
    processUserAvatar(userWithRelativeAvatar)
  ];
  
  res.json({
    code: 0,
    data: {
      fallbackAvatar: generateFallbackAvatar(userId),
      processedUsers
    }
  });
});

module.exports = router; 