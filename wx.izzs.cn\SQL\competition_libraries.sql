-- 题库表
CREATE TABLE IF NOT EXISTS `comp_libraries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '题库名称',
  `creator_openid` varchar(100) NOT NULL COMMENT '创建者openid',
  `creator_name` varchar(50) NOT NULL COMMENT '创建者昵称',
  `types` varchar(255) NOT NULL COMMENT '题目类型，逗号分隔',
  `quantities` text NOT NULL COMMENT '每种类型的数量，JSON格式',
  `total_questions` int(11) NOT NULL DEFAULT 0 COMMENT '总题目数量',
  `participant_count` int(11) NOT NULL DEFAULT 0 COMMENT '参与人数',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `creator_openid` (`creator_openid`),
  KEY `created_at` (`created_at`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='竞技题库表';

-- 题库参与记录表
CREATE TABLE IF NOT EXISTS `comp_participants` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `library_id` int(11) NOT NULL COMMENT '题库ID',
  `user_openid` varchar(100) NOT NULL COMMENT '参与者openid',
  `user_name` varchar(50) NOT NULL COMMENT '参与者昵称',
  `score` int(11) NOT NULL DEFAULT 0 COMMENT '得分',
  `correct_count` int(11) NOT NULL DEFAULT 0 COMMENT '答对题数',
  `total_questions` int(11) NOT NULL DEFAULT 0 COMMENT '总题目数',
  `accuracy` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '正确率',
  `completion_time` int(11) NOT NULL DEFAULT 0 COMMENT '完成时间(秒)',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-进行中，2-已完成',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `library_user` (`library_id`, `user_openid`),
  KEY `library_id` (`library_id`),
  KEY `user_openid` (`user_openid`),
  KEY `score` (`score`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题库参与记录表';

-- 插入5条模拟数据
INSERT INTO `comp_libraries` (
  `name`, `creator_openid`, `creator_name`,
  `types`, `quantities`, `total_questions`, `participant_count`,
  `created_at`
) VALUES
(
  '综合挑战', 'openid_user1', '张三',
  'riddle,idiom,flag', '{"riddle":10,"idiom":15,"flag":5}', 30, 12,
  '2024-01-15 14:30:00'
),
(
  '文字专场', 'openid_user2', '李四',
  'riddle,idiom,xiehouyu', '{"riddle":20,"idiom":20,"xiehouyu":10}', 50, 8,
  '2024-01-14 16:20:00'
),
(
  '识图大赛', 'openid_user3', '王五',
  'flag,car_logo,brand_logo', '{"flag":15,"car_logo":10,"brand_logo":15}', 40, 25,
  '2024-01-13 10:15:00'
),
(
  '音频挑战', 'openid_user4', '赵六',
  'audio_song,audio_animal', '{"audio_song":20,"audio_animal":10}', 30, 6,
  '2024-01-12 09:45:00'
),
(
  '全能测试', 'openid_user5', '钱七',
  'riddle,idiom,flag,car_logo,audio_song', '{"riddle":10,"idiom":10,"flag":10,"car_logo":5,"audio_song":5}', 40, 18,
  '2024-01-11 20:30:00'
);

-- 插入一些参与记录数据
INSERT INTO `comp_participants` (
  `library_id`, `user_openid`, `user_name`,
  `score`, `correct_count`, `total_questions`, `accuracy`,
  `completion_time`, `status`, `created_at`, `completed_at`
) VALUES
(1, 'openid_player1', '小明', 85, 25, 30, 83.33, 450, 2, '2024-01-15 15:00:00', '2024-01-15 15:07:30'),
(1, 'openid_player2', '小红', 92, 28, 30, 93.33, 380, 2, '2024-01-15 15:10:00', '2024-01-15 15:16:20'),
(2, 'openid_player1', '小明', 78, 39, 50, 78.00, 720, 2, '2024-01-14 17:00:00', '2024-01-14 17:12:00'),
(3, 'openid_player3', '小刚', 95, 38, 40, 95.00, 520, 2, '2024-01-13 11:00:00', '2024-01-13 11:08:40'),
(5, 'openid_player2', '小红', 88, 35, 40, 87.50, 600, 2, '2024-01-11 21:00:00', '2024-01-11 21:10:00');
