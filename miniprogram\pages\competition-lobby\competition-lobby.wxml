<!-- 竞技大厅页面 -->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">同题竞技大厅</text>
    <text class="page-subtitle">选择或创建你的专属题库</text>
  </view>

  <!-- 题库列表区域 -->
  <view class="competitions-section">
    <view class="section-title">
      <text class="title-text">📚 题库列表</text>
      <text class="title-desc">所有用户创建的题库</text>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 题库列表 -->
    <view wx:else class="competitions-list">
      <view wx:for="{{userCompetitions}}" wx:key="id"
            class="competition-item {{selectedLibraryId === item.id ? 'selected' : ''}}"
            bindtap="onLibraryItemTap" data-id="{{item.id}}">

        <!-- 基本信息（始终显示） -->
        <view class="competition-basic">
          <view class="basic-row">
            <view class="library-name">{{item.title}}</view>
            <view class="creator-name">{{item.creator}}</view>
            <view class="library-stats">
              <text class="stat-item">{{item.totalQuestions}}题</text>
              <text class="stat-item">{{item.participants}}人</text>
            </view>
          </view>
        </view>

        <!-- 详细信息（展开时显示） -->
        <view wx:if="{{selectedLibraryId === item.id}}" class="competition-details">
          <view class="detail-section">
            <view class="section-title">题目类型</view>
            <view class="competition-types">
              <text wx:for="{{item.typesWithQuantity}}" wx:for-item="typeItem" wx:key="label"
                    class="type-tag"
                    style="background-color: {{typeItem.backgroundColor}};">{{typeItem.display}}</text>
            </view>
          </view>

          <view class="detail-section">
            <view class="section-title">详细信息</view>
            <view class="detail-info">
              <view class="detail-item">
                <text class="detail-label">创建时间:</text>
                <text class="detail-value">{{item.createTime}}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">参与人数:</text>
                <text class="detail-value">{{item.participants}}人</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">题目总数:</text>
                <text class="detail-value">{{item.totalQuestions}}题</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view wx:if="{{userCompetitions.length === 0}}" class="empty-state">
        <view class="empty-icon">📝</view>
        <text class="empty-text">暂无题库，快来创建第一个吧！</text>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <view class="action-btn primary-btn" bindtap="onCreateCompetition">
      <text class="btn-text">➕ 创建题库</text>
    </view>
    <view class="action-btn challenge-btn {{selectedLibraryId ? 'active' : 'disabled'}}" bindtap="onJoinChallenge">
      <text class="btn-text">🏆 加入挑战</text>
    </view>
  </view>

  <!-- 类型选择弹窗 -->
  <view wx:if="{{showTypePicker}}" class="type-picker-mask" catchtap="closeTypePicker">
    <view class="type-picker-popup competition-mode" catchtap="noop">
      <view class="type-picker-title">{{typePickerTitle}}</view>
      <view class="type-picker-list">
        <block wx:for="{{typePickerList}}" wx:for-item="typeItem" wx:for-index="typeIndex" wx:key="value">
          <view class="type-picker-item-competition {{typeItem.isSelected ? 'active' : ''}}">
            <view class="type-item-label">{{typeItem.label}}</view>
            <view class="type-item-quantity-selector">
              <view class="quantity-btn decrease" catchtap="decreaseTypeQuantity" data-value="{{typeItem.value}}">-</view>
              <view class="quantity-value">{{typeItem.quantity}}</view>
              <view class="quantity-btn increase" catchtap="increaseTypeQuantity" data-value="{{typeItem.value}}">+</view>
            </view>
          </view>
        </block>
      </view>

      <!-- 题库名称输入区域 -->
      <view class="competition-name-section">
        <view class="name-input-row">
          <view class="name-label">题库名称 <text class="required">*</text></view>
          <view class="name-input-container">
            <input
              class="name-input"
              placeholder="2～10字符，每人每天限创建1个"
              value="{{competitionName}}"
              bindinput="onCompetitionNameInput"
              maxlength="10"
            />
            <view class="name-counter">{{competitionName.length}}/10</view>
          </view>
        </view>
      </view>

      <view class="type-picker-divider"></view>
      <view class="type-picker-btns">
        <view class="type-picker-btn" catchtap="closeTypePicker">取消</view>
        <view class="type-picker-btn start" catchtap="onConfirmCreate">创建</view>
      </view>
    </view>
  </view>
</view>
