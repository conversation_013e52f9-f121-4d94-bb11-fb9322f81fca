/* 容器样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 96vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 背景图片样式 */
.background-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

/* 背景遮罩样式 */
.background-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  z-index: 1;
}

/* 确保所有内容在遮罩层之上 */
.user-info,
.header,
.card-container,
.type-picker-mask {
  position: relative;
  z-index: 2;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 24rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

/* 添加装饰性背景 */
.user-info::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 50%;
  transform: translate(30%, -30%);
  z-index: 0;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  margin-right: 30rpx;
  position: relative;
  z-index: 1;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: 4rpx solid #fff;
  transition: all 0.3s ease;
}

.user-avatar:active {
  transform: scale(0.95);
}

.user-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #e0e0e0, #f5f5f5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  font-size: 48rpx;
  color: #999;
}

.avatar-status {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  padding: 4rpx 0;
  text-align: center;
}

.status-text {
  font-size: 20rpx;
  color: #fff;
}

.user-stats {
  flex: 1;
  display: flex;
  justify-content: space-around;
  position: relative;
  z-index: 1;
  padding: 10rpx 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 20rpx;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 2rpx;
  background: linear-gradient(to bottom, transparent, #e0e0e0, transparent);
}

.stat-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.2;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.login-prompt {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
}

.login-desc {
  font-size: 30rpx;
  color: #999;
  text-align: center;
  line-height: 1.4;
}

/* 头部标题样式 */
.header {
  text-align: center;
  padding: 40rpx 0;
  margin-bottom: 30rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  text-shadow: 
    -1px -1px 0 #fff,
    1px -1px 0 #fff,
    -1px 1px 0 #fff,
    1px 1px 0 #fff,
    2px 2px 4px rgba(0, 0, 0, 0.1);
  display: block;
}

.subtitle {
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  margin-top: 10rpx;
  display: block;
  text-shadow: 
    -1px -1px 0 #fff,
    1px -1px 0 #fff,
    -1px 1px 0 #fff,
    1px 1px 0 #fff,
    2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* 卡片容器样式 */
.card-container {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 卡片样式 */
.card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.card-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.card-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
  display: block;
}

.card-arrow {
  font-size: 32rpx;
  color: #999;
}

/* 卡片类型样式 */
.text-card {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
}

.image-card {
  background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
}

.audio-card {
  background: linear-gradient(135deg, #f3e5f5, #e1bee7);
}

.competition-card {
  background: linear-gradient(135deg, #fff9c4, #ffecb3);
  border: 2rpx solid #ffd54f;
}

/* 底部导航样式 */
.footer {
  display: flex;
  justify-content: space-around;
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 30rpx;
}

.nav-icon {
  font-size: 40rpx;
  margin-bottom: 4rpx;
}

.nav-text {
  font-size: 24rpx;
  color: #666;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeIn 0.5s ease-out forwards;
}

.card:nth-child(2) {
  animation-delay: 0.1s;
}

.card:nth-child(3) {
  animation-delay: 0.2s;
}

/* 网格容器样式 */
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  padding: 20rpx;
}

/* 网格项样式 */
.grid-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.grid-item:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 图标样式 */
.grid-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
}

/* 文字样式 */
.grid-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
}

/* 响应式布局 */
@media screen and (min-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 为不同类型的谜题添加不同的背景色 */
.grid-item[data-type^="app_icon"],
.grid-item[data-type^="brand_logo"],
.grid-item[data-type^="game_icon"],
.grid-item[data-type^="flag"],
.grid-item[data-type^="silhouette"],
.grid-item[data-type^="landmark"] {
  background-color: #e3f2fd;
}

.grid-item[data-type^="idiom"],
.grid-item[data-type^="movie_quote"],
.grid-item[data-type^="person_name"],
.grid-item[data-type^="place_name"],
.grid-item[data-type^="riddle"],
.grid-item[data-type^="xiehouyu"] {
  background-color: #f3e5f5;
}

.grid-item[data-type^="music"],
.grid-item[data-type^="animal_sound"],
.grid-item[data-type^="movie_sound"] {
  background-color: #e8f5e9;
}

/* 类型选择弹窗样式 */
.type-picker-mask {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.4); /* 半透明遮罩 */
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.type-picker-popup {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx 20rpx 30rpx;
  min-width: 80vw;
  max-width: 80vw;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;
}

/* 竞技模式弹窗样式 */
.type-picker-popup.competition-mode {
  padding: 40rpx 0 20rpx 0; /* 移除左右内边距 */
}

/* 添加顶部装饰条 */
.type-picker-popup::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #4caf50, #45a049);
}

/* 竞技模式顶部装饰条 */
.type-picker-popup.competition-mode::before {
  background: linear-gradient(90deg, #ffd54f, #ffb300);
}

.type-picker-title {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  color: #333;
  text-align: center;
  letter-spacing: 2rpx;
  padding: 0 30rpx;
}
.type-picker-list {
  width: 100%;
  min-width: 320rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
}

/* 竞技模式列表样式 - 两列布局 */
.competition-mode .type-picker-list {
  padding: 10rpx 10rpx;
  justify-content: space-between;
}

.type-picker-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 18rpx 0;
  margin: 10rpx 2.5% 0 2.5%;
  font-size: 28rpx;
  color: #666;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 90rpx;
  width: max(28%, 90rpx);
  max-width: 28%;
  box-sizing: border-box;
  min-height: 40rpx;
  display: inline-block;
  border: 2rpx solid #e0e0e0;
  position: relative;
  overflow: hidden;
}

/* 竞技模式类型选择器样式 */
.type-picker-item-competition {
  background: #fff;
  border-radius: 12rpx;
  padding: 16rpx 12rpx;
  margin: 10rpx 2%;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
  width: 46%; /* 两列布局 */
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 2rpx solid #e0e0e0;
  position: relative;
  overflow: hidden;
}

.type-picker-item-competition.active {
  background: linear-gradient(135deg, #fff9c4, #ffecb3);
  border: 2rpx solid #ffd54f;
  box-shadow: 0 4rpx 12rpx rgba(255, 213, 79, 0.4);
}

.type-item-label {
  flex: 1;
  text-align: left;
  padding-left: 10rpx;
  font-size: 26rpx; /* 略微减小字体大小 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.type-item-quantity-selector {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: 6rpx;
}

.quantity-btn {
  width: 44rpx; /* 略微减小按钮大小 */
  height: 44rpx;
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  transition: all 0.2s ease;
}

.quantity-btn.decrease {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.quantity-btn.increase {
  background: #ffd54f;
  color: #fff;
  border: 1rpx solid #ffb300;
}

.quantity-btn:active {
  transform: scale(0.9);
}

.quantity-value {
  width: 40rpx; /* 略微减小数值宽度 */
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.type-picker-divider {
  width: 100%;
  height: 2rpx;
  background: #eee;
  margin: 24rpx 0 10rpx 0;
}

/* 按钮区样式 */
.type-picker-btns {
  display: flex;
  width: 100%;
  justify-content: space-between;
  margin-top: 0;
  gap: 20rpx;
  padding: 0 30rpx; /* 保留按钮区域的左右内边距 */
  box-sizing: border-box;
}

/* 添加选中动画 */
@keyframes selectBounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1.05);
  }
}
/* 选中时绿色背景按钮 */
.type-picker-item.active {
  background: linear-gradient(135deg, #4caf50, #45a049);
  color: #fff;
  font-weight: bold;
  border: 2rpx solid #388e3c;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.4);
  transform: scale(1.05);
  animation: selectBounce 0.3s ease-out;
}
.type-picker-item.active:active {
  background: linear-gradient(135deg, #388e3c, #2e7d32);
  transform: scale(1);
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}

/* 添加选中状态的勾选图标 */
.type-picker-item.active::before {
  content: '✓';
  position: absolute;
  right: 8rpx;
  top: 4rpx;
  font-size: 20rpx;
  font-weight: bold;
  color: #fff;
  background: rgba(0, 0, 0, 0.2);
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 添加点击波纹效果 */
.type-picker-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(76, 175, 80, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.4s, height 0.4s;
  pointer-events: none;
}
.type-picker-item:active::after {
  width: 120%;
  height: 120%;
}
.type-picker-item.active::after {
  background: rgba(255, 255, 255, 0.3);
}

.type-picker-item:active {
  background: #f5f5f5;
  transform: scale(0.95);
  border-color: #ccc;
}

/* 按钮区样式 */
.type-picker-btns {
  display: flex;
  width: 100%;
  justify-content: space-between;
  margin-top: 0;
  gap: 20rpx;
  padding: 0 30rpx; /* 保留按钮区域的左右内边距 */
  box-sizing: border-box;
}

.type-picker-btn {
  flex: 1;
  margin: 0 10rpx;
  background: #f5f5f5;
  color: #333;
  border-radius: 12rpx;
  font-size: 30rpx;
  text-align: center;
  padding: 24rpx 0;
  transition: all 0.2s ease;
  border: 2rpx solid transparent;
  font-weight: 500;
}

.type-picker-btn:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

.type-picker-btn.start {
  background: linear-gradient(135deg, #66bb6a, #4caf50);
  color: #fff;
  font-weight: bold;
  border: 2rpx solid #388e3c;
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);
  font-size: 32rpx;
  letter-spacing: 4rpx;
  position: relative;
  overflow: hidden;
}

.type-picker-btn.start::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.type-picker-btn.start:hover::before {
  left: 100%;
}

.type-picker-btn.start:active {
  background: linear-gradient(135deg, #388e3c, #2e7d32);
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}

/* 竞技模式下的开始按钮样式 */
.type-picker-popup.competition-mode .type-picker-btn.start {
  background: linear-gradient(135deg, #ffd54f, #ffb300);
  border: 2rpx solid #ffa000;
  box-shadow: 0 4rpx 16rpx rgba(255, 179, 0, 0.3);
} 