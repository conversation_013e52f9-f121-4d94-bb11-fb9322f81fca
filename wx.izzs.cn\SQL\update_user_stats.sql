DELIMITER //

DROP PROCEDURE IF EXISTS `update_user_stats` //

CREATE PROCEDURE `update_user_stats`(
    IN p_user_id VARCHAR(50),
    IN p_score INT,
    IN p_is_correct BOOLEAN
)
BEGIN
    DECLARE v_today_start TIMESTAMP;
    DECLARE v_week_start TIMESTAMP;
    DECLARE v_month_start TIMESTAMP;
    DECLARE v_now TIMESTAMP;
    DECLARE v_need_reset BOOLEAN;
    
    -- 获取当前时间
    SET v_now = NOW();
    
    -- 计算各个周期的开始时间
    SET v_today_start = DATE_FORMAT(v_now, '%Y-%m-%d 00:00:00');
    SET v_week_start = DATE_SUB(v_today_start, INTERVAL WEEKDAY(v_today_start) DAY);
    SET v_month_start = DATE_FORMAT(v_today_start, '%Y-%m-01 00:00:00');
    
    -- 检查是否需要进行全局重置（通过检查任意一个用户的任一周期更新时间是否过期）
    SELECT EXISTS(
        SELECT 1 FROM users 
        WHERE today_updated_at < v_today_start 
           OR week_updated_at < v_week_start 
           OR month_updated_at < v_month_start
        LIMIT 1
    ) INTO v_need_reset;
    
    -- 如果需要全局重置，则执行重置操作
    IF v_need_reset THEN

        -- === 每日数据重置 ===
        -- 重置今日数据大于0的过期记录，并更新时间戳
        UPDATE users 
        SET
            today_score = 0,
            today_answers = 0,
            today_correct_answers = 0,
            today_updated_at = v_now
        WHERE today_updated_at < v_today_start
          AND (today_score > 0 OR today_answers > 0 OR today_correct_answers > 0);

        -- 更新今日数据已为0的过期记录的时间戳，避免重复检查
        UPDATE users 
        SET
            today_updated_at = v_now
        WHERE today_updated_at < v_today_start
          AND today_score = 0 AND today_answers = 0 AND today_correct_answers = 0;


        -- === 每周数据重置 ===
        -- 重置本周数据大于0的过期记录，并更新时间戳
        UPDATE users 
        SET
            week_score = 0,
            week_answers = 0,
            week_correct_answers = 0,
            week_updated_at = v_now
        WHERE week_updated_at < v_week_start
          AND (week_score > 0 OR week_answers > 0 OR week_correct_answers > 0);

         -- 更新本周数据已为0的过期记录的时间戳
        UPDATE users 
        SET
            week_updated_at = v_now
        WHERE week_updated_at < v_week_start
          AND week_score = 0 AND week_answers = 0 AND week_correct_answers = 0;


        -- === 每月数据重置 ===
        -- 重置本月数据大于0的过期记录，并更新时间戳
        UPDATE users 
        SET
            month_score = 0,
            month_answers = 0,
            month_correct_answers = 0,
            month_updated_at = v_now
        WHERE month_updated_at < v_month_start
          AND (month_score > 0 OR month_answers > 0 OR month_correct_answers > 0);

        -- 更新本月数据已为0的过期记录的时间戳
        UPDATE users 
        SET
            month_updated_at = v_now
        WHERE month_updated_at < v_month_start
          AND month_score = 0 AND month_answers = 0 AND month_correct_answers = 0;

    END IF;
    
    -- 更新当前用户的统计数据
    UPDATE users 
    SET 
        -- 更新总统计数据（永久累计）
        total_score = total_score + p_score,
        total_answers = total_answers + 1,
        correct_answers = correct_answers + IF(p_is_correct, 1, 0),
        total_updated_at = v_now,
        
        -- 更新今日统计数据
        today_score = today_score + p_score,
        today_answers = today_answers + 1,
        today_correct_answers = today_correct_answers + IF(p_is_correct, 1, 0),
        today_updated_at = v_now,
        
        -- 更新本周统计数据
        week_score = week_score + p_score,
        week_answers = week_answers + 1,
        week_correct_answers = week_correct_answers + IF(p_is_correct, 1, 0),
        week_updated_at = v_now,
        
        -- 更新本月统计数据
        month_score = month_score + p_score,
        month_answers = month_answers + 1,
        month_correct_answers = month_correct_answers + IF(p_is_correct, 1, 0),
        month_updated_at = v_now,
        
        -- 更新最后修改时间
        updated_at = v_now
    WHERE id = p_user_id;
END //

DELIMITER ;
