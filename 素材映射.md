1. TabBar导航图标80x80px

assets/images/
├── home.png              // 首页未选中状态图标
├── home-active.png       // 首页选中状态图标
├── profile.png           // 我的未选中状态图标
└── profile-active.png    // 我的选中状态图标

2. 谜题类型图标120x120px
assets/images/
├── app-icon.png          // APP图标类谜题图标
├── brand-logo.png        // 品牌Logo类谜题图标
├── game-icon.png         // 游戏图标类谜题图标
├── flag.png             // 国家旗帜类谜题图标
├── silhouette.png       // 物品剪影类谜题图标
├── landmark.png         // 地标景点类谜题图标
├── idiom.png            // 成语猜谜类图标
├── movie.png            // 电影台词类图标
├── person.png           // 人名谜语类图标
├── place.png            // 地名谜语类图标
├── riddle.png           // 经典谜语类图标
├── xiehouyu.png         // 歇后语类图标
├── music.png            // 音乐片段类图标
├── animal.png           // 动物叫声类图标
└── movie-sound.png      // 电影原声类图标

3. 功能操作图标60x60px
assets/images/
├── audio-play.png       // 音频播放按钮图标
├── hint.png            // 提示按钮图标
├── share.png           // 分享按钮图标
└── placeholder.png     // 临时占位图标








易于使用、时尚的占位符
只需在我们的URL后添加您想要的图像大小（宽度和高度），您就会得到一个随机的图像。

https://picsum.photos/200/300
要获得方形图像，只需添加大小。

https://picsum.photos/200
特定图像
通过添加到 URL 的开头来获取特定图像。/id/{image}

https://picsum.photos/id/237/200/300
您可以在此处找到所有图像的列表。


静态随机图像
通过添加到 url 的开头，每次根据种子获取相同的随机图像。/seed/{seed}

https://picsum.photos/seed/picsum/200/300

灰度
通过附加到 URL 的末尾来获取灰度图像。?grayscale

https://picsum.photos/200/300?grayscale

模糊
通过在 URL 末尾追加来获取模糊图像。?blur

https://picsum.photos/200/300/?blur
您可以通过提供介于 和 之间的数字来调整模糊量。110

https://picsum.photos/200/300/?blur=2

高级用法
您可以组合上述任何选项。

例如，获取灰度和模糊的特定图像。

https://picsum.photos/id/870/200/300?grayscale&blur=2
要在浏览器中请求多个相同大小的图像，请添加 query param 以防止图像被缓存：random

<img src="https://picsum.photos/200/300?random=1">
<img src="https://picsum.photos/200/300?random=2">

如果需要文件结尾，可以在 url 的末尾添加。.jpg
https://picsum.photos/200/300.jpg

要获取 WebP 格式的图像，您可以在 url 的末尾添加。.webp
https://picsum.photos/200/300.webp


