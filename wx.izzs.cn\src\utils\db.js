const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'guess',
  password: process.env.DB_PASSWORD || '8NWCCBFfiK7ELf5M',
  database: process.env.DB_NAME || 'guess',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

console.log('数据库配置:', {
  host: dbConfig.host,
  user: dbConfig.user,
  database: dbConfig.database,
  port: dbConfig.port
});

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 监听连接池事件
pool.on('connection', (connection) => {
  console.log('数据库连接已创建');
});

pool.on('acquire', (connection) => {
  console.log('数据库连接已获取');
});

pool.on('release', (connection) => {
  console.log('数据库连接已释放');
});

pool.on('error', (err) => {
  console.error('数据库连接池错误:', err);
});

/**
 * 执行SQL查询
 * @param {string} sql - SQL语句
 * @param {Array} params - 查询参数
 * @returns {Promise<Array>} 查询结果
 */
const execute = async (sql, params = []) => {
  let connection;
  try {
    console.log('执行SQL查询:', {
      sql,
      params
    });
    connection = await pool.getConnection();
    const [results] = await connection.execute(sql, params);
    console.log('SQL查询成功:', {
      affectedRows: results.affectedRows,
      insertId: results.insertId
    });
    return results;
  } catch (err) {
    console.error('数据库查询失败:', {
      sql,
      params,
      error: {
        message: err.message,
        code: err.code,
        sqlMessage: err.sqlMessage,
        sqlState: err.sqlState
      }
    });
    throw err;
  } finally {
    if (connection) {
      connection.release();
    }
  }
};

/**
 * 开始事务
 * @returns {Promise<Object>} 事务对象
 */
const beginTransaction = async () => {
  console.log('开始数据库事务');
  const connection = await pool.getConnection();
  await connection.beginTransaction();
  return connection;
};

/**
 * 提交事务
 * @param {Object} connection - 事务连接对象
 */
const commit = async (connection) => {
  try {
    console.log('提交数据库事务');
    await connection.commit();
  } finally {
    connection.release();
  }
};

/**
 * 回滚事务
 * @param {Object} connection - 事务连接对象
 */
const rollback = async (connection) => {
  try {
    console.log('回滚数据库事务');
    await connection.rollback();
  } finally {
    connection.release();
  }
};

/**
 * 执行事务中的查询
 * @param {Object} connection - 事务连接对象
 * @param {string} sql - SQL语句
 * @param {Array} params - 查询参数
 * @returns {Promise<Array>} 查询结果
 */
const executeInTransaction = async (connection, sql, params = []) => {
  try {
    console.log('执行事务查询:', {
      sql,
      params
    });
    const [results] = await connection.execute(sql, params);
    console.log('事务查询成功:', {
      affectedRows: results.affectedRows,
      insertId: results.insertId
    });
    return results;
  } catch (err) {
    console.error('事务查询失败:', {
      sql,
      params,
      error: {
        message: err.message,
        code: err.code,
        sqlMessage: err.sqlMessage,
        sqlState: err.sqlState
      }
    });
    await rollback(connection);
    throw err;
  }
};

module.exports = {
  execute,
  beginTransaction,
  commit,
  rollback,
  executeInTransaction,
  pool
}; 