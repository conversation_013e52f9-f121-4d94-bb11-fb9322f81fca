const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');

// 获取APP图标列表
router.get('/', async (req, res) => {
  // 禁止缓存，强制每次都返回200
  res.setHeader('Cache-Control', 'no-store');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  console.log('收到获取APP图标列表请求');
  try {
    // 读取APP图标目录
    const appIconsDir = path.join(__dirname, '../../static/images/app');
    console.log('APP图标目录路径:', appIconsDir);
    
    const files = await fs.readdir(appIconsDir);
    console.log('读取到的文件列表:', files);
    
    // 过滤出PNG文件并提取APP名称
    const icons = files
      .filter(file => file.endsWith('.png'))
      .map(file => ({
        name: file.replace('.png', ''),
        url: `https://wx.izzs.cn/static/images/app/${file}`
      }));
    
    console.log('处理后的图标列表:', icons);
    
    res.json({
      success: true,
      icons
    });
  } catch (error) {
    console.error('获取APP图标列表失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router; 