# 猜谜小程序开发指南

## 一、项目概述

### 1.1 项目简介
这是一个轻量级的猜谜小程序，支持多种类型的谜题，包括图片、文字、音频等多种形式。项目采用前后端分离架构，前端使用微信小程序原生开发，后端采用Node.js + Express + MongoDB的技术栈。

### 1.2 核心功能
- 多种谜题类型支持（图片、文字、音频等）
- 答题系统（直接输入/字符选择）
- 积分/关卡系统
- 用户进度保存
- 提示系统
- 排行榜（可选）

## 二、技术架构

### 2.1 前端架构
```
miniprogram/
├── app.js                 # 小程序入口文件
├── app.json              # 全局配置
├── app.wxss             # 全局样式
├── components/          # 自定义组件
│   ├── riddle-card/    # 谜题卡片组件
│   ├── answer-input/   # 答案输入组件
│   └── hint-panel/     # 提示面板组件
├── pages/              # 页面文件
│   ├── index/         # 首页
│   ├── game/          # 游戏页面
│   ├── result/        # 结果页面
│   └── profile/       # 个人中心
├── utils/             # 工具函数
│   ├── request.js    # 网络请求
│   ├── storage.js    # 本地存储
│   └── validator.js  # 数据验证
└── assets/           # 静态资源
    ├── images/      # 图片资源
    └── audio/       # 音频资源
```

### 2.2 后端架构
```
wx.izzs.cn/
├── src/
│   ├── app.js           # 应用入口
│   ├── config/         # 配置文件
│   ├── controllers/    # 控制器
│   ├── models/        # 数据模型
│   ├── routes/        # 路由
│   ├── services/      # 业务逻辑
│   └── utils/         # 工具函数
├── tests/             # 测试文件
└── package.json       # 项目配置
```

## 三、谜题类型设计

### 3.1 基础类型
1. **图片类**
   - APP图标猜名称
   - 品牌Logo猜品牌
   - 游戏图标猜游戏名
   - 国家/地区旗帜猜名称
   - 物品/动物剪影猜名称
   - 地标/景点猜名称

2. **文字类**
   - 成语猜谜
   - 电影台词猜电影/角色
   - 地名/人名谜语
   - 经典谜语

3. **音频类**
   - 音乐片段猜歌曲/歌手
   - 动物叫声猜动物
   - 电影原声猜电影

### 3.2 数据结构
```json
{
  "id": "unique_id",
  "type": "image|text|audio",
  "category": "app_icon|brand_logo|riddle",
  "content": {
    "url": "https://example.com/image.jpg",
    "text": "谜面文字",
    "audio": "https://example.com/audio.mp3"
  },
  "answer": "正确答案",
  "hints": ["提示1", "提示2"],
  "options": ["选项1", "选项2", "选项3", "选项4"],
  "difficulty": 1-5,
  "points": 100,
  "tags": ["标签1", "标签2"]
}
```

## 四、开发流程

### 4.1 环境准备
1. 安装微信开发者工具
2. 安装Node.js和npm
3. 安装MongoDB
4. 配置开发环境变量

### 4.2 开发步骤
1. **前端开发**
   - 搭建小程序项目结构
   - 实现基础UI组件
   - 开发核心游戏逻辑
   - 集成后端API
   - 实现本地存储

2. **后端开发**
   - 搭建Express服务器
   - 设计数据库模型
   - 实现API接口
   - 编写业务逻辑
   - 添加数据验证

3. **测试与优化**
   - 单元测试
   - 集成测试
   - 性能优化
   - 用户体验优化

### 4.3 发布流程
1. 小程序审核准备
2. 后端服务部署
3. 提交小程序审核
4. 发布上线

## 五、素材获取建议

### 5.1 图片素材
#### 国内免费图库
- **千图网**
  - 网址：https://www.58pic.com/
  - 特点：素材丰富，分类清晰
  - 注意：部分素材需要付费

- **包图网**
  - 网址：https://ibaotu.com/
  - 特点：设计素材为主
  - 注意：需要注册会员

- **站酷网**
  - 网址：https://www.zcool.com.cn/
  - 特点：设计师社区，素材质量高
  - 注意：部分素材需要授权

#### 国内API服务
- **百度图片搜索API**
  ```javascript
  // 调用示例
  const axios = require('axios');
  
  async function searchImages(keyword) {
    const response = await axios.get('https://image.baidu.com/search/acjson', {
      params: {
        tn: 'resultjson_com',
        logid: '8032920601499532588',
        ipn: 'rj',
        ct: 201326592,
        fp: 'result',
        queryWord: keyword,
        cl: 2,
        lm: -1,
        ie: 'utf-8',
        oe: 'utf-8',
        st: -1,
        ic: 0,
        word: keyword,
        face: 0,
        istype: 2,
        nc: 1,
        pn: 0,
        rn: 30
      }
    });
    return response.data.data;
  }
  ```

- **阿里云图片搜索API**
  ```javascript
  // 调用示例
  const alicloud = require('@alicloud/pop-core');
  
  const client = new alicloud({
    accessKeyId: 'YOUR_ACCESS_KEY_ID',
    accessKeySecret: 'YOUR_ACCESS_KEY_SECRET',
    endpoint: 'https://imagesearch.cn-shanghai.aliyuncs.com',
    apiVersion: '2019-03-25'
  });
  
  async function searchImages(keyword) {
    const params = {
      RegionId: 'cn-shanghai',
      InstanceName: 'YOUR_INSTANCE_NAME',
      Keyword: keyword
    };
    
    const result = await client.request('SearchImage', params, {
      method: 'POST'
    });
    return result.Data;
  }
  ```

### 5.2 音频素材
#### 国内音频平台
- **5sing音乐**
  - 网址：http://5sing.kugou.com/
  - 特点：原创音乐为主
  - 注意：需要作者授权

- **网易云音乐**
  - 网址：https://music.163.com/
  - 特点：音乐资源丰富
  - 注意：需要遵守版权规定

#### 国内音频API
- **百度语音合成API**
  ```javascript
  // 调用示例
  const AipSpeechClient = require('baidu-aip-sdk').speech;
  
  const client = new AipSpeechClient(
    'YOUR_APP_ID',
    'YOUR_API_KEY',
    'YOUR_SECRET_KEY'
  );
  
  async function textToSpeech(text) {
    const result = await client.text2audio(text, {
      spd: 5, // 语速
      pit: 5, // 音调
      vol: 5, // 音量
      per: 0  // 发音人
    });
    return result.data;
  }
  ```

- **阿里云语音合成API**
  ```javascript
  // 调用示例
  const alicloud = require('@alicloud/pop-core');
  
  const client = new alicloud({
    accessKeyId: 'YOUR_ACCESS_KEY_ID',
    accessKeySecret: 'YOUR_ACCESS_KEY_SECRET',
    endpoint: 'https://nls-meta.cn-shanghai.aliyuncs.com',
    apiVersion: '2019-02-28'
  });
  
  async function textToSpeech(text) {
    const params = {
      Text: text,
      Format: 'mp3',
      SampleRate: 16000,
      Voice: 'xiaoyun',
      Volume: 50,
      SpeechRate: 0,
      PitchRate: 0
    };
    
    const result = await client.request('CreateTtsTask', params, {
      method: 'POST'
    });
    return result.TaskId;
  }
  ```

### 5.3 文字素材
#### 国内公开数据库
- **百度百科API**
  ```javascript
  // 调用示例
  const axios = require('axios');
  
  async function searchBaiduBaike(keyword) {
    const response = await axios.get('https://baike.baidu.com/api/openapi/BaikeLemmaCardApi', {
      params: {
        scope: 103,
        format: 'json',
        appid: 'YOUR_APP_ID',
        bk_key: keyword
      }
    });
    return response.data;
  }
  ```

- **豆瓣电影API**
  ```javascript
  // 调用示例
  const axios = require('axios');
  
  async function searchMovie(keyword) {
    const response = await axios.get('https://api.douban.com/v2/movie/search', {
      params: {
        q: keyword,
        apikey: 'YOUR_API_KEY'
      }
    });
    return response.data;
  }
  ```

- **汉典API**
  ```javascript
  // 调用示例
  const axios = require('axios');
  
  async function searchIdiom(keyword) {
    const response = await axios.get('https://www.zdic.net/hans/' + encodeURIComponent(keyword));
    // 需要解析返回的HTML内容
    return parseIdiomContent(response.data);
  }
  ```

#### 国内谜语资源
- **谜语大全**
  - 网址：https://www.miyu.com/
  - 特点：分类清晰，内容丰富
  - 注意：需要遵守版权

- **谜语网**
  - 网址：https://www.miyu5.com/
  - 特点：更新及时，互动性强
  - 注意：部分内容需要注册

### 5.4 数据整理工具
#### 国内协作工具
- **语雀**
  - 网址：https://www.yuque.com/
  - 特点：支持Markdown，适合文档协作
  - 注意：免费版有限制

- **腾讯文档**
  - 网址：https://docs.qq.com/
  - 特点：在线协作，支持多种格式
  - 注意：需要腾讯账号

#### 国内数据库服务
- **阿里云数据库**
  ```javascript
  // MongoDB连接示例
  const mongoose = require('mongoose');
  
  mongoose.connect('*******************************************************************', {
    useNewUrlParser: true,
    useUnifiedTopology: true
  });
  ```

- **腾讯云数据库**
  ```javascript
  // MongoDB连接示例
  const mongoose = require('mongoose');
  
  mongoose.connect('********************************************************************', {
    useNewUrlParser: true,
    useUnifiedTopology: true
  });
  ```

### 5.5 素材处理建议
1. **图片处理**
   - 统一尺寸（建议300x300）
   - 压缩优化（使用TinyPNG）
   - 格式转换（优先使用WebP）
   - 添加水印（如需）

2. **音频处理**
   - 统一格式（MP3/WAV）
   - 控制时长（建议≤10秒）
   - 压缩优化
   - 添加淡入淡出效果

3. **文字处理**
   - 统一长度（建议≤200字）
   - 去除特殊字符
   - 添加标点符号
   - 检查错别字

### 5.6 版权注意事项
1. **图片版权**
   - 优先使用CC0协议图片
   - 商业用途需购买授权
   - 注明图片来源
   - 避免使用未授权Logo

2. **音频版权**
   - 优先使用CC协议音频
   - 音乐片段需获得授权
   - 注明音频来源
   - 避免使用未授权音乐

3. **文字版权**
   - 优先使用公共领域内容
   - 引用需注明出处
   - 避免使用未授权内容
   - 注意商标权问题

## 六、性能优化建议

### 6.1 前端优化
- 图片懒加载
- 资源预加载
- 本地缓存策略
- 代码分包加载

### 6.2 后端优化
- 数据库索引优化
- 接口缓存
- 负载均衡
- CDN加速

## 七、安全建议

### 7.1 数据安全
- 敏感数据加密
- 用户数据脱敏
- 定期数据备份

### 7.2 接口安全
- 请求签名验证
- 频率限制
- 参数验证
- XSS防护

## 八、运营建议

### 8.1 内容运营
- 定期更新谜题
- 节日主题谜题
- 用户投稿机制
- 内容审核机制

### 8.2 用户运营
- 积分奖励机制
- 每日任务
- 社交分享
- 排行榜系统

## 九、开发时间规划

| 阶段 | 时间 | 主要任务 |
|------|------|----------|
| 需求分析 | 1-2天 | 确定功能范围和技术方案 |
| 环境搭建 | 1天 | 配置开发环境 |
| 前端开发 | 7-10天 | 实现核心功能 |
| 后端开发 | 5-7天 | 搭建服务器和API |
| 测试优化 | 3-5天 | 功能测试和性能优化 |
| 发布准备 | 2天 | 审核和部署 |

## 十、注意事项

1. 严格遵守微信小程序规范
2. 注意版权问题，避免使用未授权素材
3. 重视用户体验，保持界面简洁直观
4. 做好错误处理和异常情况处理
5. 注意数据安全和用户隐私保护
6. 保持代码可维护性和可扩展性

## 十一、后续规划

1. 增加更多谜题类型
2. 添加多人对战功能
3. 实现AR猜谜功能
4. 接入AI生成谜题
5. 开发管理后台
6. 添加数据分析功能

## 十二、参考资源

1. [微信小程序开发文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
2. [Node.js官方文档](https://nodejs.org/)
3. [MongoDB官方文档](https://docs.mongodb.com/)
4. [Express.js官方文档](https://expressjs.com/) 

## 十三、页面设计详细说明

### 13.1 首页设计
首页采用卡片式布局，主要展示三种谜题类型入口：

1. **布局结构**
   - 顶部：小程序标题和用户信息
   - 中部：三大类型卡片（文字、图片、音频）
   - 底部：排行榜入口、个人中心入口

2. **类型卡片设计**
   - 文字类卡片
     * 图标：📝
     * 标题：文字谜题
     * 副标题：字谜、诗词、名言等
     * 背景：浅蓝色渐变
   
   - 图片类卡片
     * 图标：🖼️
     * 标题：图片谜题
     * 副标题：国旗、Logo、剧照等
     * 背景：浅绿色渐变
   
   - 音频类卡片
     * 图标：🎵
     * 标题：音频谜题
     * 副标题：歌曲、动物叫声等
     * 背景：浅紫色渐变

3. **交互设计**
   - 卡片点击效果：轻微放大+阴影
   - 点击后统一跳转到答题页面
   - 答题页面根据类型参数动态渲染不同内容

### 13.2 答题页面设计
采用统一页面设计，通过参数控制不同类型内容的展示：

1. **页面布局**
   - 顶部：进度条、剩余时间
   - 中部：题目内容展示区
   - 底部：答题区域（输入框/选项按钮）

2. **答题模式**
   - 模式一：输入框模式
     * 显示答案字数提示
     * 实时输入验证
     * 提交按钮
   
   - 模式二：选项模式
     * 四个选项按钮
     * 选项随机排序
     * 选中效果

3. **通用功能**
   - 提示按钮
   - 跳过按钮
   - 返回按钮
   - 得分显示

## 十四、数据库设计

### 14.1 谜题主表(riddles)
```sql
CREATE TABLE riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    type ENUM('text', 'image', 'audio') NOT NULL COMMENT '谜题类型',
    sub_type VARCHAR(50) NOT NULL COMMENT '子类型(字谜/诗词/国旗等)',
    content TEXT NOT NULL COMMENT '谜题内容(文字/图片URL/音频URL)',
    answer_type VARCHAR(50) NOT NULL COMMENT '答案类型(动物/水果/地名等)',
    answer VARCHAR(255) NOT NULL COMMENT '正确答案',
    analysis TEXT COMMENT '解析说明',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项JSON数组(仅选项模式使用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    INDEX idx_type (type),
    INDEX idx_sub_type (sub_type),
    INDEX idx_answer_type (answer_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='谜题主表';
```

### 14.2 诗词谜题表(poetry_riddles)
```sql
CREATE TABLE poetry_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    riddle_id INT NOT NULL COMMENT '关联主表ID',
    dynasty VARCHAR(50) NOT NULL COMMENT '朝代',
    author VARCHAR(100) NOT NULL COMMENT '作者',
    title VARCHAR(255) NOT NULL COMMENT '诗词标题',
    content TEXT NOT NULL COMMENT '诗词内容',
    translation TEXT COMMENT '译文',
    background TEXT COMMENT '创作背景',
    tags JSON COMMENT '标签数组',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (riddle_id) REFERENCES riddles(id),
    INDEX idx_dynasty (dynasty),
    INDEX idx_author (author)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='诗词谜题表';
```

### 14.3 用户答题记录表(user_answers)
```sql
CREATE TABLE user_answers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    riddle_id INT NOT NULL COMMENT '谜题ID',
    user_answer VARCHAR(255) NOT NULL COMMENT '用户答案',
    is_correct TINYINT DEFAULT 0 COMMENT '是否正确',
    score INT DEFAULT 0 COMMENT '得分',
    time_used INT DEFAULT 0 COMMENT '用时(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (riddle_id) REFERENCES riddles(id),
    INDEX idx_user_id (user_id),
    INDEX idx_riddle_id (riddle_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户答题记录表';
```

### 14.4 示例数据
```sql
-- 文字类谜题示例
INSERT INTO riddles (type, sub_type, content, answer_type, answer, analysis, difficulty) VALUES
('text', '字谜', '远看山有色，近听水无声', '字', '画', '这是一道经典字谜，通过"山有色"和"水无声"的描述，暗示"画"字', 3),
('text', '成语', '画蛇添足', '成语', '多此一举', '画蛇时给蛇画上脚，比喻做了多余的事', 2),
('text', '唐诗', '床前明月光，疑是地上霜', '唐诗', '静夜思', '李白的名作，描写思乡之情', 1);

-- 图片类谜题示例
INSERT INTO riddles (type, sub_type, content, answer_type, answer, analysis, difficulty) VALUES
('image', '国旗', 'https://example.com/flags/china.png', '国家', '中国', '这是中国国旗，红色背景上有五颗黄色星星', 1),
('image', 'Logo', 'https://example.com/logos/apple.png', '品牌', '苹果', '这是苹果公司的标志，一个被咬了一口的苹果', 2);

-- 音频类谜题示例
INSERT INTO riddles (type, sub_type, content, answer_type, answer, analysis, difficulty) VALUES
('audio', '歌曲', 'https://example.com/audio/song1.mp3', '歌曲', '月亮代表我的心', '邓丽君的经典歌曲，旋律优美', 2),
('audio', '动物', 'https://example.com/audio/cat.mp3', '动物', '猫', '这是猫的叫声，喵喵喵', 1);

-- 诗词谜题示例
INSERT INTO poetry_riddles (riddle_id, dynasty, author, title, content, translation) VALUES
(3, '唐', '李白', '静夜思', '床前明月光，疑是地上霜。举头望明月，低头思故乡。', '床前明亮的月光，好像地上的霜。抬头望着明月，低头想起故乡。');
```





