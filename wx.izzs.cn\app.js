// 加载环境变量
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const path = require('path');

// 创建Express应用
const app = express();

// 中间件配置
app.use(cors({
  origin: ['https://wx.izzs.cn', 'http://wx.izzs.cn', 'http://localhost:3003', 'http://127.0.0.1:3003'],
  methods: ['GET', 'POST'],
  credentials: true
}));
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));
app.use(morgan('dev')); // 日志
app.use(express.json()); // JSON解析
app.use(express.urlencoded({ extended: true })); // URL编码解析

// 静态文件服务
app.use('/static', express.static(path.join(__dirname, 'static'), {
  setHeaders: (res, filePath) => {
    // 记录请求的文件路径
    console.log('静态文件请求:', filePath)
    
    // 设置正确的Content-Type
    if (filePath.endsWith('.png')) {
      res.set('Content-Type', 'image/png')
    } else if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
      res.set('Content-Type', 'image/jpeg')
    }
    
    // 记录文件是否存在
    const exists = require('fs').existsSync(filePath)
    console.log('文件是否存在:', exists, filePath)
    
    // 禁用缓存
    res.set('Cache-Control', 'no-store')
    res.set('Pragma', 'no-cache')
    res.set('Expires', '0')
  },
  // 添加错误处理
  fallthrough: false,
  // 添加文件系统错误处理
  onError: (err, req, res, next) => {
    console.error('静态文件服务错误:', err)
    console.error('请求路径:', req.path)
    console.error('原始URL:', req.originalUrl)
    res.status(404).send('文件未找到')
  }
}))

// 添加静态文件请求日志中间件
app.use('/static', (req, res, next) => {
  console.log('静态文件请求:', {
    path: req.path,
    originalUrl: req.originalUrl,
    headers: req.headers,
    query: req.query
  })
  next()
})

// 路由配置
app.use('/api', require('./src/routes'));

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: '服务器内部错误'
  });
});

// 启动服务器
const PORT = process.env.PORT || 3003;
app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
  console.log(`环境: ${process.env.NODE_ENV || 'development'}`);
}); 