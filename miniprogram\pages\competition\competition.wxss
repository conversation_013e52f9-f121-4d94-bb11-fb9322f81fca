.container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部信息 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(90deg,
    #00E676 0%,
    #00E676 var(--progress-percent, 0%),
    #fff var(--progress-percent, 0%),
    #fff 100%
  );
  border-radius: 10rpx;
  margin-bottom: 30rpx;
  transition: background 0.3s ease;
}

.score {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  text-shadow:
    -1px -1px 0 #fff,
    1px -1px 0 #fff,
    -1px 1px 0 #fff,
    1px 1px 0 #fff,
    2px 2px 4px rgba(0, 0, 0, 0.1);
}

.timer-display {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.3);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  min-width: 120rpx;
  text-shadow:
    -1px -1px 0 #fff,
    1px -1px 0 #fff,
    -1px 1px 0 #fff,
    1px 1px 0 #fff;
}

.progress {
  font-size: 28rpx;
  color: #666;
  text-shadow:
    -1px -1px 0 #fff,
    1px -1px 0 #fff,
    -1px 1px 0 #fff,
    1px 1px 0 #fff,
    2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* 题目区域 */
.question-area {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.question-area-fixed {
  background-color: #FFF;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  min-height: 450rpx;
  max-height: 450rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  box-sizing: border-box;
  position: relative;
}

/* 图片容器样式 */
.image-container {
  width: 100%;
  height: 320rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 40rpx 0 0 0;
}

.question-area-fixed .app-icon,
.question-area-fixed .question-text,
.question-area-fixed .question-audio {
  margin-bottom: 20rpx;
}

/* 音频类题目的特殊布局 */
.question-area-fixed .audio-container {
  margin-top: 40rpx; /* 为上一题答案和连胜计数留出空间 */
}

.question-area-fixed .audio-controls {
  margin-top: 5rpx;
  margin-bottom: 5rpx;
}

.question-area-fixed .app-icon {
  max-height: 320rpx;
  width: auto;
  min-width: 320rpx;
  border-radius: 20rpx;
  box-shadow: none;
  display: block;
  object-fit: contain;
  background: transparent;
}

.question-area-fixed .question-text {
  font-size: 36rpx;
  color: #333;
  text-align: center;
  word-break: break-all;
  max-width: 90%;
  width: 100%;
  padding: 20rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.question-area-fixed .question-audio {
  width: 90%;
  margin: 0 auto 20rpx auto;
}

.question-area-fixed .answer-length-tip {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.app-icon {
  width: 300rpx;
  height: 300rpx;
  margin: 0 auto 40rpx;
  display: block;
  border-radius: 20rpx;
  box-shadow: none;
  background: transparent;
}

.input-area {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.answer-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #fff;
  caret-color: transparent; /* 隐藏光标 */
}

.submit-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #07c160;
  color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;
}

.skip-btn {
  font-size: 28rpx;
  border-radius: 8rpx;
  width: 220rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s, color 0.3s;
  border: none;
}

/* 提高跳过按钮禁用状态的优先级 */
button.skip-btn.skip-btn-disabled {
  background-color: rgb(255, 255, 255) !important;
  color: #81c784 !important;
  cursor: not-allowed;
  opacity: 1 !important;  /* 覆盖通用禁用样式 */
}

.skip-btn-active {
  background-color: #07c160 !important;
  color: #fff !important;
  cursor: pointer;
}

.skip-btn-used {
  background-color: #e0e0e0 !important;
  color: #999 !important;
  cursor: not-allowed;
}

/* 修改通用按钮禁用样式，排除跳过按钮 */
button[disabled]:not(.skip-btn) {
  opacity: 0.6;
  background-color: #f5f5f5 !important;
  color: #999 !important;
}

.hint-btn {
  background-color: #ff9800;
  color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;
  width: 220rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

/* 结果显示 */
.result-area {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  text-align: center;
}

.result-text {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.result-text.correct {
  color: #07c160;
}

.result-text.wrong {
  color: #ff4d4f;
}

.correct-answer {
  font-size: 28rpx;
  color: #666;
}

.answer-length-tip {
  text-align: center;
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.hint-text {
  color: #ff9800;
  margin-left: 20rpx;
}

.input-box {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.input-cell {
  width: 60rpx;
  height: 80rpx;
  border: 2rpx solid #07c160;
  border-radius: 8rpx;
  font-size: 40rpx;
  text-align: center;
  background-color: #fff;
  caret-color: transparent; /* 隐藏光标 */
}

.btn-area {
  display: flex;
  justify-content: center;
  gap: 30rpx;
  margin-bottom: 30rpx;
}

.celebrate {
  position: fixed;
  left: 50%;
  top: 40%;
  transform: translate(-50%, -50%);
  z-index: 999;
  width: 200rpx;
  height: 200rpx;
  pointer-events: none;
  animation: popUp 0.5s;
}

@keyframes popUp {
  0% { opacity: 0; transform: translate(-50%, 0) scale(0.5);}
  60% { opacity: 1; transform: translate(-50%, -40rpx) scale(1.2);}
  100% { opacity: 1; transform: translate(-50%, -80rpx) scale(1);}
}

.sad-face {
  position: fixed;
  left: 50%;
  top: 40%;
  transform: translate(-50%, -50%);
  z-index: 999;
  width: 200rpx;
  height: 200rpx;
  pointer-events: none;
  animation: popUp 0.5s;
}

/* 多彩彩带绽放动画容器 */
.confetti-container {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  pointer-events: none;
  overflow: hidden;
}

/* 彩带粒子 */
.confetti-piece {
  position: absolute;
  opacity: 0.9;
  border-radius: 50%;
}

/* 彩带粒子动画 - 从中间向上喷射再落下 */
.confetti-piece.burst-1 {
  animation: confetti-burst-1 3.5s ease-out forwards;
}

.confetti-piece.burst-2 {
  animation: confetti-burst-2 3.5s ease-out forwards;
}

.confetti-piece.burst-3 {
  animation: confetti-burst-3 3.5s ease-out forwards;
}

.confetti-piece.burst-4 {
  animation: confetti-burst-4 3.5s ease-out forwards;
}

.confetti-piece.burst-5 {
  animation: confetti-burst-5 3.5s ease-out forwards;
}

.confetti-piece.burst-6 {
  animation: confetti-burst-6 3.5s ease-out forwards;
}

/* 彩带喷射动画关键帧 - 从中间向上喷射再落下 */
@keyframes confetti-burst-1 {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg) scale(1);
    opacity: 1;
  }
  30% {
    transform: translateY(-40vh) translateX(150rpx) rotate(180deg) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: translateY(120vh) translateX(300rpx) rotate(720deg) scale(0.5);
    opacity: 0;
  }
}

@keyframes confetti-burst-2 {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg) scale(1);
    opacity: 1;
  }
  35% {
    transform: translateY(-45vh) translateX(-180rpx) rotate(200deg) scale(1.3);
    opacity: 1;
  }
  100% {
    transform: translateY(120vh) translateX(-350rpx) rotate(800deg) scale(0.3);
    opacity: 0;
  }
}

@keyframes confetti-burst-3 {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg) scale(1);
    opacity: 1;
  }
  25% {
    transform: translateY(-35vh) translateX(80rpx) rotate(150deg) scale(1.4);
    opacity: 1;
  }
  100% {
    transform: translateY(120vh) translateX(200rpx) rotate(600deg) scale(0.4);
    opacity: 0;
  }
}

@keyframes confetti-burst-4 {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg) scale(1);
    opacity: 1;
  }
  40% {
    transform: translateY(-50vh) translateX(-120rpx) rotate(240deg) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: translateY(120vh) translateX(-280rpx) rotate(900deg) scale(0.6);
    opacity: 0;
  }
}

@keyframes confetti-burst-5 {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg) scale(1);
    opacity: 1;
  }
  20% {
    transform: translateY(-30vh) translateX(50rpx) rotate(120deg) scale(1.5);
    opacity: 1;
  }
  60% {
    transform: translateY(-10vh) translateX(100rpx) rotate(400deg) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: translateY(120vh) translateX(150rpx) rotate(720deg) scale(0.2);
    opacity: 0;
  }
}

@keyframes confetti-burst-6 {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg) scale(1);
    opacity: 1;
  }
  15% {
    transform: translateY(-25vh) translateX(-60rpx) rotate(100deg) scale(1.6);
    opacity: 1;
  }
  45% {
    transform: translateY(-15vh) translateX(-120rpx) rotate(300deg) scale(1.3);
    opacity: 1;
  }
  100% {
    transform: translateY(120vh) translateX(-200rpx) rotate(800deg) scale(0.1);
    opacity: 0;
  }
}

.input-box-single {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}
.input-single {
  position: absolute;
  left: 50%;
  top: 0;
  height: 60rpx;
  background: transparent;
  color: transparent;
  caret-color: transparent !important;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  z-index: 2;
  border: none;
  transform: translateX(-50%) translateX(24rpx);
  padding: 0;
  margin: 0;
  letter-spacing: 32rpx;
  font-family: 'Courier New', Courier, monospace;
  opacity: 0.01; /* 几乎完全透明但仍可接收输入 */
}
.input-cells {
  display: flex;
  justify-content: center;
  gap: 8rpx;
}
.input-cell-single {
  width: 48rpx;
  height: 60rpx;
  border: 2rpx solid #07c160;
  border-radius: 6rpx;
  font-size: 32rpx;
  text-align: center;
  background-color: #fff;
  line-height: 60rpx;
  box-sizing: border-box;
  font-family: 'Courier New', Courier, monospace;
  caret-color: transparent; /* 隐藏光标 */
}
.input-cell-single.active {
  border-color: #ff5252;
  animation: blink-border 1s steps(1) infinite;
}
@keyframes blink-border {
  0% { border-color: #ff5252; }
  50% { border-color: #fff; }
  100% { border-color: #ff5252; }
}

.answer-type-tip {
  font-size: 28rpx;
  color: #888;
  text-align: center;
  margin-top: auto;
  margin-bottom: 0;
  padding: 10rpx 0;
}

.center-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  text-align: center;
  flex: 1;
}

.audio-container {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.audio-play-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #1aad19; /* 微信绿 */
  margin-right: 20rpx;
}

.play-icon {
  width: 40rpx;
  height: 40rpx;
}

.audio-info {
  flex-grow: 1;
}

.audio-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.audio-artist {
  font-size: 24rpx;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.audio-duration {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

.audio-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.audio-controls {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}

.audio-progress-bar {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.progress-time {
  font-size: 24rpx;
  color: #666;
  padding: 0 10rpx;
}

.progress-bar-line {
  flex-grow: 1;
  height: 38rpx; /* 增加容器高度以容纳padding */
  background-color: transparent; /* 移除背景色 */
  position: relative;
  padding: 15rpx 0; /* 增加可点击区域 */
  margin: -15rpx 0; /* 抵消padding对布局的影响 */
  cursor: pointer;
  box-sizing: border-box;
}

/* 添加固定的灰色轨道条 */
.progress-bar-line::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 8rpx;
  background-color: #e0e0e0;
  border-radius: 4rpx;
  transform: translateY(-50%);
  z-index: 1;
}

.progress-line {
  position: absolute;
  top: 50%;
  left: 0;
  height: 8rpx !important; /* 与灰色轨道条完全一致的高度 */
  background-color: #1aad19 !important; /* 微信绿 */
  border-radius: 4rpx !important; /* 与灰色轨道条完全一致的圆角 */
  transform: translateY(-50%); /* 垂直居中 */
  z-index: 2; /* 确保绿色条在灰色轨道条上方 */
  box-sizing: border-box;
}

.progress-dot {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 20rpx;
  height: 20rpx;
  background-color: #1aad19; /* 微信绿 */
  border-radius: 50%;
  z-index: 1;
  transition: all 0.2s ease;
}

/* 拖拽时的进度点样式 */
.progress-dot.dragging {
  width: 28rpx;
  height: 28rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

.audio-play-btn-bottom {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

.audio-play-btn-bottom .play-icon {
  width: 100rpx;
  height: 100rpx;
}

/* 图片预览弹窗样式 */
.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.image-container-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: transparent;
}

/* 新增变换容器，专门处理变换效果 */
.image-transform-container {
  width: 100%;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-origin: center center;
  /* 简化CSS属性，减少性能开销 */
  will-change: transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.preview-image {
  width: 100%;
  height: auto;
  max-height: 100vh;
  display: block;
  /* 简化CSS属性，减少性能开销 */
  touch-action: none;
  -webkit-user-select: none;
  user-select: none;
}

/* 通用输入框光标隐藏 */
input, textarea {
  caret-color: transparent !important;
}

/* 微信小程序input组件光标隐藏 */
.wx-input, .wx-textarea {
  caret-color: transparent !important;
}

/* 上一题答案悬浮显示 */
.last-answer {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background-color: rgba(0, 0, 0, 0.4);
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  z-index: 10;
  max-width: 50%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 连对次数悬浮显示 */
.streak-counter {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: rgba(255, 152, 0, 0.8);
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  z-index: 10;
  font-weight: bold;
} 