// 引入环境变量
const env = require('../../env.js')
const app = getApp()

Page({
  data: {
    currentType: '', // 当前谜题类型
    competitionId: null, // 竞技题库ID
    competitionQuantities: {}, // 竞技题库各类型题目数量配置
    questionList: [], // 当前题目列表
    currentIndex: 0, // 当前题目索引
    currentQuestion: null, // 当前题目对象
    userInputArr: [], // 用户输入的字符数组（保留原样式）
    userInputStr: '', // 单一输入框内容
    showResult: false, // 是否显示结果
    isCorrect: false, // 是否答对
    correctAnswer: '', // 正确答案
    score: 0, // 当前得分
    totalCount: 0, // 总题数
    skipDisabled: true, // 跳过按钮是否禁用
    skipCountdown: 3, // 跳过倒计时
    defaultSkipCount: 5, // 默认每日跳过总次数
    skipCount: 0, // 剩余跳过次数，初始化为0，会在onLoad中根据今日数据更新
    defaultHintCount: 10, // 默认每日提示总次数
    hintCount: 0, // 剩余提示次数，初始化为0，会在onLoad中根据今日数据更新
    hintText: '', // 当前提示内容
    showCelebrate: false, // 是否显示庆祝动画
    confettiPieces: [], // 彩带粒子数据
    showSadFace: false, // 是否显示哭脸动画
    usedTime: 0, // 答题用时
    timer: null, // 计时器
    inputBoxWidth: 0, // 输入框宽度
    imageLoadRetry: 0, // 图片加载重试次数
    innerAudioContext: null, // 音频播放器实例
    isPlayingAudio: false, // 是否正在播放音频
    audioDuration: '00:00', // 音频总时长
    audioCurrentTime: '00:00', // 音频当前播放时间
    audioProgress: 0, // 音频播放进度百分比
    showImagePreview: false, // 是否显示图片预览弹窗
    imageScale: 1, // 图片缩放比例
    startDistance: 0, // 双指初始距离
    imageTranslateX: 0, // 图片X轴平移距离
    imageTranslateY: 0, // 图片Y轴平移距离
    lastTouchX: 0, // 上次触摸的X坐标
    lastTouchY: 0, // 上次触摸的Y坐标
    isDragging: false, // 是否正在拖动
    lastScaleTime: null, // 记录最后一次缩放时间
    scaleThrottleTimer: null, // 缩放节流定时器
    dragThrottleTimer: null, // 拖动节流定时器
    touchStartTime: 0, // 触摸开始时间
    touchStartX: 0, // 触摸开始X坐标
    touchStartY: 0, // 触摸开始Y坐标
    isTouchMoved: false, // 是否发生了触摸移动
    lastAnswer: '', // 上一题答案
    streak: 0, // 连对次数
    roundCorrectCount: 0, // 本轮答对题数
    roundTotalAnswered: 0, // 本轮已答题数
    progressPercent: 0, // 题目进度百分比
    isDraggingProgress: false, // 是否正在拖拽进度条
    progressBarRect: null, // 进度条位置信息

    // 竞技参与者数据（本地缓存）
    participantData: {
      library_id: null, // 题库ID
      user_openid: '', // 用户openid
      user_name: '', // 用户昵称
      score: 0, // 得分
      correct_count: 0, // 答对题数
      total_questions: 0, // 总题目数
      accuracy: 0.00, // 正确率
      completion_time: 0, // 完成时间(秒)
      status: 1, // 状态：1-进行中，2-已完成
      start_time: null, // 开始时间
      is_data_dirty: false // 数据是否需要同步到数据库
    },
  },

  // 题目类型映射表 - 基于数据库 riddle_type 字段值
  riddleTypeMap: {
    // 文字类题库
    'riddle': '字谜',
    'idiom': '成语',
    'xiehouyu': '歇后语',
    'animal': '动物名',
    'fruit': '水果名',
    'place': '地名',
    'person': '人名',
    'tang_poetry': '唐诗',
    'song_poetry': '宋词',
    
    // 图片类题库
    'flag': '国家或地区',
    'map_outline': '地区',
    'car_logo': '汽车品牌',
    'brand_logo': '品牌名',
    'movie_still': '电影名',
    'app_icon': '应用名称',
    
    // 音频类题库
    'audio_song': '歌曲名',
    'audio_animal': '动物名'
  },

  // 题目类型映射表 - 将数据库类型转换为友好提示（兼容字段）
  typeHintMap: {
    // 文字类
    'riddle': '字谜',
    'idiom': '成语', 
    'xiehouyu': '歇后语',
    'animal': '动物名',
    'fruit': '水果名',
    'place': '地名',
    'person': '人名',
    'tang_poetry': '唐诗',
    'song_poetry': '宋词',
    'country': '国家或地区',
    'city': '城市名',
    'movie': '电影名',
    'book': '书名',
    'brand': '品牌名',
    'celebrity': '名人',
    'historical_figure': '历史人物',
    'plant': '植物名',
    'color': '颜色',
    'profession': '职业',
    'sport': '运动项目',
    'festival': '节日',
    
    // 图片类
    'flag': '国家或地区',
    'map_outline': '地区',
    'car_logo': '汽车品牌',
    'brand_logo': '品牌',
    'movie_still': '电影',
    'app_icon': '应用',
    'landmark': '地标建筑',
    'food': '美食',
    'flower': '花朵',
    'architecture': '建筑',
    'painting': '画作',
    'cartoon': '动漫角色',
    'logo': '标志',
    'sign': '标识',
    
    // 音频类
    'audio_song': '歌曲名',
    'audio_animal': '动物名',
    'audio_instrument': '乐器名',
    'audio_nature': '自然声音',
    'audio_movie': '电影片段',
    'audio_tv': '电视剧片段'
  },

  // 基于表名的类型映射（根据实际数据库表名）
  tableTypeMap: {
    // 文字类题库
    'animal_riddles': '动物名',
    'fruit_riddles': '水果名',
    'idiom_riddles': '成语',
    'person_riddles': '人名',
    'place_riddles': '地名',
    'tang_poetry_riddles': '唐诗',
    'song_poetry_riddles': '宋词',
    'xiehouyu_riddles': '歇后语',
    'word_riddles': '字谜',
    
    // 图片类题库
    'app_icon_riddles': '应用名称',
    'brand_logo_riddles': '品牌名',
    'car_logo_riddles': '汽车品牌',
    'flag_riddles': '地区名',
    'map_outline_riddles': '地区名',
    'movie_still_riddles': '电影名',
    
    // 音频类题库
    'audio_animal_riddles': '动物名',
    'audio_song_riddles': '歌曲名',
    
    // 主表
    'riddles': '谜题'
  },

  // 更新进度条
  updateProgressBars() {
    const { currentIndex, totalCount } = this.data

    // 计算题目进度百分比
    const progressPercent = totalCount > 0 ? ((currentIndex + 1) / totalCount) * 100 : 0

    this.setData({
      progressPercent: Math.min(progressPercent, 100)
    })
  },

  // 获取友好的类型提示
  getTypeHint(question) {
    // 1. 优先使用 riddle_type 字段（数据库标准字段）
    if (question.riddle_type && this.riddleTypeMap[question.riddle_type]) {
      return this.riddleTypeMap[question.riddle_type]
    }
    
    // 2. 使用 answer_type 字段
    if (question.answer_type && this.typeHintMap[question.answer_type]) {
      return this.typeHintMap[question.answer_type]
    }
    
    // 3. 使用 type 字段
    if (question.type && this.typeHintMap[question.type]) {
      return this.typeHintMap[question.type]
    }
    
    // 4. 根据表名映射（主要逻辑）
    if (question.table_name && this.tableTypeMap[question.table_name]) {
      return this.tableTypeMap[question.table_name]
    }
    
    // 5. 根据来源映射
    if (question.source && this.tableTypeMap[question.source]) {
      return this.tableTypeMap[question.source]
    }
    
    // 6. 根据category映射
    if (question.category && this.typeHintMap[question.category]) {
      return this.typeHintMap[question.category]
    }
    
    // 7. 根据题目类型和内容智能推断
    if (question.type === 'image') {
      if (question.content && question.content.includes('flag')) {
        return '国家/地区名'
      }
      if (question.content && question.content.includes('app_icon')) {
        return '应用名称'
      }
      if (question.content && question.content.includes('car_logo')) {
        return '汽车品牌'
      }
      if (question.content && question.content.includes('brand_logo')) {
        return '品牌名'
      }
      if (question.content && question.content.includes('movie_still')) {
        return '电影名'
      }
      if (question.content && question.content.includes('map_outline')) {
        return '国家/地区名'
      }
      return '图片内容'
    }
    
    if (question.type === 'audio') {
      if (question.content && question.content.includes('song')) {
        return '歌曲名'
      }
      if (question.content && question.content.includes('animal')) {
        return '动物名'
      }
      return '音频内容'
    }
    
    // 8. 默认值
    return '谜题'
  },

  onLoad(options) {
    // 获取谜题类型参数，支持多个类型用逗号分隔
    const type = options.types || options.type || 'text'
    const competitionId = options.competitionId || null

    // 获取题库数量配置（优先从URL参数获取，其次从全局变量获取）
    let competitionQuantities = {}
    if (options.quantities) {
      try {
        competitionQuantities = JSON.parse(decodeURIComponent(options.quantities))
      } catch (e) {
        console.warn('解析URL中的quantities参数失败:', e)
      }
    }

    // 如果URL参数中没有quantities，尝试从全局变量获取
    if (Object.keys(competitionQuantities).length === 0 && app.globalData.competitionQuantities) {
      competitionQuantities = app.globalData.competitionQuantities
    }

    console.log('竞技页面初始化参数:', {
      types: type,
      competitionId: competitionId,
      competitionQuantities: competitionQuantities,
      urlOptions: options
    })

    // 初始化参与者数据（仅在竞技模式下）
    let participantData = this.data.participantData
    if (competitionId && app.globalData.openid) {
      participantData = {
        library_id: competitionId,
        user_openid: app.globalData.openid,
        user_name: app.globalData.userInfo?.nickName || app.globalData.userInfo?.nickname || '匿名用户',
        score: 0,
        correct_count: 0,
        total_questions: 0,
        accuracy: 0.00,
        completion_time: 0,
        status: 1, // 进行中
        start_time: new Date(),
        is_data_dirty: false
      }
    }

    this.setData({
      currentType: type,
      competitionId: competitionId,
      competitionQuantities: competitionQuantities,
      participantData: participantData,
      streak: 0, // 初始化连对次数为0
      lastAnswer: '', // 初始化上一题答案为空
      roundCorrectCount: 0, // 初始化本轮答对题数为0
      roundTotalAnswered: 0 // 初始化本轮已答题数为0
    })

    // 监听页面返回事件（通过重写页面的onUnload来处理）
    const originalOnUnload = this.onUnload
    this.onUnload = function() {
      // 如果图片预览正在显示，关闭预览
      if (this.data.showImagePreview) {
        this.setData({ showImagePreview: false })
      }
      // 调用原始的onUnload方法
      if (originalOnUnload) {
        originalOnUnload.call(this)
      }
    }

    // 初始化提示次数
    const today = new Date().toLocaleDateString()
    const hintData = wx.getStorageSync('hintData') || {}
    if (hintData.date !== today) {
      hintData.date = today
      hintData.count = this.data.defaultHintCount // 使用默认每日提示次数
      wx.setStorageSync('hintData', hintData)
    }
    this.setData({ hintCount: hintData.count })

    // 初始化跳过次数 (每日)
    const skipData = wx.getStorageSync('skipData') || {}
    if (skipData.date !== today) {
      skipData.date = today
      skipData.count = this.data.defaultSkipCount // 使用默认每日跳过次数
      wx.setStorageSync('skipData', skipData)
    }
    this.setData({ skipCount: skipData.count })

    // 获取题目列表
    this.getRiddleList(type)
  },

  // 初始化或重置音频播放器
  initAudioPlayer(src) {
    if (this.data.innerAudioContext) {
      this.data.innerAudioContext.destroy()
    }
    const innerAudioContext = wx.createInnerAudioContext()
    innerAudioContext.autoplay = true
    innerAudioContext.src = src
    
    // 监听事件
    innerAudioContext.onPlay(() => {
      this.setData({ isPlayingAudio: true })
    })
    innerAudioContext.onPause(() => {
      this.setData({ isPlayingAudio: false })
    })
    innerAudioContext.onStop(() => {
      this.setData({ isPlayingAudio: false, audioCurrentTime: '00:00' })
    })
    innerAudioContext.onEnded(() => {
      this.setData({ isPlayingAudio: false, audioCurrentTime: '00:00' })
    })
    innerAudioContext.onError((res) => {
      wx.showToast({
        title: '音频加载或播放失败',
        icon: 'none',
        duration: 2000
      })
      this.setData({ isPlayingAudio: false, audioCurrentTime: '00:00' })
    })
    innerAudioContext.onTimeUpdate(() => {
      const currentTime = this.formatTime(innerAudioContext.currentTime)
      const duration = this.formatTime(innerAudioContext.duration)
      const progress = (innerAudioContext.currentTime / innerAudioContext.duration) * 100
      this.setData({
        audioCurrentTime: currentTime,
        audioDuration: duration,
        audioProgress: progress
      })
    })
    innerAudioContext.onCanplay(() => {
      // 音频进入可播放状态，获取时长等信息
      const duration = this.formatTime(innerAudioContext.duration)
      this.setData({ audioDuration: duration })
    })

    this.setData({ innerAudioContext })
  },

  // 格式化时间
  formatTime(time) {
    const minute = Math.floor(time / 60)
    const second = Math.floor(time % 60)
    return `${minute < 10 ? '0' + minute : minute}:${second < 10 ? '0' + second : second}`
  },

  // 切换音频播放状态
  toggleAudioPlayback() {
    const innerAudioContext = this.data.innerAudioContext
    if (!innerAudioContext) return

    if (this.data.isPlayingAudio) {
      innerAudioContext.pause()
    } else {
      innerAudioContext.play()
    }
  },

  // 获取进度条位置信息
  getProgressBarRect() {
    return new Promise((resolve) => {
      const query = wx.createSelectorQuery().in(this)
      query.select('.progress-bar-line').boundingClientRect((rect) => {
        this.setData({ progressBarRect: rect })
        resolve(rect)
      }).exec()
    })
  },

  // 根据点击位置计算进度
  calculateProgress(clientX) {
    const rect = this.data.progressBarRect
    if (!rect) return 0

    const relativeX = clientX - rect.left
    const progress = Math.max(0, Math.min(100, (relativeX / rect.width) * 100))
    return progress
  },

  // 设置音频播放进度
  setAudioProgress(progress) {
    const innerAudioContext = this.data.innerAudioContext
    if (!innerAudioContext || !innerAudioContext.duration) return

    const targetTime = (progress / 100) * innerAudioContext.duration
    innerAudioContext.seek(targetTime)

    // 立即更新UI显示
    this.setData({
      audioProgress: progress,
      audioCurrentTime: this.formatTime(targetTime)
    })
  },

  // 进度条点击事件
  onProgressBarTap(e) {
    if (this.data.isDraggingProgress) return // 如果正在拖拽，忽略点击

    const clientX = e.detail.x
    this.getProgressBarRect().then(() => {
      const progress = this.calculateProgress(clientX)
      this.setAudioProgress(progress)
    })
  },

  // 进度条触摸开始
  onProgressTouchStart(e) {
    this.setData({ isDraggingProgress: true })
    this.getProgressBarRect()
  },

  // 进度条触摸移动
  onProgressTouchMove(e) {
    if (!this.data.isDraggingProgress) return

    const clientX = e.touches[0].clientX
    const progress = this.calculateProgress(clientX)

    // 实时更新进度显示
    this.setData({ audioProgress: progress })

    // 实时更新时间显示
    const innerAudioContext = this.data.innerAudioContext
    if (innerAudioContext && innerAudioContext.duration) {
      const targetTime = (progress / 100) * innerAudioContext.duration
      this.setData({
        audioCurrentTime: this.formatTime(targetTime)
      })
    }
  },

  // 进度条触摸结束
  onProgressTouchEnd(e) {
    if (!this.data.isDraggingProgress) return

    const clientX = e.changedTouches[0].clientX
    const progress = this.calculateProgress(clientX)

    // 设置最终播放位置
    this.setAudioProgress(progress)
    this.setData({ isDraggingProgress: false })
  },

  // 打乱数组顺序
  shuffleArray(array) {
    const newArray = [...array]
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]]
    }
    return newArray
  },

  // 从后端获取题目列表（支持多类型）
  getRiddleList(types) {
    // 解析类型字符串，支持多个类型
    const typeArray = types.split(',').filter(t => t.trim())

    // 显示加载提示
    wx.showLoading({
      title: '加载题目中...',
      mask: true
    })

    // 并发请求所有类型的题目
    const promises = typeArray.map(type => {
      return new Promise((resolve, reject) => {
        // 获取该类型的题目数量，优先使用题库配置的数量，默认为10
        const typeQuantity = this.data.competitionQuantities && this.data.competitionQuantities[type.trim()]
          ? this.data.competitionQuantities[type.trim()]
          : 10

        console.log(`正在加载类型 ${type.trim()} 的题目，数量: ${typeQuantity}`)

        wx.request({
          url: env.API_BASE_URL + '/api/riddle/random',
          method: 'GET',
          data: {
            type: type.trim(),
            count: typeQuantity  // 使用题库配置的数量
          },
          success: (res) => {
            if (res.data && res.data.code === 0) {
              // 处理返回的数据
              const questions = (res.data.data || []).map(q => {
                if (q.type === 'image' && q.content) {
                  q.content = q.content
                }
                return q
              })
              console.log(`类型 ${type.trim()} 实际加载了 ${questions.length} 道题目`)
              resolve(questions)
            } else {
              console.warn(`类型 ${type.trim()} 加载失败:`, res.data)
              resolve([])
            }
          },
          fail: (err) => {
            console.error(`类型 ${type.trim()} 请求失败:`, err)
            resolve([])
          }
        })
      })
    })
    
    // 等待所有请求完成
    Promise.all(promises).then(results => {
      // 合并所有题目
      let allQuestions = []
      results.forEach(questions => {
        allQuestions = allQuestions.concat(questions)
      })
      
      // 添加日志展示第一条数据
      if (allQuestions.length > 0) {
        console.log('加载的第一条题目数据:', JSON.stringify(allQuestions[0], null, 2))
      }
      
      // 检查是否有题目
      if (allQuestions.length === 0) {
        wx.hideLoading()
        wx.showModal({
          title: '暂无题目',
          content: '当前选择的类型暂时没有题目，请选择其他类型',
          showCancel: false,
          confirmText: '返回选择',
          success: () => {
            wx.navigateBack()
          }
        })
        return
      }
      
      // 为每道题目添加友好的类型提示，并处理图片路径
      const questionsWithHint = allQuestions.map(question => {
        // 处理图片类型题目的图片路径
        if (question.type === 'image' && question.content) {
          let finalPath = ''
          // 如果已经是完整URL，则不需要处理
          if (question.content.startsWith('http://') || question.content.startsWith('https://')) {
            finalPath = question.content
          } else if (question.content.startsWith('/static/')) {
            // 如果以/static/开头，直接拼接域名
            finalPath = `${env.API_BASE_URL}${question.content}`
          } else {
            // 对于国旗类型的图片，使用新的路由
            if (question.riddle_type === 'flag' || question.content.includes('flag/')) {
              // 从原始路径中提取文件名，保持原始中文
              const filename = decodeURIComponent(question.content.split('/').pop())
              // 构建URL时使用完整的路径
              finalPath = `${env.API_BASE_URL}/static/images/flag/${filename}`
            } else {
              // 其他类型图片保持原有逻辑
              finalPath = `${env.API_BASE_URL}/static/images/${question.content}`
            }
          }
          question.content = finalPath
        }
        // 处理音频类型题目的音频路径
        else if (question.type === 'audio' && question.content) {
          let finalPath = ''
          // 如果已经是完整URL，则不需要处理
          if (question.content.startsWith('http://') || question.content.startsWith('https://')) {
            finalPath = question.content
          } else if (question.content.startsWith('/static/')) {
            // 如果以/static/开头，直接拼接域名
            finalPath = `${env.API_BASE_URL}${question.content}`
          } else {
            // 解码原始内容，保持原始路径（包含子目录）
            const decodedContent = decodeURIComponent(question.content);
            // 构建URL时使用完整的路径，将 images 替换为 audio
            finalPath = `${env.API_BASE_URL}/static/audio/${decodedContent}`;
          }
          question.content = finalPath
        }
        return {
          ...question,
          answer_type: this.getTypeHint(question)
        }
      })
      
      // 打乱题目顺序
      const shuffledQuestions = this.shuffleArray(questionsWithHint)

      // 重置本轮统计数据（不重置跳过次数）
      this.resetRoundStats()

      // 初始化第一题
      const first = shuffledQuestions[0]
      const answerLen = first.answer.length
      
      // 如果是音频题目，初始化音频播放器
      if (first.type === 'audio' && first.content) {
        this.initAudioPlayer(first.content)
      }
      
      // 更新参与者数据的总题目数（仅在竞技模式下）
      let updatedParticipantData = this.data.participantData
      if (this.data.competitionId && updatedParticipantData.library_id) {
        updatedParticipantData = {
          ...updatedParticipantData,
          total_questions: shuffledQuestions.length,
          is_data_dirty: true
        }
      }

      this.setData({
        questionList: shuffledQuestions,
        currentIndex: 0,
        currentQuestion: first,
        totalCount: shuffledQuestions.length,
        participantData: updatedParticipantData,
        userInputArr: Array(answerLen).fill(''),
        userInputStr: '',
        inputBoxWidth: answerLen * 48 + (answerLen - 1) * 8,
        showResult: false,
        isCorrect: false,
        correctAnswer: '',
        score: 0,
        skipDisabled: true,
        skipCountdown: 3,
        hintText: '',
        usedTime: 0
      })
      
      wx.hideLoading()
      this.startSkipCountdown()
      this.startTimer()
      this.updateProgressBars() // 初始化进度条

      // 显示题库信息
      const typeCount = typeArray.length
      let loadingMessage = `已加载${shuffledQuestions.length}道题目`

      // 如果是竞技模式，显示详细的类型和数量信息
      if (this.data.competitionId && this.data.competitionQuantities) {
        const typeDetails = typeArray.map(type => {
          const quantity = this.data.competitionQuantities[type.trim()] || 0
          const actualLoaded = shuffledQuestions.filter(q => q.riddle_type === type.trim()).length
          return `${type.trim()}:${actualLoaded}/${quantity}`
        }).join(' ')
        loadingMessage = `竞技题库加载完成\n${loadingMessage}\n${typeDetails}`
      }

      wx.showToast({
        title: loadingMessage,
        icon: 'success',
        duration: 2500
      })
      
    }).catch(() => {
      wx.hideLoading()
      wx.showModal({
        title: '加载失败',
        content: '题目加载失败，请检查网络连接后重试',
        showCancel: false,
        confirmText: '返回重试',
        success: () => {
          wx.navigateBack()
        }
      })
    })
  },

  // 计时器开始
  startTimer() {
    if (this.data.timer) clearInterval(this.data.timer)
    this.setData({ usedTime: 0 })
    const timer = setInterval(() => {
      this.setData({ usedTime: this.data.usedTime + 1 })
    }, 1000)
    this.setData({ timer })
  },

  // 跳过倒计时
  startSkipCountdown() {
    this.setData({ skipDisabled: true, skipCountdown: 3 })
    let count = 3
    const timer = setInterval(() => {
      count--
      if (count <= 0) {
        clearInterval(timer)
        this.setData({ skipDisabled: false, skipCountdown: 0 })
      } else {
        this.setData({ skipCountdown: count })
      }
    }, 1000)
  },

  // 重置本轮统计数据（不重置跳过次数，跳过次数按每日计算）
  resetRoundStats() {
    this.setData({
      roundCorrectCount: 0, // 重置本轮答对题数
      roundTotalAnswered: 0 // 重置本轮已答题数
    });
  },

  // 单一输入框输入事件（保留原样式）
  onSingleInput(e) {
    let value = e.detail.value
    value = value.slice(0, this.data.currentQuestion.answer.length)
    this.setData({ userInputStr: value })
    // 输入满x个字符时自动验证
    if (value.length === this.data.currentQuestion.answer.length) {
      this.autoCheckAnswer(value)
    }
  },

  // 自动验证答案
  autoCheckAnswer(userAnswer) {
    const { currentQuestion, usedTime } = this.data

    const isCorrect = userAnswer.replace(/\s/g, '').toLowerCase() === currentQuestion.answer.replace(/\s/g, '').toLowerCase()
    const score = isCorrect ? currentQuestion.points : 0
    
    // 更新连对次数
    let newStreak = this.data.streak;
    if (isCorrect) {
      newStreak += 1;
    } else {
      newStreak = 0;
    }
    
    // 更新本轮统计数据
    const roundCorrectCount = isCorrect ? this.data.roundCorrectCount + 1 : this.data.roundCorrectCount;
    const roundTotalAnswered = this.data.roundTotalAnswered + 1;

    // 更新参与者数据（仅在竞技模式下）
    let updatedParticipantData = this.data.participantData
    if (this.data.competitionId && updatedParticipantData.library_id) {
      const newCorrectCount = isCorrect ? updatedParticipantData.correct_count + 1 : updatedParticipantData.correct_count
      const newScore = updatedParticipantData.score + score
      const answeredQuestions = roundTotalAnswered
      const newAccuracy = answeredQuestions > 0 ? (newCorrectCount / answeredQuestions * 100) : 0

      updatedParticipantData = {
        ...updatedParticipantData,
        score: newScore,
        correct_count: newCorrectCount,
        accuracy: parseFloat(newAccuracy.toFixed(2)),
        is_data_dirty: true
      }
    }

    this.setData({
      showResult: true,
      isCorrect,
      correctAnswer: currentQuestion.answer,
      streak: newStreak,
      roundCorrectCount: roundCorrectCount,
      roundTotalAnswered: roundTotalAnswered,
      participantData: updatedParticipantData
    })

    if (isCorrect) {
      this.setData({ score: this.data.score + score })
      this.showConfetti()
    } else {
      this.setData({ showSadFace: true })
      setTimeout(() => {
        this.setData({ showSadFace: false })
      }, 800)
    }

    // 更新进度条
    this.updateProgressBars()

    // 登录用户提交答题记录到后端
    if (app.globalData.openid) {
      // 获取题目来源表名
      let tableName = null
      if (currentQuestion.riddle_type) {
        // 根据riddle_type映射表名
        const typeToTable = {
          'riddle': 'word_riddles',
          'idiom': 'idiom_riddles',
          'xiehouyu': 'xiehouyu_riddles',
          'animal': 'animal_riddles',
          'fruit': 'fruit_riddles',
          'place': 'place_riddles',
          'person': 'person_riddles',
          'tang_poetry': 'tang_poetry_riddles',
          'song_poetry': 'song_poetry_riddles',
          'flag': 'flag_riddles',
          'map_outline': 'map_outline_riddles',
          'car_logo': 'car_logo_riddles',
          'brand_logo': 'brand_logo_riddles',
          'movie_still': 'movie_still_riddles',
          'app_icon': 'app_icon_riddles',
          'audio_song': 'audio_song_riddles',
          'audio_animal': 'audio_animal_riddles'
        }
        tableName = typeToTable[currentQuestion.riddle_type]
      }


      wx.request({
        url: env.API_BASE_URL + '/api/answer/submit',
        method: 'POST',
        data: {
          openid: app.globalData.openid,
          riddle_id: currentQuestion.id,
          table_name: tableName,
          user_answer: userAnswer,
          is_correct: isCorrect ? 1 : 0,
          score: score,
          time_used: usedTime
        },
        success: (res) => {
          if (res.data && res.data.code === 0) {
            // 更新本地用户数据
            const userStats = res.data.data.user_stats
            if (userStats) {
              // 更新全局用户数据
              if (app.globalData.userInfo) {
                app.globalData.userInfo = {
                  ...app.globalData.userInfo,
                  ...userStats
                }
              }
              
              // 显示实时统计提示
              // 移除实时统计提示，保持游戏流畅性
            }
          } else {
            console.error('答题记录提交失败:', res.data)
          }
        },
        fail: (err) => {
          console.error('提交答题记录请求失败:', err)
          // 失败时静默处理，不影响用户体验
        }
      })
    } else {
      console.warn('用户未登录，跳过提交答题记录')
    }

    // 每答完5题同步一次数据到数据库（防止数据丢失）
    if (this.data.competitionId && this.data.participantData.is_data_dirty &&
        this.data.roundTotalAnswered % 5 === 0) {
      this.syncParticipantDataToDatabase()
    }

    if (this.data.timer) clearInterval(this.data.timer)
    setTimeout(() => {
      this.nextQuestion()
    }, 1200)
  },

  // 下一题
  nextQuestion() {
    const { currentIndex, questionList } = this.data
    if (currentIndex + 1 < questionList.length) {
      const next = questionList[currentIndex + 1]
      
      // 保存当前题目的答案作为上一题答案
      const lastAnswer = this.data.currentQuestion ? this.data.currentQuestion.answer : '';
      
      // 如果是音频题目，初始化音频播放器
      if (next.type === 'audio' && next.content) {
        this.initAudioPlayer(next.content)
      } else {
        // 如果不是音频题目，销毁当前音频实例
        if (this.data.innerAudioContext) {
          this.data.innerAudioContext.destroy()
          this.setData({ innerAudioContext: null, isPlayingAudio: false, audioDuration: '00:00', audioCurrentTime: '00:00' })
        }
      }
      
      // 确保题目有友好的类型提示
      const nextWithHint = {
        ...next,
        answer_type: next.answer_type || this.getTypeHint(next)
      }
      
      // 重置图片加载重试次数
      this.setData({ imageLoadRetry: 0 })
      
      const answerLen = nextWithHint.answer.length
      this.setData({
        currentIndex: currentIndex + 1,
        currentQuestion: nextWithHint,
        userInputArr: Array(answerLen).fill(''),
        userInputStr: '',
        inputBoxWidth: answerLen * 48 + (answerLen - 1) * 8,
        showResult: false,
        isCorrect: false,
        correctAnswer: '',
        skipDisabled: true,
        skipCountdown: 3,
        hintText: '',
        usedTime: 0,
        lastAnswer: lastAnswer // 设置上一题答案
      })
      this.startSkipCountdown()
      this.startTimer()
      this.updateProgressBars() // 更新进度条
    } else {
      // 计算答对题数和正确率
      const correctCount = this.data.roundCorrectCount;
      const totalQuestions = this.data.roundTotalAnswered;
      const accuracy = totalQuestions > 0 ? Math.round((correctCount / totalQuestions) * 100) : 0;

      // 完成竞技，更新参与者数据状态（仅在竞技模式下）
      if (this.data.competitionId && this.data.participantData.library_id) {
        const completionTime = this.data.participantData.start_time
          ? Math.floor((new Date() - this.data.participantData.start_time) / 1000)
          : 0

        const finalParticipantData = {
          ...this.data.participantData,
          completion_time: completionTime,
          status: 2, // 已完成
          is_data_dirty: true
        }

        this.setData({ participantData: finalParticipantData })

        // 立即同步到数据库
        this.syncParticipantDataToDatabase()
      }

      // 获取用户最新统计数据
      if (app.globalData.openid && app.globalData.userInfo) {
        const userInfo = app.globalData.userInfo
        const totalAccuracy = userInfo.total_answers > 0 ? 
          Math.round((userInfo.correct_answers / userInfo.total_answers) * 100) : 0
        
        wx.showModal({
          title: '🎉 本轮结束',
          content: `📊 本轮成绩
答对：${correctCount} / ${totalQuestions} 题
正确率：${accuracy}%
得分：${this.data.score} 分

📈 总体数据
总积分：${userInfo.total_score} 分
总正确率：${totalAccuracy}%

📅 积分明细
今日：${userInfo.today_score} 分
本周：${userInfo.week_score} 分
本月：${userInfo.month_score} 分`,
          confirmText: '再来一轮',
          cancelText: '返回首页',
          success: (res) => {
            if (res.confirm) {
              // 重新开始相同类型的游戏
              this.getRiddleList(this.data.currentType)
            } else {
              // 返回首页
              wx.navigateBack()
            }
          }
        })
      } else {
        // 未登录用户显示简单统计
        wx.showModal({
          title: '🎉 本轮结束',
          content: `📊 本轮成绩
答对：${correctCount} / ${totalQuestions} 题
正确率：${accuracy}%
得分：${this.data.score} 分

💡 登录后可查看更多统计数据
包括总积分、历史记录等`,
          confirmText: '再来一轮',
          cancelText: '返回首页',
          success: (res) => {
            if (res.confirm) {
              this.getRiddleList(this.data.currentType)
            } else {
              wx.navigateBack()
            }
          }
        })
      }
    }
  },

  // 跳过当前题目
  skipQuestion() {
    if (this.data.skipDisabled || this.data.skipCount <= 0) {
      wx.showToast({ title: '今日跳过次数已用完', icon: 'none' })
      return
    }
    
    // 减少剩余跳过次数并更新本地存储
    const newSkipCount = this.data.skipCount - 1
    
    // 保存当前题目的答案作为上一题答案
    const lastAnswer = this.data.currentQuestion ? this.data.currentQuestion.answer : '';
    
    // 跳过的题目也计入总回答题目数据
    const roundTotalAnswered = this.data.roundTotalAnswered + 1;

    // 更新参与者数据（跳过题目不增加得分和答对数，但更新正确率）
    let updatedParticipantData = this.data.participantData
    if (this.data.competitionId && updatedParticipantData.library_id) {
      const answeredQuestions = roundTotalAnswered
      const newAccuracy = answeredQuestions > 0 ? (updatedParticipantData.correct_count / answeredQuestions * 100) : 0

      updatedParticipantData = {
        ...updatedParticipantData,
        accuracy: parseFloat(newAccuracy.toFixed(2)),
        is_data_dirty: true
      }
    }

    this.setData({
      skipCount: newSkipCount,
      lastAnswer: lastAnswer,
      roundTotalAnswered: roundTotalAnswered, // 更新总回答题目数据
      participantData: updatedParticipantData
      // 跳过题目不算答错，不重置连对次数
    })
    
    const today = new Date().toLocaleDateString()
    let skipData = wx.getStorageSync('skipData') || {}
    skipData.date = today
    skipData.count = newSkipCount
    wx.setStorageSync('skipData', skipData)
    
    this.nextQuestion()
  },

  // 显示提示
  showHint() {
    if (this.data.hintCount <= 0) {
      wx.showToast({ title: '今日提示次数已用完', icon: 'none' })
      return
    }
    const answer = this.data.currentQuestion.answer
    // 已经提示过的字符集合
    let revealedArr = this.data.hintText ? this.data.hintText.replace('包含：', '').split('、') : []
    let candidates = answer.split('').filter(c => !revealedArr.includes(c))
    if (candidates.length === 0) candidates = answer.split('')
    const randomChar = candidates[Math.floor(Math.random() * candidates.length)]
    // 累加提示字符
    if (revealedArr.length === 0) {
      revealedArr.push(randomChar)
    } else if (!revealedArr.includes(randomChar)) {
      revealedArr.push(randomChar)
    }
    const newHint = '包含：' + revealedArr.join('、')
    // 更新本地提示次数
    const today = new Date().toLocaleDateString()
    let hintData = wx.getStorageSync('hintData') || {}
    hintData.date = today
    hintData.count = (hintData.count || 3) - 1
    wx.setStorageSync('hintData', hintData)
    this.setData({
      hintText: newHint,
      hintCount: hintData.count
    })
  },

  // 生成彩带粒子数据
  generateConfettiPieces() {
    const pieces = [];
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
      '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
      '#FF9F43', '#10AC84', '#EE5A24', '#0984E3', '#A29BFE',
      '#FD79A8', '#FDCB6E', '#6C5CE7', '#00B894', '#E17055'
    ];

    // 生成80个彩带粒子，从页面中间区域开始
    for (let i = 0; i < 80; i++) {
      // 从页面中间区域（40%-60%）开始，向上喷射
      const centerLeft = 45 + Math.random() * 10; // 45%-55%的水平位置
      const centerTop = 45 + Math.random() * 10;  // 45%-55%的垂直位置

      // 三种不同大小的彩带
      let size;
      const sizeType = Math.random();
      if (sizeType < 0.3) {
        size = Math.random() * 6 + 4; // 小彩带 4-10rpx
      } else if (sizeType < 0.7) {
        size = Math.random() * 8 + 10; // 中彩带 10-18rpx
      } else {
        size = Math.random() * 10 + 18; // 大彩带 18-28rpx
      }

      pieces.push({
        id: i,
        left: centerLeft, // 从中间区域开始
        top: centerTop,   // 从中间区域开始
        color: colors[Math.floor(Math.random() * colors.length)], // 随机颜色
        size: size, // 三种大小差异
        animationType: Math.floor(Math.random() * 6) + 1, // 随机动画类型 1-6
        delay: Math.random() * 0.8 // 随机延迟 0-0.8秒
      });
    }

    return pieces;
  },

  // 多彩彩带绽放动画
  showConfetti() {
    // 生成彩带粒子数据
    const confettiPieces = this.generateConfettiPieces();

    // 立即显示动画
    this.setData({
      showCelebrate: true,
      confettiPieces: confettiPieces
    });

    // 3.5秒后隐藏动画（不阻塞其他操作）
    setTimeout(() => {
      this.setData({
        showCelebrate: false,
        confettiPieces: [] // 清空数据释放内存
      });
    }, 3500);
  },

  // 显示图片预览
  showImagePreview() {
    // 清除可能存在的节流定时器
    if (this.data.scaleThrottleTimer) {
      clearTimeout(this.data.scaleThrottleTimer);
    }
    
    this.setData({
      showImagePreview: true,
      imageScale: 1, // 重置缩放比例
      imageTranslateX: 0, // 重置X轴平移
      imageTranslateY: 0, // 重置Y轴平移
      isDragging: false, // 重置拖动状态
      lastScaleTime: null, // 重置缩放时间
      scaleThrottleTimer: null, // 重置节流定时器
      dragThrottleTimer: null, // 重置拖动节流定时器
    })
  },

  // 隐藏图片预览
  hideImagePreview(e) {
    // 如果正在拖动，则不关闭预览
    if (this.data.isDragging) {
      this.setData({ isDragging: false });
      return;
    }

    // 清除所有定时器
    if (this.data.scaleThrottleTimer) {
      clearTimeout(this.data.scaleThrottleTimer);
    }
    if (this.data.dragThrottleTimer) {
      clearTimeout(this.data.dragThrottleTimer);
    }

    this.setData({
      showImagePreview: false,
      scaleThrottleTimer: null,
      dragThrottleTimer: null
    });
  },



  // 处理触摸开始事件
  handleTouchStart(e) {
    // 小程序环境中不使用stopPropagation
    const touch = e.touches[0];

    // 记录触摸开始信息，用于点击检测
    this.setData({
      touchStartTime: Date.now(),
      touchStartX: touch.clientX,
      touchStartY: touch.clientY,
      isTouchMoved: false
    });

    if (e.touches.length === 2) {
      // 双指操作 - 缩放
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );

      this.setData({
        startDistance: distance,
        isDragging: false,
        lastScaleTime: Date.now() // 记录最后一次缩放时间
      });
    } else if (e.touches.length === 1 && this.data.imageScale > 1) {
      // 单指操作 - 拖动（仅当放大状态下）
      this.setData({
        lastTouchX: touch.clientX,
        lastTouchY: touch.clientY,
        isDragging: true
      });
    }
  },

  // 处理触摸移动事件
  handleTouchMove(e) {
    // 小程序环境中不使用stopPropagation

    // 检测是否发生了移动（用于区分点击和拖动）
    const touch = e.touches[0];
    const moveDistance = Math.sqrt(
      Math.pow(touch.clientX - this.data.touchStartX, 2) +
      Math.pow(touch.clientY - this.data.touchStartY, 2)
    );

    // 如果移动距离超过10px，标记为移动
    if (moveDistance > 10) {
      this.setData({ isTouchMoved: true });
    }

    if (e.touches.length === 2) {
      // 双指操作 - 缩放
      // 使用节流技术，防止过于频繁的状态更新
      if (this.data.scaleThrottleTimer) return;
      
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const currentDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      
      // 如果有初始距离，计算缩放比例
      if (this.data.startDistance > 0) {
        // 计算新的缩放比例
        let newScale = this.data.imageScale * (currentDistance / this.data.startDistance);
        
        // 限制缩放范围
        newScale = Math.min(Math.max(0.5, newScale), 3);
        
        // 使用更轻量的节流技术
        const throttleDelay = 16; // 降低到16ms (约60fps)，进一步提高响应性
        
        // 直接更新UI，不使用临时变量
        const scaleThrottleTimer = setTimeout(() => {
          this.setData({
            imageScale: newScale,
            startDistance: currentDistance,
            scaleThrottleTimer: null
          });
        }, throttleDelay);
        
        this.setData({
          scaleThrottleTimer: scaleThrottleTimer
        });
      }
    } else if (e.touches.length === 1 && this.data.isDragging) {
      // 单指操作 - 拖动
      // 使用节流技术，防止过于频繁的状态更新
      if (this.data.dragThrottleTimer) return;
      
      const touch = e.touches[0];
      const deltaX = touch.clientX - this.data.lastTouchX;
      const deltaY = touch.clientY - this.data.lastTouchY;
      
      // 获取屏幕宽度和高度
      const systemInfo = wx.getSystemInfoSync();
      const screenWidth = systemInfo.windowWidth;
      const screenHeight = systemInfo.windowHeight;
      
      // 计算图片在当前缩放下的尺寸
      // 假设图片原始宽度等于屏幕宽度，高度按比例计算
      const scaledImageWidth = screenWidth * this.data.imageScale;
      const scaledImageHeight = screenHeight * 0.5 * this.data.imageScale; // 假设图片高度为屏幕高度的50%
      
      // 计算图片边缘与视口边缘的距离
      // 当前平移值表示图片中心相对于视口中心的偏移
      // 需要计算图片边缘相对于视口边缘的距离
      
      // 图片左边缘到视口左边缘的距离 = 视口宽度/2 - 图片宽度/2 + 当前X平移
      const leftEdgeDistance = screenWidth/2 - scaledImageWidth/2 + this.data.imageTranslateX;
      // 图片右边缘到视口右边缘的距离 = 视口宽度/2 - 图片宽度/2 - 当前X平移
      const rightEdgeDistance = screenWidth/2 - scaledImageWidth/2 - this.data.imageTranslateX;
      // 图片上边缘到视口上边缘的距离 = 视口高度/2 - 图片高度/2 + 当前Y平移
      const topEdgeDistance = screenHeight/2 - scaledImageHeight/2 + this.data.imageTranslateY;
      // 图片下边缘到视口下边缘的距离 = 视口高度/2 - 图片高度/2 - 当前Y平移
      const bottomEdgeDistance = screenHeight/2 - scaledImageHeight/2 - this.data.imageTranslateY;
      
      // 计算新的平移距离
      let newTranslateX = this.data.imageTranslateX;
      let newTranslateY = this.data.imageTranslateY;
      
      // X方向拖动逻辑
      if (deltaX > 0) { // 向右拖动
        // 只有当左边缘还在视口左边缘之外时才允许向右拖动
        if (leftEdgeDistance < 0) {
          // 计算最大可右移距离，确保左边缘最多到达视口左边缘
          const maxRightMove = Math.min(deltaX, -leftEdgeDistance);
          newTranslateX = this.data.imageTranslateX + maxRightMove;
        }
      } else if (deltaX < 0) { // 向左拖动
        // 只有当右边缘还在视口右边缘之外时才允许向左拖动
        if (rightEdgeDistance < 0) {
          // 计算最大可左移距离，确保右边缘最多到达视口右边缘
          const maxLeftMove = Math.max(deltaX, rightEdgeDistance);
          newTranslateX = this.data.imageTranslateX + maxLeftMove;
        }
      }
      
      // Y方向拖动逻辑
      if (deltaY > 0) { // 向下拖动
        // 只有当上边缘还在视口上边缘之外时才允许向下拖动
        if (topEdgeDistance < 0) {
          // 计算最大可下移距离，确保上边缘最多到达视口上边缘
          const maxDownMove = Math.min(deltaY, -topEdgeDistance);
          newTranslateY = this.data.imageTranslateY + maxDownMove;
        }
      } else if (deltaY < 0) { // 向上拖动
        // 只有当下边缘还在视口下边缘之外时才允许向上拖动
        if (bottomEdgeDistance < 0) {
          // 计算最大可上移距离，确保下边缘最多到达视口下边缘
          const maxUpMove = Math.max(deltaY, bottomEdgeDistance);
          newTranslateY = this.data.imageTranslateY + maxUpMove;
        }
      }
      
      // 特殊情况：如果图片宽度小于等于视口宽度，则居中显示
      if (scaledImageWidth <= screenWidth) {
        newTranslateX = 0;
      }
      
      // 特殊情况：如果图片高度小于等于视口高度，则居中显示
      if (scaledImageHeight <= screenHeight) {
        newTranslateY = 0;
      }
      
      // 设置节流定时器，直接更新UI
      const dragThrottleDelay = 16; // 降低到16ms (约60fps)，进一步提高响应性
      const dragThrottleTimer = setTimeout(() => {
        this.setData({
          imageTranslateX: newTranslateX,
          imageTranslateY: newTranslateY,
          lastTouchX: touch.clientX,
          lastTouchY: touch.clientY,
          dragThrottleTimer: null
        });
      }, dragThrottleDelay);
      
      this.setData({
        dragThrottleTimer: dragThrottleTimer
      });
    }
  },

  // 处理触摸结束事件
  handleTouchEnd(e) {
    // 小程序环境中不使用stopPropagation

    // 检测是否为点击操作
    const touchEndTime = Date.now();
    const touchDuration = touchEndTime - this.data.touchStartTime;
    const isClick = !this.data.isTouchMoved && touchDuration < 300; // 300ms内且无移动视为点击

    // 清除缩放节流定时器
    if (this.data.scaleThrottleTimer) {
      clearTimeout(this.data.scaleThrottleTimer);
      this.setData({
        scaleThrottleTimer: null
      });
    }

    // 清除拖动节流定时器
    if (this.data.dragThrottleTimer) {
      clearTimeout(this.data.dragThrottleTimer);
      this.setData({
        dragThrottleTimer: null
      });
    }

    // 如果缩放回到1以下，重置平移
    if (this.data.imageScale <= 1) {
      this.setData({
        imageTranslateX: 0,
        imageTranslateY: 0
      });
    }

    // 重置初始距离
    if (e.touches.length < 2) {
      this.setData({
        startDistance: 0
      });
    }

    // 重置拖动状态
    this.setData({
      isDragging: false
    });

    // 如果是点击操作，关闭预览
    if (isClick) {
      this.hideImagePreview(e);
    }
  },
  
  // 阻止默认行为
  preventDefault(e) {
    // 小程序环境中不使用stopPropagation
    return false;
  },

  // 图片加载错误处理
  onImageError(e) {
    const { currentQuestion } = this.data
    
    // 如果重试次数小于3次，尝试重新加载
    if (this.data.imageLoadRetry < 3) {
      // 重新构建URL，确保使用原始中文文件名
      try {
        const urlParts = currentQuestion.content.split('/')
        const filename = decodeURIComponent(urlParts.pop())
        const baseUrl = urlParts.join('/')
        
        // 使用原始中文文件名重新构建URL，确保包含完整的路径
        const newUrl = `${baseUrl}/${filename}`
        
        this.setData({
          imageLoadRetry: this.data.imageLoadRetry + 1,
          'currentQuestion.content': newUrl
        })
      } catch (e) {
      }
      return
    }
    
    // 超过重试次数，显示错误提示
    wx.showToast({
      title: '图片加载失败，请重试',
      icon: 'none',
      duration: 2000
    })
    
    // 重置重试次数
    this.setData({ imageLoadRetry: 0 })
  },

  onShow() {
    // 页面显示时的处理
  },

  onHide() {
    // 页面隐藏时关闭图片预览
    if (this.data.showImagePreview) {
      this.setData({ showImagePreview: false })
    }
  },

  // 同步参与者数据到数据库
  async syncParticipantDataToDatabase() {
    const participantData = this.data.participantData

    // 检查是否需要同步
    if (!participantData.is_data_dirty || !participantData.library_id || !participantData.user_openid) {
      console.log('参与者数据无需同步或数据不完整')
      return
    }

    console.log('开始同步参与者数据到数据库:', participantData)

    try {
      const response = await this.updateParticipantData({
        library_id: participantData.library_id,
        user_openid: participantData.user_openid,
        user_name: participantData.user_name,
        score: participantData.score,
        correct_count: participantData.correct_count,
        total_questions: participantData.total_questions,
        accuracy: participantData.accuracy,
        completion_time: participantData.completion_time,
        status: participantData.status
      })

      if (response.code === 0) {
        console.log('参与者数据同步成功')
        // 标记数据已同步
        this.setData({
          'participantData.is_data_dirty': false
        })
      } else {
        console.error('参与者数据同步失败:', response.message)
      }
    } catch (error) {
      console.error('参与者数据同步请求失败:', error)
    }
  },

  // 调用更新参与者数据API
  updateParticipantData(data) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: env.API_BASE_URL + '/api/competition-participant/update',
        method: 'POST',
        data: data,
        header: {
          'Content-Type': 'application/json'
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(new Error(`HTTP ${res.statusCode}`))
          }
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  },

  onUnload() {
    // 页面卸载时同步参与者数据（如果有未同步的数据）
    if (this.data.participantData.is_data_dirty) {
      this.syncParticipantDataToDatabase()
    }

    if (this.data.timer) clearInterval(this.data.timer)
    // 销毁音频播放器实例
    if (this.data.innerAudioContext) {
      this.data.innerAudioContext.destroy()
      this.setData({ innerAudioContext: null })
    }
    // 页面卸载时关闭图片预览
    if (this.data.showImagePreview) {
      this.setData({ showImagePreview: false })
    }
  }
}) 