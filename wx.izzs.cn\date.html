<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库更新管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* 基础样式设置 */
        body {
            background-color: #f8f9fa; /* 柔和的背景色 */
        }
        .container {
            max-width: 1000px; /* 调整最大宽度，集中内容 */
            margin: 30px auto;
            min-width: 80vw;
            padding: 0 15px; /* 增加左右内边距 */
        }
        .card {
             margin-bottom: 30px; /* 增加卡片间距 */
             border: none; /* 移除默认边框 */
             box-shadow: 0 4px 8px rgba(0,0,0,0.05); /* 增加柔和阴影 */
        }
        .card-header {
             background-color: #007bff; /* 头部背景色 */
             color: white; /* 头部文字颜色 */
             font-size: 1.25rem; /* 头部字体大小 */
             font-weight: bold; /* 头部字体加粗 */
        }
         .card-body {
             padding: 20px; /* 卡片内边距 */
         }
        .status-message { margin-top: 10px; }
        .progress { display: none; margin-top: 10px; height: 25px; } /* 调整进度条高度 */
         .progress-bar { line-height: 25px; text-align: center; color: white; } /* 进度条文字居中 */

         /* 确保按钮不会太宽，输入列宽度相等 */
         .upload-form-row {
             display: flex; /* 使用flexbox保持元素在一行 */
             align-items: center; /* 垂直居中对齐元素 */
         }
         .upload-form-row > .col-auto {
             flex: 0 0 10%; /* 按钮占10%宽度 */
             display: flex; /* 使用flexbox居中按钮 */
             justify-content: center; /* 水平居中按钮 */
         }

        /* 调整输入容器的样式，使标签和输入框内联并控制宽度 */
        .upload-form-row > div:not(.col-auto) {
            display: flex; /* 使用flexbox对齐标签和输入框 */
            align-items: center; /* 垂直居中对齐 */
            flex: 0 0 35%; /* 每个输入区域占35%宽度 */
        }
        .upload-form-row > div:nth-child(1):not(.col-auto) { /* 第一个输入容器的特定边距 */
             margin-right: 5%; /* 两个输入区域之间5%的间距 */
        }

        .upload-form-row .form-label {
            margin-right: 0.5rem; /* 标签和输入框之间的间距 */
            margin-bottom: 0; /* 移除标签底部边距 */
            flex-shrink: 0; /* 防止标签缩小 */
        }
        .upload-form-row .form-select, .upload-form-row .form-control {
            flex-grow: 1; /* 允许输入框/选择框填充剩余空间 */
            width: auto; /* 允许宽度由flex-grow决定 */
            max-width: 100%; /* 确保不超过容器宽度 */
         }

        /* 优化表格文本换行 */
         .table td {
             word-break: break-word;
             vertical-align: middle;
         }
         .table th {
             text-align: center;
         }
         .card-header + .card-body {
             padding-top: 10px; /* 缩短头部与内容间距 */
         }

        .d-grid {
            display: grid;
        }
        .text-truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 2px 4px;
            border-radius: 3px;
            background-color: #f8f9fa;
        }
        .text-truncate:hover {
            background-color: #e9ecef;
            overflow: visible;
            white-space: normal;
            position: relative;
            z-index: 1;
        }

        /* CSV映射模态框样式 */
        #mappingModal .modal-header {
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        #mappingModal .modal-title {
            flex-grow: 1; /* 允许标题占据空间 */
            text-align: center; /* 在弹性项目内居中文本 */
        }
        #mappingModal .status-dot {
            font-size: 2.5em; /* 放大圆点 */
            font-weight: bold;
            margin: 0 5px; /* 添加水平间距 */
        }
        #mappingModal .status-dot.text-success {
            color: #28a745; /* 高饱和绿色 */
        }
        #mappingModal .status-dot.text-danger {
            color: #dc3545; /* 高饱和红色 */
        }
        #mappingModal .modal-footer {
            justify-content: center; /* 居中按钮 */
            gap: 15px; /* 按钮之间添加间距 */
        }

        /* 增加映射模态框宽度 */
        #mappingModal .modal-dialog {
            max-width: 80vw; /* 设置最大宽度 */
            width: 80vw; /* 设置宽度 */
            margin: auto; /* 水平居中 */
        }

        /* 垂直居中模态框 */
        #mappingModal.modal .modal-dialog {
            transform: translate(0, -50%);
            top: 50%;
            margin: auto;
        }

        /* 居中表格单元格内容 */
        #mappingModal .table td,
        #mappingModal .table th {
            vertical-align: middle;
            text-align: center;
            padding: 8px;
        }

        /* 说明文本样式 */
        #mappingModal .alert-info.mt-3 {
            display: flex;
            align-items: center;
            padding: 10px 20px; /* 调整内边距 */
        }
        #mappingModal .alert-info.mt-3 h6 {
            margin-right: 10px;
            margin-bottom: 0; /* 移除底部边距 */
            flex-shrink: 0; /* 防止缩小 */
        }
        #mappingModal .alert-info.mt-3 ul {
            list-style: none; /* 移除列表项目符号 */
            padding: 0;
            margin: 0;
            display: inline-block; /* 将列表显示为内联块 */
        }
        #mappingModal .alert-info.mt-3 li {
            display: inline; /* 内联显示列表项 */
            margin-right: 15px; /* 项目之间添加间距 */
        }
        #mappingModal .alert-info.mt-3 li:last-child {
            margin-right: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="mt-3 mb-3 text-center">数据库更新管理系统</h2>
        
        <div class="card">
            <div class="card-header">选择数据表并上传CSV文件</div>
            <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="row align-items-end mb-2 upload-form-row">
                         <div> <!-- 表格选择容器 -->
                              <label for="tableSelect" class="form-label">选择数据表：</label>
                              <!-- 使用下拉选择框 -->
                              <select class="form-select" id="tableSelect" required>
                                  <option value="">-- 请选择数据表 --</option>
                                  <!-- 选项将通过JavaScript动态填充 -->
                              </select>
                          </div>
                        <div> <!-- 文件输入容器 -->
                             <label for="csvFile" class="form-label">选择CSV文件：</label>
                             <input type="file" class="form-control" id="csvFile" accept=".csv" required>
                         </div>
                          <div class="col-auto">
                              <button type="submit" class="btn btn-primary">上传</button>
                          </div>
                    </div>

                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                    </div>
                    <div class="status-message alert" style="display: none;"></div>
                </form>
            </div>
        </div>

        <div class="card table-list">
            <div class="card-header" id="tableListHeader">数据表列表</div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead id="tableHead">
                            <!-- 表头将通过JavaScript动态加载 -->
                        </thead>
                        <tbody id="tableBody">
                            <!-- 表格数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 移除表格选择模态框 -->

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取DOM元素
            const uploadForm = document.getElementById('uploadForm');
            const progressBar = document.querySelector('.progress');
            const progressBarInner = document.querySelector('.progress-bar');
            const statusMessage = document.querySelector('.status-message');
            const tableSelect = document.getElementById('tableSelect'); // 获取选择下拉框
            const tableHead = document.getElementById('tableHead');
            const tableBody = document.getElementById('tableBody');
            const csvFile = document.getElementById('csvFile'); 
            const tableListHeader = document.getElementById('tableListHeader');
            
            let allTableStatusData = {};
            // selectedTable现在由选择下拉框的值控制

             // 格式化时间戳为 YYYY-MM-DD hh:mm:ss
             function formatTimestamp(timestamp) {
                 if (!timestamp) return '';
                 // 处理可能的日期对象或字符串表示
                 const date = (timestamp instanceof Date) ? timestamp : new Date(timestamp);
                 if (isNaN(date.getTime())) return timestamp; // 如果无效日期则返回原始值

                 const year = date.getFullYear();
                 const month = ('0' + (date.getMonth() + 1)).slice(-2);
                 const day = ('0' + date.getDate()).slice(-2);
                 const hours = ('0' + date.getHours()).slice(-2);
                 const minutes = ('0' + date.getMinutes()).slice(-2);
                 const seconds = ('0' + date.getSeconds()).slice(-2);
                 return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
             }

             // 生成随机浅色（保留函数以备后用）
             function getRandomLightColor() {
                 const r = Math.floor(Math.random() * 106 + 150); // 150-255
                 const g = Math.floor(Math.random() * 106 + 150); // 150-255
                 const b = Math.floor(Math.random() * 106 + 150); // 150-255
                 return `rgb(${r},${g},${b})`;
             }

            // 获取特殊字段注释
            function getSpecialComment(colName) {
                const specialComments = {
                    'id': '序号',
                    'created_at': '创建时间',
                    'updated_at': '更新时间'
                };
                return specialComments[colName] || '';
            }

            // 获取字段注释（用于数据表列表和单个表数据）
            function getColumnComment(col) {
                 const specialComment = getSpecialComment(col.name);
                 return col.comment || specialComment || '';
             }

            // 加载所有表列表并填充下拉菜单
            function loadAllTables() {
                fetch('/api/database/tables/status')
                    .then(response => response.json())
                    .then(data => {
                         if (data.success) {
                             allTableStatusData = data.data;
                            // 填充选择下拉框
                            populateTableSelect(allTableStatusData);
                            // 初始显示所有表的汇总信息
                            renderAllTablesSummary(allTableStatusData);
                         } else {
                            showStatus('获取数据表列表失败：' + data.message, 'danger');
                            renderErrorTable('加载数据表列表失败', data.message);
                        }
                    })
                    .catch(error => {
                        console.error('加载所有表时出错:', error);
                        showStatus('获取数据表列表失败：' + error.message, 'danger');
                        renderErrorTable('加载数据表列表失败', error.message);
                    });
            }

            // 填充表格选择下拉菜单
            function populateTableSelect(data) {
                tableSelect.innerHTML = '<option value="">-- 请选择数据表 --</option>'; // 重置选项
                if (!data || Object.keys(data).length === 0) {
                    return;
                }
                for (const tableName in data) {
                    const option = document.createElement('option');
                    option.value = tableName;
                    option.textContent = `${getTableDescription(tableName)} (${tableName})`;
                    tableSelect.appendChild(option);
                }

                // 添加change事件监听器
                tableSelect.addEventListener('change', function() {
                    const selectedTable = this.value;
                    // 重置文件输入
                    document.getElementById('csvFile').value = '';
                    // 根据选择显示相应的数据
                    if (selectedTable) {
                        loadTableData(selectedTable);
                    } else {
                        renderAllTablesSummary(allTableStatusData);
                    }
                });
            }

             // 渲染所有表的汇总信息（初始状态和未选择表时）
             function renderAllTablesSummary(data) {
                 tableListHeader.textContent = '数据表列表';
                 const headers = `
                     <tr>
                         <th style="width: 200px">表名</th>
                         <th>描述</th>
                         <th style="width: 100px">总记录数</th>
                         <th style="width: 70%">数据表字段信息</th>
                         <th style="width: auto;">最后更新时间</th>
                     </tr>
                 `;
                 tableHead.innerHTML = headers;
                 tableBody.innerHTML = ''; // 清除之前的数据

                 if (!data || Object.keys(data).length === 0) {
                     tableBody.innerHTML = '<tr><td colspan="5">无数据表</td></tr>';
                     return;
                 }

                 const rows = Object.entries(data).map(([tableName, info]) => {
                     // 格式化字段信息，使用网格布局
                     const columnsInfo = `
                         <div class="d-grid" style="grid-template-columns: repeat(13, 1fr); gap: 4px;">
                             ${info.columns.map(col => {
                                 const required = col.null === 'NO' && !col.default ? 
                                     '<span class="text-danger" title="必填">*</span>' : '';
                                 const autoIncrement = col.extra === 'auto_increment' ? 
                                     '<span class="text-info" title="自增">A</span>' : '';
                                 const timestamp = col.type.includes('timestamp') ? 
                                     '<span class="text-warning" title="时间戳">T</span>' : '';
                                 const comment = getColumnComment(col);
                                 return `
                                     <div class="text-truncate" style="font-size: 0.85rem;" 
                                          title="${col.name}: ${col.type}${comment ? '\n' + comment : ''}">
                                         ${col.name}${required}${autoIncrement}${timestamp}
                                         ${comment ? `<span class="text-muted" style="font-size: 0.8em; display: block;">${comment}</span>` : ''}
                                     </div>`;
                             }).join('')}
                         </div>`;

                     return `
                         <tr>
                             <td>${tableName}</td>
                             <td>${getTableDescription(tableName)}</td>
                             <td class="text-center">${info.count}</td>
                             <td class="p-2">${columnsInfo}</td>
                             <td>${info.latest_updated_at ? formatTimestamp(info.latest_updated_at) : '-'}</td>
                         </tr>`;
                 }).join('');
                 tableBody.innerHTML = rows;
             }

            // 加载单个表的数据（最后20条记录）
            function loadTableData(tableName) {
                if (!tableName) {
                    renderAllTablesSummary(allTableStatusData);
                    return;
                }
                tableListHeader.textContent = `数据表: ${getTableDescription(tableName)} (${tableName}) - 最后20条记录`;
                tableHead.innerHTML = ''; // 清除之前的数据
                tableBody.innerHTML = '<tr><td colspan="100%">正在加载数据...</td></tr>'; // 加载指示器

                // 从缓存获取列信息
                const tableInfo = allTableStatusData[tableName];
                if (!tableInfo) {
                     showStatus(`获取表 ${tableName} 结构失败：缓存中不存在该表信息`, 'danger');
                     renderErrorTable(`加载表 ${tableName} 数据失败`, '无法获取表结构信息');
                     return;
                }
                const columns = tableInfo.columns;

                // 格式化表头，添加字段说明
                const headers = columns.map(col => {
                    const required = col.null === 'NO' && !col.default ? 
                        '<span class="text-danger" title="必填">*</span>' : '';
                    const autoIncrement = col.extra === 'auto_increment' ? 
                        '<span class="text-info" title="自增">A</span>' : '';
                    const timestamp = col.type.includes('timestamp') ? 
                        '<span class="text-warning" title="时间戳">T</span>' : '';
                    const comment = getColumnComment(col);
                    return `<th title="${col.name}: ${col.type}${comment ? '\n' + comment : ''}">
                        ${col.name}${required}${autoIncrement}${timestamp}<br><small class="text-muted">${comment}</small>
                    </th>`;
                }).join('');
                tableHead.innerHTML = `<tr>${headers}</tr>`;

                // 获取数据记录
                fetch(`/api/database/tables/${tableName}/latest?limit=20`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 渲染数据行
                            const rows = data.data.records.map(record => {
                                const cells = columns.map(col => {
                                    let cellContent = record[col.name];
                                    // 格式化时间戳字段
                                    if (col.type.includes('timestamp')) {
                                        cellContent = formatTimestamp(cellContent);
                                    }
                                    // 处理null或undefined
                                    return `<td>${cellContent !== null && cellContent !== undefined ? cellContent : '-'}</td>`;
                                }).join('');
                                return `<tr>${cells}</tr>`;
                            }).join('');
                            tableBody.innerHTML = rows;
                        } else {
                            showStatus(`获取表 ${tableName} 数据失败：` + data.message, 'danger');
                             // 渲染错误表格时仍然使用已知的列信息
                            renderErrorTable(`加载表 ${tableName} 数据失败`, data.message, columns);
                        }
                    })
                    .catch(error => {
                        console.error(`加载表 ${tableName} 数据时出错:`, error);
                        showStatus(`获取表 ${tableName} 数据失败：` + error.message, 'danger');
                         // 渲染错误表格时仍然使用已知的列信息
                        renderErrorTable(`加载表 ${tableName} 数据失败`, error.message, columns);
                    });
            }

             // 渲染错误信息表格
             function renderErrorTable(title, message, columns = []) {
                  tableListHeader.textContent = title;
                  // 如果有列信息，尝试渲染带有列头的空表格
                  if (columns.length > 0) {
                       const headers = columns.map(col => {
                           const required = col.null === 'NO' && !col.default ? '<span class="text-danger" title="必填">*</span>' : '';
                           const autoIncrement = col.extra === 'auto_increment' ? '<span class="text-info" title="自增">A</span>' : '';
                           const timestamp = col.type.includes('timestamp') ? '<span class="text-warning" title="时间戳">T</span>' : '';
                            const comment = getColumnComment(col);
                            return `<th title="${col.name}: ${col.type}${comment ? '\n' + comment : ''}">
                                ${col.name}${required}${autoIncrement}${timestamp}<br><small class="text-muted">${comment}</small>
                            </th>`;
                       }).join('');
                       tableHead.innerHTML = `<tr>${headers}</tr>`;
                       tableBody.innerHTML = `<tr><td colspan="${columns.length}"><p>${message}</p></td></tr>`;
                   } else {
                        // 否则渲染只有错误消息的表格
                        tableHead.innerHTML = '';
                       tableBody.innerHTML = `<tr><td colspan="100%"><p>${message}</p></td></tr>`;
                   }
             }

            // 获取表格描述
            function getTableDescription(tableName) {
                 const descriptions = {
                    'achievements': '成就表',
                    'animal_riddles': '动物题库',
                    'app_icon_riddles': 'APP图标题库',
                    'audio_animal_riddles': '动物音频题库',
                    'audio_song_riddles': '歌曲音频题库',
                    'brand_logo_riddles': '品牌logo题库',
                    'car_logo_riddles': '车标题库',
                    'flag_riddles': '国旗题库',
                    'fruit_riddles': '水果题库',
                    'idiom_riddles': '成语题库',
                    'login_logs': '登录记录',
                    'map_outline_riddles': '地图轮廓题库',
                    'movie_still_riddles': '电影剧照题库',
                    'person_riddles': '人名题库',
                    'place_riddles': '地名题库',
                    'song_poetry_riddles': '宋词题库',
                    'tang_poetry_riddles': '唐诗题库',
                    'user_achievements': '用户成就',
                    'user_answers': '答题记录',
                    'user_stats': '用户数据统计',
                    'users': '用户信息',
                    'word_riddles': '字谜表',
                    'xiehouyu_riddles': '歇后语题库'
                };
                return descriptions[tableName] || tableName; // 如果没有描述则返回表名
            }

            // 添加字段映射确认模态框
            const mappingModal = `
                <div class="modal fade" id="mappingModal" tabindex="-1" aria-labelledby="mappingModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="mappingModalLabel">CSV字段映射确认</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr id="mappingTableHeader"> <!-- 表头动态生成 -->
                                                <th style="width: 200px; white-space: nowrap;">字段信息</th> <!-- 左上角标题, 宽度加倍，不换行 -->
                                                <!-- 字段名将通过JavaScript动态加载 -->
                                            </tr>
                                        </thead>
                                        <tbody id="mappingTableBody"> <!-- 表格内容动态加载 -->
                                            <!-- 表格内容将通过JavaScript动态加载 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="alert alert-info mt-3">
                                    <small>
                                        <i class="bi bi-info-circle"></i> 说明：
                                        <ul class="mb-0">
                                            <li><span class="text-success">●</span> 表示CSV文件中存在该字段</li>
                                            <li><span class="text-danger">●</span> 表示CSV文件中不存在该字段</li>
                                            <li><span class="text-danger">*</span> 表示该字段为必填</li>
                                            <li>类型为 timestamp 的字段默认值建议使用 CURRENT_TIMESTAMP</li>
                                        </ul>
                                    </small>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" id="confirmMapping">确认上传</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', mappingModal);

            // 获取表结构信息 (保留此函数供映射使用)
            async function getTableStructure(tableName) {
                try {
                    const response = await fetch(`/api/database/tables/${tableName}/structure`);
                    const data = await response.json();
                    if (data.success) {
                        return data.data;
                    }
                    throw new Error(data.message);
                } catch (error) {
                    console.error('获取表结构失败:', error);
                    throw error;
                }
            }

            // 解析CSV文件头部
            function parseCSVHeader(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const text = e.target.result;
                        // 处理可能的BOM标记
                        const cleanText = text.replace(/^\uFEFF/, '');
                        // 处理可能的引号和空格
                        const firstLine = cleanText.split('\n')[0].trim();
                        const headers = firstLine.split(',').map(h => {
                            // 移除引号并trim
                            return h.replace(/^["']|["']$/g, '').trim();
                        }).filter(h => h); // 过滤空值
                        
                        console.log('解析到的CSV头部:', headers); // 调试信息
                        resolve(headers);
                    };
                    reader.onerror = function(error) {
                        console.error('读取CSV文件失败:', error);
                        reject(error);
                    };
                    reader.readAsText(file);
                });
            }

            // 显示字段映射确认弹窗
            async function showMappingConfirmation(tableName, csvFile) {
                try {
                    // 获取表结构（此处仍使用后端接口，确保最新结构信息）
                    const [tableStructure, csvHeaders] = await Promise.all([
                        getTableStructure(tableName),
                        parseCSVHeader(csvFile)
                    ]);

                    console.log('表结构:', tableStructure); // 调试信息
                    console.log('CSV头部:', csvHeaders); // 调试信息

                    // 构建表格表头（字段名）
                    const tableHeaderHtml = '<th>字段信息</th>' + tableStructure.columns.map(col => `
                        <th title="${col.name}: ${col.type}" style="min-width: 100px; white-space: nowrap;"> <!-- 添加最小宽度和不换行 -->
                            ${col.name}
                            ${col.null === 'NO' && !col.default ? '<span class="text-danger" title="必填">*</span>' : ''}
                        </th>
                    `).join('');
                    document.getElementById('mappingTableHeader').innerHTML = tableHeaderHtml;

                    const mappingTableBody = document.getElementById('mappingTableBody');
                    mappingTableBody.innerHTML = ''; // 清空旧内容

                    // 遍历生成行（根据用户要求的顺序：注释, 状态, 类型, 映射关系, 默认值）
                    const rowTypes = [
                        { label: '注释', render: col => `<td><small class="text-muted">${getColumnComment(col)}</small></td>` },
                        { label: '状态', render: col => {
                            const existsInCsv = csvHeaders.some(h => h.toLowerCase() === col.name.toLowerCase());
                            const statusChar = existsInCsv ? '√' : '×';
                            return `<td class="text-center"><span class="status-dot ${existsInCsv ? 'text-success' : 'text-danger'}">${statusChar}</span></td>`;
                        }},
                        { label: '类型', render: col => `<td><small class="text-muted">${col.type}</small></td>` },
                        { label: '映射关系', render: col => {
                            const existsInCsv = csvHeaders.some(h => h.toLowerCase() === col.name.toLowerCase());
                            if (existsInCsv) {
                                const matchedHeader = csvHeaders.find(h => h.toLowerCase() === col.name.toLowerCase());
                                return `<td>
                                    <select class="form-select form-select-sm" name="mapping_${col.name}">
                                        <option value="">-- 不映射 --</option>
                                        ${csvHeaders.map(h => 
                                            `<option value="${h}" ${h.toLowerCase() === col.name.toLowerCase() ? 'selected' : ''}>${h}</option>`
                                        ).join('')}
                                    </select>
                                </td>`;
                            } else {
                                return '<td><span class="text-muted">-</span></td>';
                            }
                        }},
                        { label: '默认值', render: col => {
                            const existsInCsv = csvHeaders.some(h => h.toLowerCase() === col.name.toLowerCase());
                            if (!existsInCsv) {
                                let defaultValue = '';
                                if (col.default) {
                                    defaultValue = col.default;
                                } else if (col.type.includes('timestamp')) {
                                    defaultValue = 'CURRENT_TIMESTAMP';
                                } else if (col.type.includes('int')) {
                                    defaultValue = '0';
                                } else if (col.type.includes('varchar') || col.type.includes('text')) {
                                    defaultValue = '';
                                } else if (col.type.includes('enum')) {
                                    const match = col.type.match(/'([^']+)'/);
                                    if (match && match[1]) { defaultValue = match[1]; } else { defaultValue = ''; }
                                }
                                return `<td>
                                    <input type="text" class="form-control form-control-sm" 
                                           name="default_${col.name}" value="${defaultValue}"
                                           placeholder="默认值">
                                </td>`;
                            } else {
                                return '<td><span class="text-muted">-</span></td>';
                            }
                        }}
                    ];

                    rowTypes.forEach(rowInfo => {
                        const tr = document.createElement('tr');
                        tr.innerHTML = `<th>${rowInfo.label}</th>` + tableStructure.columns.map(col => rowInfo.render(col)).join('');
                        mappingTableBody.appendChild(tr);
                    });

                    // 显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('mappingModal'));
                    modal.show();

                    // 处理确认按钮点击
                    document.getElementById('confirmMapping').onclick = function() {
                        const formData = new FormData();
                        formData.append('table', tableName);
                        formData.append('file', csvFile);

                        // 收集字段映射
                        const mapping = {};
                        let hasRequiredMapping = true;
                        const missingRequired = [];

                        tableStructure.columns.forEach(col => {
                            const isRequired = col.null === 'NO' && !col.default;
                            const select = document.querySelector(`select[name="mapping_${col.name}"]`);
                            
                            if (select && select.value) {
                                mapping[col.name] = select.value;
                            } else if (isRequired) {
                                hasRequiredMapping = false;
                                missingRequired.push(col.name);
                            }
                        });

                        // 验证必填字段映射
                        if (!hasRequiredMapping) {
                            showStatus(`以下必填字段未设置映射：${missingRequired.join(', ')}`, 'danger');
                            return;
                        }

                        formData.append('mapping', JSON.stringify(mapping));

                        // 收集默认值
                        const defaults = {};
                        tableStructure.columns.forEach(col => {
                            const input = document.querySelector(`input[name="default_${col.name}"]`);
                            if (input && input.value) {
                                defaults[col.name] = input.value;
                            }
                        });
                        formData.append('defaults', JSON.stringify(defaults));

                        // 关闭模态框
                        modal.hide();

                        // 开始上传
                        uploadFile(formData);
                    };
                } catch (error) {
                    console.error('准备上传时出错:', error); // 调试信息
                    showStatus('准备上传时出错：' + error.message, 'danger');
                }
            }

            // 处理文件上传
            function uploadFile(formData) {
                progressBar.style.display = 'block';
                progressBarInner.style.width = '0%';
                progressBarInner.textContent = '0%';
                showStatus('开始上传...', 'info');

                const xhr = new XMLHttpRequest();
                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        progressBarInner.style.width = percentComplete.toFixed(2) + '%';
                        progressBarInner.textContent = percentComplete.toFixed(0) + '% ';
                        showStatus(`上传进度: ${e.loaded} / ${e.total} 字节 (${percentComplete.toFixed(0)}%)`, 'info');
                    }
                });

                xhr.addEventListener('load', function() {
                    progressBarInner.style.width = '100%';
                    progressBarInner.textContent = '100% ';
                    showStatus('上传完成，正在处理...', 'info');
                    try {
                        const data = JSON.parse(xhr.responseText);
                        if (data.success) {
                            // 显示详细的统计信息
                            showStatus(data.summary, 'success');
                            // 重新加载表格数据
                            loadTableData(formData.get('table'));
                            // 重新加载所有表状态
                            loadAllTables();
                        } else {
                            showStatus(`更新失败：${data.message}`, 'danger');
                        }
                    } catch (error) {
                        showStatus('服务器响应解析失败：' + error.message, 'danger');
                        console.error('解析服务器响应失败:', xhr.responseText, error);
                    }
                });

                xhr.addEventListener('error', function() {
                    showStatus('上传过程中发生网络错误', 'danger');
                    progressBar.style.display = 'none';
                });

                xhr.addEventListener('abort', function() {
                    showStatus('上传已取消', 'warning');
                    progressBar.style.display = 'none';
                });

                xhr.open('POST', '/api/database/upload');
                xhr.send(formData);
            }

            // 修改表单提交处理
            uploadForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const tableName = tableSelect.value;
                const csvFile = document.getElementById('csvFile').files[0];

                if (!tableName) {
                    showStatus('请选择数据表', 'danger');
                    return;
                }

                if (!csvFile) {
                    showStatus('请选择CSV文件', 'danger');
                    return;
                }

                // 显示字段映射确认弹窗
                showMappingConfirmation(tableName, csvFile);
            });

            // 显示状态消息
            function showStatus(message, type) {
                statusMessage.textContent = message;
                statusMessage.className = `alert alert-${type} status-message`;
                statusMessage.style.display = message !== '' ? 'block' : 'none';
            }

            // 初始加载所有表列表
            loadAllTables();

        });
    </script>
</body>
</html> 