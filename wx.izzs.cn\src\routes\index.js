const express = require('express');
const router = express.Router();

// 导入各个路由模块
const authRouter = require('./auth');
const appIconsRouter = require('./appIcons');
const riddleRouter = require('./riddle');
const answerRouter = require('./answer');
const userRouter = require('./user');
const rankingRouter = require('./ranking');
const profileRouter = require('./profile');
const testRouter = require('./test');
const databaseRouter = require('./database');
const flagRouter = require('./flag');
const competitionRouter = require('./competition');
const competitionLibraryRouter = require('./competitionLibrary');

// 注册路由
router.use('/auth', authRouter);
router.use('/app-icons', appIconsRouter);
router.use('/riddle', riddleRouter);
router.use('/answer', answerRouter);
router.use('/user', userRouter);
router.use('/ranking', rankingRouter);
router.use('/profile', profileRouter);
router.use('/test', testRouter);
router.use('/database', databaseRouter);
router.use('/flag', flagRouter);
router.use('/competition', competitionRouter);
router.use('/competition-library', competitionLibraryRouter);

module.exports = router; 