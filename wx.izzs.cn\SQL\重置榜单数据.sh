#!/bin/bash

# 数据库连接信息
DB_HOST=localhost
DB_USER=guess
DB_PASSWORD=8NWCCBFfiK7ELf5M
DB_NAME=guess
DB_PORT=3306

# 获取当前日期和时间
NOW=$(date +"%Y-%m-%d %H:%M:%S")
TODAY=$(date +"%Y-%m-%d")
TODAY_START="$TODAY 00:00:00"

# 日志记录函数
log_message() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1"
}

# 重置周期跟踪
RESET_PERIODS=""

log_message "脚本开始执行"
log_message "当前时间: $NOW"
log_message "今天开始时间: $TODAY_START"

# 每次运行都包含每日重置周期
RESET_PERIODS="每日"

# 计算本周开始时间 (假设周一是一周的开始)
DAY_OF_WEEK=$(date +%u)
log_message "当前星期: $DAY_OF_WEEK (1=周一, 7=周日)"

# 计算本周开始（周一）的日期
if [ "$DAY_OF_WEEK" -eq 1 ]; then
    # 如果今天是周一，本周开始就是今天
    WEEK_START_DATE=$TODAY
    WEEK_START="$WEEK_START_DATE 00:00:00"
    log_message "今天是周一，本周开始时间就是今天: $WEEK_START"
    # 周一执行时，包含每周重置周期
    RESET_PERIODS="$RESET_PERIODS, 每周"
else
    # 其他情况，本周开始是 (DAY_OF_WEEK - 1) 天前
    DAYS_TO_SUBTRACT=$((DAY_OF_WEEK - 1))
    
    # 只使用日期部分进行计算，避免时间干扰
    WEEK_START_DATE=$(date -d "$TODAY -$DAYS_TO_SUBTRACT days" +"%Y-%m-%d")
    WEEK_START="$WEEK_START_DATE 00:00:00"
    
    log_message "今天不是周一，本周开始时间: $WEEK_START"
fi

# 计算本月开始时间
MONTH_START=$(date +"%Y-%m-01 00:00:00")
log_message "本月开始时间: $MONTH_START"

# 判断是否是每月1号
if [ "$(date +%d)" -eq 1 ]; then
    log_message "今天是本月第一天"
    # 月初执行时，包含每月重置周期
    RESET_PERIODS="$RESET_PERIODS, 每月"
fi

# 记录本次重置的周期
log_message "本次执行的重置周期: $RESET_PERIODS"

# 使用 mysql 命令行客户端执行 SQL 语句
log_message "执行数据库重置操作..."

# 构建 SQL 语句
SQL_COMMAND="
USE $DB_NAME;

SET @v_now = '$NOW';
SET @v_today_start = '$TODAY_START';
SET @v_week_start = '$WEEK_START';
SET @v_month_start = '$MONTH_START';

-- === 每日数据重置 ===
-- 检查并重置需要每日重置的数据
UPDATE users
SET
    today_score = CASE 
        WHEN today_updated_at < @v_today_start AND (today_score > 0 OR today_answers > 0 OR today_correct_answers > 0) THEN 0
        ELSE today_score
    END,
    today_answers = CASE 
        WHEN today_updated_at < @v_today_start AND (today_score > 0 OR today_answers > 0 OR today_correct_answers > 0) THEN 0
        ELSE today_answers
    END,
    today_correct_answers = CASE 
        WHEN today_updated_at < @v_today_start AND (today_score > 0 OR today_answers > 0 OR today_correct_answers > 0) THEN 0
        ELSE today_correct_answers
    END,
    today_updated_at = CASE 
        WHEN today_updated_at < @v_today_start THEN @v_now
        ELSE today_updated_at
    END
WHERE today_updated_at < @v_today_start;

-- === 每周数据重置 ===
-- 检查并重置需要每周重置的数据
UPDATE users
SET
    week_score = CASE 
        WHEN week_updated_at < @v_week_start AND (week_score > 0 OR week_answers > 0 OR week_correct_answers > 0) THEN 0
        ELSE week_score
    END,
    week_answers = CASE 
        WHEN week_updated_at < @v_week_start AND (week_score > 0 OR week_answers > 0 OR week_correct_answers > 0) THEN 0
        ELSE week_answers
    END,
    week_correct_answers = CASE 
        WHEN week_updated_at < @v_week_start AND (week_score > 0 OR week_answers > 0 OR week_correct_answers > 0) THEN 0
        ELSE week_correct_answers
    END,
    week_updated_at = CASE 
        WHEN week_updated_at < @v_week_start THEN @v_now
        ELSE week_updated_at
    END
WHERE week_updated_at < @v_week_start;

-- === 每月数据重置 ===
-- 检查并重置需要每月重置的数据
UPDATE users
SET
    month_score = CASE 
        WHEN month_updated_at < @v_month_start AND (month_score > 0 OR month_answers > 0 OR month_correct_answers > 0) THEN 0
        ELSE month_score
    END,
    month_answers = CASE 
        WHEN month_updated_at < @v_month_start AND (month_score > 0 OR month_answers > 0 OR month_correct_answers > 0) THEN 0
        ELSE month_answers
    END,
    month_correct_answers = CASE 
        WHEN month_updated_at < @v_month_start AND (month_score > 0 OR month_answers > 0 OR month_correct_answers > 0) THEN 0
        ELSE month_correct_answers
    END,
    month_updated_at = CASE 
        WHEN month_updated_at < @v_month_start THEN @v_now
        ELSE month_updated_at
    END
WHERE month_updated_at < @v_month_start;
"

# 执行 SQL 命令
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e"$SQL_COMMAND"

# 检查命令是否执行成功
if [ $? -eq 0 ]; then
    log_message "数据库统计数据重置成功 (周期: $RESET_PERIODS)"
    echo "----------------------------------------------------------------------------"
    echo "★[$(date +"%Y-%m-%d %H:%M:%S")] Successful"
else
    log_message "数据库统计数据重置失败"
    echo "----------------------------------------------------------------------------"
    echo "✗[$(date +"%Y-%m-%d %H:%M:%S")] Failed"
    exit 1
fi