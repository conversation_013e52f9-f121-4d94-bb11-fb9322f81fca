const { validateOpenid } = require('../utils/wx');

/**
 * 验证用户openid的中间件
 * 用于需要用户身份的路由
 */
const validateOpenidMiddleware = async (req, res, next) => {
  // 同时支持从query和body中获取openid
  const openid = req.query.openid || req.body.openid;
  
  console.log('验证用户标识:', {
    method: req.method,
    path: req.path,
    queryOpenid: req.query.openid,
    bodyOpenid: req.body.openid,
    finalOpenid: openid
  });
  
  if (!openid) {
    console.error('缺少用户标识:', {
      method: req.method,
      path: req.path,
      query: req.query,
      body: req.body
    });
    return res.json({
      code: 401,
      msg: '缺少用户标识'
    });
  }

  try {
    // 验证openid格式
    if (!validateOpenid(openid)) {
      console.error('无效的用户标识:', openid);
      return res.json({
        code: 401,
        msg: '无效的用户标识'
      });
    }

    // 将openid添加到请求对象中，方便后续使用
    req.openid = openid;
    next();
  } catch (err) {
    console.error('验证用户标识失败:', err);
    res.json({
      code: 500,
      msg: '验证用户标识失败',
      error: err.message
    });
  }
};

module.exports = {
  validateOpenid: validateOpenidMiddleware
}; 