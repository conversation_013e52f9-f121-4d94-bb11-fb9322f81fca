const express = require('express');
const router = express.Router();
const db = require('../utils/db');

/**
 * 获取题库列表
 * GET /api/competition-library/list
 */
router.get('/list', async (req, res) => {
  try {
    const { page = 1, limit = 10, creator_openid } = req.query;
    const offset = (page - 1) * limit;
    
    let sql = `
      SELECT
        id,
        name,
        creator_openid,
        creator_name,
        types,
        quantities,
        total_questions,
        participant_count,
        created_at
      FROM comp_libraries
      WHERE status = 1
    `;
    
    const params = [];
    
    // 如果指定创建者，只查询该用户创建的题库
    if (creator_openid) {
      sql += ' AND creator_openid = ?';
      params.push(creator_openid);
    }
    
    sql += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));
    
    const libraries = await db.execute(sql, params);
    
    // 处理数据格式
    const formattedLibraries = libraries.map(lib => ({
      id: lib.id,
      title: lib.name,
      types: lib.types.split(','),
      quantities: JSON.parse(lib.quantities),
      totalQuestions: lib.total_questions,
      participants: lib.participant_count,
      createTime: lib.created_at,
      creator: lib.creator_name
    }));
    
    res.json({
      code: 0,
      message: 'success',
      data: {
        libraries: formattedLibraries,
        total: formattedLibraries.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取题库列表失败:', error);
    res.status(500).json({
      code: -1,
      message: '获取题库列表失败',
      error: error.message
    });
  }
});

/**
 * 创建题库
 * POST /api/competition-library/create
 */
router.post('/create', async (req, res) => {
  try {
    const { 
      name, 
      creator_openid, 
      creator_name, 
      types, 
      quantities 
    } = req.body;
    
    // 参数验证
    if (!name || !creator_openid || !creator_name || !types || !quantities) {
      return res.status(400).json({
        code: -1,
        message: '参数不完整'
      });
    }
    
    // 验证题库名称
    if (name.length < 2 || name.length > 10) {
      return res.status(400).json({
        code: -1,
        message: '题库名称长度必须在2-10字符之间'
      });
    }
    
    // 验证题库名称格式（只允许中文、英文、数字）
    const nameRegex = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/;
    if (!nameRegex.test(name)) {
      return res.status(400).json({
        code: -1,
        message: '题库名称只能包含中文、英文和数字'
      });
    }
    
    // 计算总题目数量
    const totalQuestions = Object.values(quantities).reduce((sum, count) => sum + count, 0);
    
    if (totalQuestions === 0) {
      return res.status(400).json({
        code: -1,
        message: '至少需要选择一种题型'
      });
    }
    
    // 插入数据库
    const sql = `
      INSERT INTO comp_libraries (
        name, creator_openid, creator_name,
        types, quantities, total_questions, participant_count
      ) VALUES (?, ?, ?, ?, ?, ?, 0)
    `;

    const params = [
      name,
      creator_openid,
      creator_name,
      types.join(','),
      JSON.stringify(quantities),
      totalQuestions
    ];

    const result = await db.execute(sql, params);
    const libraryId = result.insertId;
    
    res.json({
      code: 0,
      message: '题库创建成功',
      data: {
        id: libraryId,
        name: name,
        total_questions: totalQuestions
      }
    });
    
  } catch (error) {
    console.error('创建题库失败:', error);
    res.status(500).json({
      code: -1,
      message: '创建题库失败',
      error: error.message
    });
  }
});

/**
 * 获取题库详情
 * GET /api/competition-library/detail/:libraryId
 */
router.get('/detail/:libraryId', async (req, res) => {
  try {
    const { libraryId } = req.params;

    const sql = `
      SELECT
        id,
        name,
        creator_openid,
        creator_name,
        types,
        quantities,
        total_questions,
        participant_count,
        created_at
      FROM comp_libraries
      WHERE id = ? AND status = 1
    `;

    const libraries = await db.execute(sql, [libraryId]);

    if (libraries.length === 0) {
      return res.status(404).json({
        code: -1,
        message: '题库不存在'
      });
    }

    const lib = libraries[0];

    res.json({
      code: 0,
      message: 'success',
      data: {
        id: lib.id,
        title: lib.name,
        types: lib.types.split(','),
        quantities: JSON.parse(lib.quantities),
        totalQuestions: lib.total_questions,
        participants: lib.participant_count,
        createTime: lib.created_at,
        creator: lib.creator_name
      }
    });

  } catch (error) {
    console.error('获取题库详情失败:', error);
    res.status(500).json({
      code: -1,
      message: '获取题库详情失败',
      error: error.message
    });
  }
});

/**
 * 检查用户今日是否已创建题库
 * GET /api/competition-library/check-today-creation
 */
router.get('/check-today-creation', async (req, res) => {
  try {
    const { openid } = req.query;

    if (!openid) {
      return res.status(400).json({
        code: -1,
        message: '缺少openid参数'
      });
    }

    // 获取今日日期范围（当地时间）
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    // 查询今日创建记录
    const sql = `
      SELECT COUNT(*) as count
      FROM comp_libraries
      WHERE creator_openid = ?
        AND created_at >= ?
        AND created_at < ?
        AND status = 1
    `;

    const result = await db.execute(sql, [openid, todayStart, todayEnd]);
    const hasCreatedToday = result[0].count > 0;

    res.json({
      code: 0,
      message: '查询成功',
      data: {
        hasCreatedToday: hasCreatedToday,
        todayCount: result[0].count
      }
    });

  } catch (error) {
    console.error('检查今日创建记录失败:', error);
    res.status(500).json({
      code: -1,
      message: '检查今日创建记录失败',
      error: error.message
    });
  }
});

/**
 * 加入题库（开始答题）
 * POST /api/competition-library/join
 */
router.post('/join', async (req, res) => {
  try {
    const { library_id, user_openid, user_name } = req.body;
    
    if (!library_id || !user_openid || !user_name) {
      return res.status(400).json({
        code: -1,
        message: '参数不完整'
      });
    }
    
    // 检查题库是否存在
    const libSql = 'SELECT total_questions FROM comp_libraries WHERE id = ? AND status = 1';
    const libraries = await db.execute(libSql, [library_id]);

    if (libraries.length === 0) {
      return res.status(404).json({
        code: -1,
        message: '题库不存在'
      });
    }

    const totalQuestions = libraries[0].total_questions;

    // 检查用户是否已经参与过
    const checkSql = 'SELECT id FROM comp_participants WHERE library_id = ? AND user_openid = ?';
    const existing = await db.execute(checkSql, [library_id, user_openid]);

    if (existing.length > 0) {
      return res.json({
        code: 0,
        message: '已参与过该题库',
        data: { already_joined: true }
      });
    }

    // 插入参与记录
    const insertSql = `
      INSERT INTO comp_participants (
        library_id, user_openid, user_name, total_questions, status
      ) VALUES (?, ?, ?, ?, 1)
    `;

    await db.execute(insertSql, [library_id, user_openid, user_name, totalQuestions]);

    // 更新题库参与人数和更新时间
    const updateSql = `
      UPDATE comp_libraries
      SET participant_count = participant_count + 1,
          updated_at = NOW()
      WHERE id = ?
    `;
    await db.execute(updateSql, [library_id]);
    
    res.json({
      code: 0,
      message: '加入题库成功',
      data: { 
        library_id: library_id,
        total_questions: totalQuestions
      }
    });
    
  } catch (error) {
    console.error('加入题库失败:', error);
    res.status(500).json({
      code: -1,
      message: '加入题库失败',
      error: error.message
    });
  }
});

module.exports = router;
