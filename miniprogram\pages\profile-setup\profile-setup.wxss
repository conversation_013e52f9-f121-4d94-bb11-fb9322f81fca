/* pages/profile-setup/profile-setup.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  padding-bottom: calc(env(safe-area-inset-bottom) + 30rpx);
  box-sizing: border-box;
}

/* 页面标题 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 30rpx 20rpx;
  color: white;
  position: relative;
  margin-bottom: 20rpx;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s;
}

.back-btn:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.back-icon {
  font-size: 36rpx;
  font-weight: bold;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
}

.skip-btn {
  font-size: 28rpx;
  opacity: 0.8;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
}

.skip-btn-placeholder {
  width: 60rpx;
}

/* 资料设置区域 */
.profile-section {
  background: white;
  margin: 0 30rpx 30rpx;
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  text-align: center;
}

.section-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.6;
  text-align: center;
}

/* 头像设置 */
.avatar-section {
  margin-bottom: 50rpx;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.avatar-btn {
  position: relative;
  width: 160rpx !important;
  height: 160rpx !important;
  min-width: 160rpx;
  min-height: 160rpx;
  max-width: 160rpx;
  max-height: 160rpx;
  padding: 0 !important;
  margin: 0 0 20rpx 80rpx;
  background: transparent !important;
  border: none !important;
  border-radius: 80rpx !important;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
  flex-shrink: 0;
  outline: none;
  box-sizing: border-box;
}

.avatar-btn::after {
  display: none !important;
  content: none !important;
  border: none !important;
}

.avatar-btn::before {
  display: none !important;
  content: none !important;
}

.avatar {
  width: 160rpx !important;
  height: 160rpx !important;
  min-width: 160rpx !important;
  min-height: 160rpx !important;
  max-width: 160rpx !important;
  max-height: 160rpx !important;
  border-radius: 80rpx !important;
  object-fit: cover !important;
  object-position: center center !important;
  display: block !important;
  border: none !important;
  outline: none !important;
  background: transparent;
  position: relative;
  z-index: 1;
  box-sizing: border-box !important;
}

.avatar-mask {
  display: none;
}

.avatar-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

/* 昵称设置 */
.nickname-section {
  margin-bottom: 20rpx;
}

.input-group {
  width: 100%;
}

.nickname-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  min-width: 80rpx;
  flex-shrink: 0;
  line-height: 80rpx;
}

.input-container {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.nickname-input {
  flex: 1;
  height: 80rpx;
  padding: 0 60rpx 0 20rpx;
  font-size: 28rpx;
  border: 3rpx solid #e0e0e0;
  border-radius: 16rpx;
  background: #f8f9fa;
  box-sizing: border-box;
  transition: all 0.3s;
}

.nickname-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.input-counter {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #999;
  background: white;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.input-tip {
  margin-top: 0;
  margin-left: 100rpx;
  font-size: 24rpx;
  color: #666;
  text-align: left;
}

/* 保存按钮 */
.save-section {
  padding: 30rpx;
}

.save-btn {
  width: 100%;
  height: 96rpx;
  background: #07c160;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 48rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(7, 193, 96, 0.3);
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-btn::after {
  border: none;
}

.save-btn.disabled {
  background: #cccccc;
  box-shadow: none;
  opacity: 0.6;
  color: #999;
}

.save-btn:not(.disabled):active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.4);
  background: #06ad56;
}

/* 提示信息 */
.tips-section {
  margin: 20rpx 30rpx 30rpx;
}

.tips-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tips-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.tips-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tips-item {
  display: flex;
  align-items: flex-start;
}

.tip-dot {
  color: #667eea;
  font-weight: bold;
  margin-right: 12rpx;
  margin-top: 2rpx;
  line-height: 1;
}

.tip-text {
  flex: 1;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
} 