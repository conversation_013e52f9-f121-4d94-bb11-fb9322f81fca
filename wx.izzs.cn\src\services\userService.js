const mysql = require('mysql2/promise');
const dbConfig = require('../config/db');
const { processUserAvatar, processUsersAvatars } = require('../utils/avatarHelper');

// 获取排行榜
exports.getRanking = async () => {
  const conn = await mysql.createConnection(dbConfig);
  const [rows] = await conn.execute('SELECT id, nickname, avatar_url, total_score, correct_answers, total_answers FROM users ORDER BY total_score DESC, correct_answers DESC LIMIT 50');
  await conn.end();
  
  // 处理用户头像
  return processUsersAvatars(rows);
};

// 获取个人信息
exports.getProfile = async (openid) => {
  const conn = await mysql.createConnection(dbConfig);
  const [rows] = await conn.execute('SELECT id, nickname, avatar_url, total_score, correct_answers, total_answers FROM users WHERE id = ?', [openid]);
  await conn.end();
  
  const user = rows[0] || null;
  // 处理用户头像
  return user ? processUserAvatar(user) : null;
};

// 获取用户资料（简化版本，仅返回基本信息）
exports.getUserProfile = async (openid) => {
  const conn = await mysql.createConnection(dbConfig);
  
  try {
    const [rows] = await conn.execute(`
      SELECT 
        id,
        nickname,
        avatar_url,
        total_score,
        total_answers,
        correct_answers,
        created_at,
        updated_at
      FROM users 
      WHERE id = ?
    `, [openid]);
    
    if (rows.length === 0) {
      return null;
    }
    
    let user = rows[0];
    
    // 处理用户头像
    user = processUserAvatar(user);
    
    // 转换字段名以匹配前端需要
    return {
      id: user.id,
      nickName: user.nickname,
      avatarUrl: user.avatar_url,
      totalScore: user.total_score,
      totalAnswers: user.total_answers,
      correctAnswers: user.correct_answers,
      createdAt: user.created_at,
      updatedAt: user.updated_at
    };
    
  } finally {
    await conn.end();
  }
};

// 获取用户统计信息
exports.getUserStats = async (openid) => {
  const conn = await mysql.createConnection(dbConfig);
  
  try {
    // 获取用户基本信息
    const [userRows] = await conn.execute(`
      SELECT 
        id,
        nickname,
        avatar_url,
        total_score,
        total_answers,
        correct_answers,
        created_at
      FROM users 
      WHERE id = ?
    `, [openid]);
    
    if (userRows.length === 0) {
      return null;
    }
    
    let user = userRows[0];
    
    // 处理用户头像
    user = processUserAvatar(user);
    
    // 获取用户排名
    const [rankRows] = await conn.execute(`
      SELECT COUNT(*) + 1 as rank
      FROM users 
      WHERE total_score > ?
    `, [user.total_score]);
    
    user.rank = rankRows[0].rank;
    
    // 获取当前连胜和最佳连胜
    const [streakRows] = await conn.execute(`
      SELECT 
        COALESCE(MAX(current_streak), 0) as current_streak,
        COALESCE(MAX(best_streak), 0) as best_streak
      FROM user_stats 
      WHERE user_id = ?
    `, [openid]);
    
    user.current_streak = streakRows[0].current_streak;
    user.best_streak = streakRows[0].best_streak;
    
    // 获取最近答题记录
    const [recordRows] = await conn.execute(`
      SELECT 
        ua.id,
        ua.riddle_id,
        ua.table_name,
        ua.user_answer,
        ua.is_correct,
        ua.score,
        ua.time_used,
        ua.created_at,
        CASE 
          WHEN ua.table_name = 'idiom_riddles' THEN '成语'
          WHEN ua.table_name = 'xiehouyu_riddles' THEN '歇后语'
          WHEN ua.table_name = 'animal_riddles' THEN '动物'
          WHEN ua.table_name = 'fruit_riddles' THEN '水果'
          WHEN ua.table_name = 'place_riddles' THEN '地名'
          WHEN ua.table_name = 'person_riddles' THEN '人名'
          WHEN ua.table_name = 'tang_poetry_riddles' THEN '唐诗'
          WHEN ua.table_name = 'song_poetry_riddles' THEN '宋词'
          WHEN ua.table_name = 'flag_riddles' THEN '国旗'
          WHEN ua.table_name = 'map_outline_riddles' THEN '地图轮廓'
          WHEN ua.table_name = 'car_logo_riddles' THEN '车标'
          WHEN ua.table_name = 'brand_logo_riddles' THEN '品牌logo'
          WHEN ua.table_name = 'movie_still_riddles' THEN '电影剧照'
          WHEN ua.table_name = 'app_icon_riddles' THEN 'APP图标'
          WHEN ua.table_name = 'audio_song_riddles' THEN '歌曲'
          WHEN ua.table_name = 'audio_animal_riddles' THEN '动物叫声'
          WHEN ua.table_name = 'word_riddles' THEN '字谜'
          ELSE '其他'
        END as question_type,
        '示例题目内容' as question_content,
        '示例正确答案' as correct_answer
      FROM user_answers ua
      WHERE ua.user_id = ?
      ORDER BY ua.created_at DESC
      LIMIT 10
    `, [openid]);
    
    user.recent_records = recordRows;
    
    // 获取用户成就
    const [achievementRows] = await conn.execute(`
      SELECT 
        a.id,
        a.name,
        a.description,
        a.icon,
        a.points,
        ua.achieved_at,
        CASE WHEN ua.achievement_id IS NOT NULL THEN 1 ELSE 0 END as achieved
      FROM achievements a
      LEFT JOIN user_achievements ua ON a.id = ua.achievement_id AND ua.user_id = ?
      ORDER BY achieved DESC, a.id ASC
    `, [openid]);
    
    user.achievements = achievementRows;
    
    // 获取最近7天统计
    const [dailyRows] = await conn.execute(`
      SELECT 
        date,
        daily_score,
        daily_answers,
        daily_correct
      FROM user_stats 
      WHERE user_id = ? AND date >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
      ORDER BY date ASC
    `, [openid]);
    
    user.daily_stats = dailyRows;
    
    return user;
    
  } finally {
    await conn.end();
  }
};

// 保存用户信息
exports.saveUser = async (userData) => {
  const conn = await mysql.createConnection(dbConfig);
  
  try {
    const { openid, nickName, avatarUrl } = userData;
    
    // 检查用户是否已存在
    const [existingRows] = await conn.execute(
      'SELECT id FROM users WHERE id = ?',
      [openid]
    );
    
    if (existingRows.length > 0) {
      // 更新用户信息
      await conn.execute(
        'UPDATE users SET nickname = ?, avatar_url = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [nickName, avatarUrl, openid]
      );
    } else {
      // 创建新用户
      await conn.execute(`
        INSERT INTO users (
          id, 
          nickname, 
          avatar_url, 
          total_score, 
          total_answers, 
          correct_answers,
          created_at,
          updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [openid, nickName, avatarUrl, 0, 0, 0]);
    }
    
    return { success: true };
    
  } finally {
    await conn.end();
  }
};

// 更新用户头像URL
exports.updateUserAvatar = async (openid, avatarUrl) => {
  const conn = await mysql.createConnection(dbConfig);
  
  try {
    // 检查用户是否存在
    const [existingRows] = await conn.execute(
      'SELECT id FROM users WHERE id = ?',
      [openid]
    );
    
    if (existingRows.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }
    
    // 更新头像URL
    await conn.execute(
      'UPDATE users SET avatar_url = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [avatarUrl, openid]
    );
    
    return { success: true };
    
  } catch (error) {
    console.error('更新用户头像URL失败:', error);
    return {
      success: false,
      error: error.message
    };
  } finally {
    await conn.end();
  }
}; 