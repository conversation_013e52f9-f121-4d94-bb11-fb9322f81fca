// 获取应用实例
const app = getApp()
const { processUserAvatar, generateFallbackAvatar } = require('../../utils/avatarHelper')
const authManager = require('../../utils/auth')
const env = require('../../env.js')

Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    canIUseGetUserProfile: false,
    showTypePicker: false, // 是否显示类型选择弹窗
    typePickerList: [], // 当前主类型下的子类型列表
    typePickerMainType: '', // 当前主类型
    typePickerTitle: '', // 弹窗标题
    selectedTypes: [], // 选中的子类型
    loginLoading: false, // 登录加载状态
    userStats: {
      totalScore: 0,
      correctAnswers: 0,
      rank: '--'
    },
    backgroundImage: `${env.API_BASE_URL}/static/images/1.jpg`, // 添加背景图片URL
    isCompetitionMode: false, // 是否为竞技模式
    typeQuantities: {}, // 存储每个类型的数量
  },

  // 主类型与子类型映射
  typeMap: {
    text: [
      { label: '字谜', value: 'riddle' },
      { label: '成语', value: 'idiom' },
      { label: '歇后语', value: 'xiehouyu' },
      { label: '动物', value: 'animal' },
      { label: '水果', value: 'fruit' },
      { label: '地名', value: 'place' },
      { label: '人名', value: 'person' },
      { label: '唐诗', value: 'tang_poetry' },
      { label: '宋词', value: 'song_poetry' }
    ],
    image: [
      { label: '国旗', value: 'flag' },
      { label: '地图轮廓', value: 'map_outline' },
      { label: '车标', value: 'car_logo' },
      { label: '品牌logo', value: 'brand_logo' },
      { label: '电影剧照', value: 'movie_still' },
      { label: 'APP图标', value: 'app_icon' }
    ],
    audio: [
      { label: '歌曲', value: 'audio_song' },
      { label: '动物叫声', value: 'audio_animal' }
    ],
    competition: [
      // 文字类
      { label: '字谜', value: 'riddle' },
      { label: '成语', value: 'idiom' },
      { label: '歇后语', value: 'xiehouyu' },
      { label: '动物', value: 'animal' },
      { label: '水果', value: 'fruit' },
      { label: '地名', value: 'place' },
      { label: '人名', value: 'person' },
      { label: '唐诗', value: 'tang_poetry' },
      { label: '宋词', value: 'song_poetry' },
      // 图片类
      { label: '国旗', value: 'flag' },
      { label: '地图轮廓', value: 'map_outline' },
      { label: '车标', value: 'car_logo' },
      { label: '品牌logo', value: 'brand_logo' },
      { label: '电影剧照', value: 'movie_still' },
      { label: 'APP图标', value: 'app_icon' },
      // 音频类
      { label: '歌曲', value: 'audio_song' },
      { label: '动物叫声', value: 'audio_animal' }
    ]
  },

  onLoad() {
    // 检查是否可以使用getUserProfile（兼容性保留）
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }

    // 初始化登录状态
    this.initAuth()
  },

  onShow() {
    // 每次显示页面时刷新用户信息和统计数据
    this.refreshUserInfo()
  },

  // 初始化认证状态
  async initAuth() {
    try {
      // 静默登录（仅获取openid）
      const loginResult = await authManager.silentLogin()

      if (loginResult.success) {
        // 检查是否有用户信息
        const status = authManager.checkLoginStatus()
        if (status.hasUserInfo) {
          // 静默登录成功且有用户信息，设置用户信息并获取统计数据
          this.setUserInfo(status.userInfo) // setUserInfo 内部会调用 getUserStats
        } else {
           // 静默登录成功但没有用户信息，可能需要引导用户完善资料，但首页只需要显示默认数据

           this.setData({
             hasUserInfo: false,
             userStats: {
               totalScore: 0,
               correctAnswers: 0,
               rank: '--'
             }
           })
        }
      } else {
        // 静默登录失败，保持未登录状态，显示默认数据

         this.setData({
           hasUserInfo: false,
           userStats: {
             totalScore: 0,
             correctAnswers: 0,
             rank: '--'
           }
         })
      }
    } catch (error) {
      console.error('初始化认证失败:', error)
       // 异常情况下也显示默认数据
       this.setData({
         hasUserInfo: false,
         userStats: {
           totalScore: 0,
           correctAnswers: 0,
           rank: '--'
         }
       })
    }
  },

  // 获取用户统计数据 (使用回调方式，与profile页面一致)
  getUserStats() {

    // 检查 openid 是否存在
    if (!app.globalData.openid) {
      console.warn('openid not available, cannot fetch user stats.');
      this.setData({ // 如果没有 openid，重置统计数据
        userStats: {
          totalScore: 0,
          correctAnswers: 0,
          rank: '--'
        }
      });
      return; // 提前返回
    }

    wx.request({
      url: env.API_BASE_URL + '/api/user/stats',
      method: 'GET',
      data: { openid: app.globalData.openid },
      success: (res) => {

        if (res.data && res.data.code === 0) {
          const data = res.data.data

          this.setData({
            userStats: {
              totalScore: data.total_score || 0,
              correctAnswers: data.correct_answers || 0,
              rank: data.rank || '--'
            }
          })

        } else {
          // 处理接口返回非成功状态码的情况
          const errorMessage = (res.data && res.data.message) || '未知错误';
          console.error('获取用户统计数据失败:', res.statusCode, errorMessage); // 添加错误日志和状态码
          this.setData({ // 如果接口返回非成功，显示默认值
            userStats: {
              totalScore: 0,
              correctAnswers: 0,
              rank: '--'
            }
          });
        }
      },
      fail: (err) => {
        // 处理请求本身失败的情况 (例如网络错误)
        console.error('获取用户统计数据请求失败:', err); // 添加请求错误日志
        this.setData({ // 如果请求异常，显示默认值
          userStats: {
            totalScore: 0,
            correctAnswers: 0,
            rank: '--'
          }
        });
      }
    })
  },

  // 设置用户信息
  setUserInfo(userInfo) { // 移除 async 关键字

    // 处理用户头像
    const processedUserInfo = processUserAvatar(userInfo)

    this.setData({
      userInfo: processedUserInfo,
      hasUserInfo: true
    });

    // 获取用户统计数据
    this.getUserStats(); // 直接调用，不使用 await
  },

  // 刷新用户信息
  async refreshUserInfo() { // 保持 async，因为 checkLoginStatus 可能异步
    const status = authManager.checkLoginStatus(); // 假设 checkLoginStatus 是同步或返回 Promise


    if (status.isLoggedIn && status.hasUserInfo) {

      // 如果已登录且有用户信息，设置用户信息并获取统计数据
      this.setUserInfo(status.userInfo); // 直接调用 setUserInfo
    } else {

      // 未登录或没有用户信息，重置状态并显示默认数据
      this.setData({
        userInfo: null, // 重置 userInfo
        hasUserInfo: false,
        userStats: { // 重置 userStats
          totalScore: 0,
          correctAnswers: 0,
          rank: '--'
        }
      });
    }
  },

  // 微信一键登录
  async doLogin() { // 保持 async
    if (this.data.loginLoading) return

    this.setData({ loginLoading: true })

    try {
      wx.showLoading({ title: '登录中...' })

      // 使用简单登录流程
      const result = await authManager.simpleLogin()



      if (result.success) {
        if (result.needSetProfile) {
          // 需要设置资料，跳转到资料设置页面
          wx.hideLoading()
          wx.showToast({
            title: '请完善资料',
            icon: 'none'
          })

          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/profile-setup/profile-setup'
            })
          }, 1000)
        } else {
          // 已有资料，直接设置用户信息并获取统计数据

          this.setUserInfo(result.userInfo); // 直接调用 setUserInfo
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          })
        }
      } else {
        const errorMessage = result.error || '登录失败';
        console.error('Login failed:', errorMessage); // 添加登录失败日志
        wx.showToast({
          title: errorMessage,
          icon: 'none'
        })
      }

    } catch (error) {
      console.error('Login request exception:', error); // 添加登录请求异常日志
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
      this.setData({ loginLoading: false })
    }
  },

  // 获取用户信息（兼容旧版本）
  getUserProfile() {
    this.doLogin()
  },

  // 头像加载错误处理
  onAvatarError(e) {
    // 生成备用头像（已经是完整URL）
    const fallbackAvatar = generateFallbackAvatar(app.globalData.openid || 'default')

    // 更新用户信息中的头像
    const updatedUserInfo = {
      ...this.data.userInfo,
      avatarUrl: fallbackAvatar,
      avatar_url: fallbackAvatar
    }

    this.setData({
      userInfo: updatedUserInfo
    })

    // 同时更新缓存
    wx.setStorageSync('userInfo', updatedUserInfo)
  },

  // 点击类型卡片弹窗选择子类型
  navigateToGame(e) {
    const mainType = e.currentTarget.dataset.type

    // 如果是竞技模式，直接跳转到竞技大厅页面
    if (mainType === 'competition') {
      wx.navigateTo({
        url: '/pages/competition-lobby/competition-lobby'
      })
      return
    }

    const typeList = this.typeMap[mainType] || []
    const isCompetitionMode = false

    // 为每个类型项添加isSelected属性
    const typeListWithSelected = typeList.map(item => ({
      ...item,
      isSelected: false,
      quantity: 0 // 竞技模式下的数量
    }))

    let title = ''
    if (mainType === 'text') {
      title = '请选择文字谜题类型'
    } else if (mainType === 'image') {
      title = '请选择图片谜题类型'
    } else if (mainType === 'audio') {
      title = '请选择音频谜题类型'
    }

    // 强制刷新数据，确保selectedTypes是数组
    this.setData({
      showTypePicker: true,
      typePickerList: typeListWithSelected,
      typePickerMainType: mainType,
      typePickerTitle: title,
      selectedTypes: [], // 确保是数组类型
      isCompetitionMode: isCompetitionMode,
      typeQuantities: {} // 重置数量
    })
  },

  // 切换子类型选中状态
  onTypePickerToggle(e) {
    const value = e.currentTarget.dataset.value

    // 直接更新typePickerList中对应项的isSelected状态
    const updatedList = this.data.typePickerList.map(item => {
      if (item.value === value) {
        return {
          ...item,
          isSelected: !item.isSelected
        }
      }
      return item
    })

    // 获取所有选中的值
    const newSelected = updatedList.filter(item => item.isSelected).map(item => item.value)

    // 添加振动反馈
    wx.vibrateShort({
      type: 'light',
      fail: () => {} // 防止不支持振动的设备报错
    })

    // 使用setData更新数据
    this.setData({
      typePickerList: updatedList,
      selectedTypes: newSelected
    })
  },

  // 增加类型数量
  increaseTypeQuantity(e) {
    const value = e.currentTarget.dataset.value
    const updatedList = this.data.typePickerList.map(item => {
      if (item.value === value) {
        return {
          ...item,
          quantity: (item.quantity || 0) + 5,
          isSelected: true // 自动选中
        }
      }
      return item
    })

    // 获取所有选中的值
    const newSelected = updatedList.filter(item => item.isSelected).map(item => item.value)
    
    // 更新数量映射
    const quantities = {}
    updatedList.forEach(item => {
      if (item.isSelected && item.quantity > 0) {
        quantities[item.value] = item.quantity
      }
    })

    // 添加振动反馈
    wx.vibrateShort({
      type: 'light',
      fail: () => {} // 防止不支持振动的设备报错
    })

    this.setData({
      typePickerList: updatedList,
      selectedTypes: newSelected,
      typeQuantities: quantities
    })
  },

  // 减少类型数量
  decreaseTypeQuantity(e) {
    const value = e.currentTarget.dataset.value
    const updatedList = this.data.typePickerList.map(item => {
      if (item.value === value) {
        const newQuantity = Math.max((item.quantity || 0) - 5, 0)
        return {
          ...item,
          quantity: newQuantity,
          isSelected: newQuantity > 0 // 数量为0时取消选中
        }
      }
      return item
    })

    // 获取所有选中的值
    const newSelected = updatedList.filter(item => item.isSelected).map(item => item.value)
    
    // 更新数量映射
    const quantities = {}
    updatedList.forEach(item => {
      if (item.isSelected && item.quantity > 0) {
        quantities[item.value] = item.quantity
      }
    })

    // 添加振动反馈
    wx.vibrateShort({
      type: 'light',
      fail: () => {} // 防止不支持振动的设备报错
    })

    this.setData({
      typePickerList: updatedList,
      selectedTypes: newSelected,
      typeQuantities: quantities
    })
  },

  // 关闭弹窗
  closeTypePicker() {
    this.setData({ showTypePicker: false })
  },

  // 点击开始，进入guess页面
  onTypePickerStart() {
    if (this.data.selectedTypes.length === 0) {
      wx.showToast({ title: '请至少选择一个类型', icon: 'none' })
      return
    }

    // 竞技模式下检查是否有数量选择
    if (this.data.isCompetitionMode) {
      const hasQuantity = Object.values(this.data.typeQuantities).some(qty => qty > 0)
      if (!hasQuantity) {
        wx.showToast({ title: '请至少选择一种题型的数量', icon: 'none' })
        return
      }
    }

    // 获取选中类型的标签名称
    const selectedLabels = this.data.typePickerList
      .filter(item => item.isSelected)
      .map(item => item.label)

    const types = this.data.selectedTypes.join(',')
    this.setData({ showTypePicker: false })

    // 显示开始提示
    let toastTitle = ''
    if (this.data.isCompetitionMode) {
      toastTitle = '开始同题竞技挑战'
    } else {
      toastTitle = `开始${selectedLabels.join('、')}挑战`
    }

    wx.showToast({
      title: toastTitle,
      icon: 'success',
      duration: 1500
    })

    // 稍微延迟跳转，让用户看到提示
    setTimeout(() => {
      if (this.data.isCompetitionMode) {
        this.goToCompetitionPage(types, this.data.typeQuantities)
      } else {
        this.goToGuessPage(types)
      }
    }, 500)
  },

  // 跳转到guess页面
  async goToGuessPage(subType) { // 保持 async
    // 检查登录状态
    const status = authManager.checkLoginStatus() // 假设 checkLoginStatus 是同步或返回 Promise

    // 未登录，显示登录提示
    if (!status.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '登录后可以记录答题成绩和查看排行榜',
        confirmText: '去登录',
        cancelText: '暂不登录',
        success: (res) => {
          if (res.confirm) {
            this.doLogin()
          } else {
            // 不登录直接进入游戏
            app.globalData.currentRiddleType = subType
            wx.navigateTo({
              url: '/pages/guess/guess?type=' + subType
            })
          }
        }
      })
      return
    }

    // 已登录，直接进入游戏
    app.globalData.currentRiddleType = subType
    wx.navigateTo({
      url: '/pages/guess/guess?type=' + subType
    })
  },

  // 跳转到竞技模式页面
  async goToCompetitionPage(types, quantities) {
    // 检查登录状态
    const status = authManager.checkLoginStatus()

    // 未登录，显示登录提示
    if (!status.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '同题竞技模式需要登录才能参与排行榜',
        confirmText: '去登录',
        cancelText: '暂不登录',
        success: (res) => {
          if (res.confirm) {
            this.doLogin()
          }
        }
      })
      return
    }

    // 已登录，进入竞技模式
    app.globalData.competitionTypes = types
    app.globalData.competitionQuantities = quantities
    
    wx.navigateTo({
      url: '/pages/competition/competition?types=' + types
    })
  },

  // 跳转到排行榜
  navigateToRanking() {
    wx.navigateTo({
      url: '/pages/ranking/ranking'
    })
  },

  // 跳转到个人中心
  navigateToProfile() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    })
  },

  // 跳转到资料设置页面
  navigateToProfileSetup() {
    wx.navigateTo({
      url: '/pages/profile-setup/profile-setup?edit=true'
    })
  },

  // 跳转到首页
  navigateToHome() {
    wx.reLaunch({
      url: '/pages/index/index'
    })
  },

  // 分享功能
  onShareAppMessage() {
    const { userStats } = this.data
    return {
      title: `猜谜识图 All in 1 - 来挑战你的知识储备！`,
      path: '/pages/index/index',
      imageUrl: '/assets/images/share.png'
    }
  },

  // 下拉刷新
  async onPullDownRefresh() { // 保持 async
    // 刷新用户信息和统计数据
    await this.refreshUserInfo() // await refreshUserInfo 是可以的，如果 checkLoginStatus 返回 Promise
    wx.stopPullDownRefresh()
  },

  // 空方法用于catchtap防止报错
  noop() {},
}) 