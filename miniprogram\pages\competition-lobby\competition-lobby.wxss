/* 竞技大厅页面样式 */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
  padding-bottom: 140rpx; /* 为底部按钮留出空间 */
  position: relative;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 10rpx 20rpx 0 20rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  text-shadow:
    -1px -1px 0 #fff,
    1px -1px 0 #fff,
    -1px 1px 0 #fff,
    1px 1px 0 #fff,
    2px 2px 4px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  text-shadow:
    -1px -1px 0 #fff,
    1px -1px 0 #fff,
    -1px 1px 0 #fff,
    1px 1px 0 #fff,
    2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* 题库列表区域 */
.competitions-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx 15rpx 30rpx 15rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

/* 为同题竞技大厅板块添加浅色背景 */
.competitions-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #ffffff 100%);
  border: 2rpx solid #e9ecef;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.title-desc {
  font-size: 24rpx;
  color: #666;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 题库列表 */
.competitions-list {
  /* 列表容器样式 */
}

.competition-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx 16rpx;
  margin-bottom: 16rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.competition-item:active {
  transform: scale(0.98);
}

.competition-item.selected {
  background: rgba(255, 249, 196, 0.3);
  border-color: #ffd54f;
  box-shadow: 0 4rpx 16rpx rgba(255, 213, 79, 0.2);
}



/* 基本信息区域 */
.competition-basic {
  margin-bottom: 8rpx;
}

.basic-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.library-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.creator-name {
  font-size: 24rpx;
  color: #666;
  flex-shrink: 0;
  white-space: nowrap;
}

.library-stats {
  display: flex;
  gap: 8rpx;
  flex-shrink: 0;
}

.stat-item {
  font-size: 22rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  white-space: nowrap;
}

/* 详细信息区域 */
.competition-details {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.detail-section {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.competition-types {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.type-tag {
  font-size: 22rpx;
  color: #333;
  font-weight: 500;
  padding: 8rpx 12rpx;
  border-radius: 14rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.detail-info {
  /* 详细信息容器 */
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
  font-size: 24rpx;
}

.detail-label {
  color: #666;
}

.detail-value {
  color: #333;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  background: #ffffff;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.primary-btn {
  background: linear-gradient(135deg, #fff9c4, #ffecb3);
  border: 2rpx solid #ffd54f;
  box-shadow: 0 4rpx 16rpx rgba(255, 213, 79, 0.3);
}

.primary-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(255, 213, 79, 0.3);
}

.challenge-btn {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.challenge-btn.disabled {
  opacity: 0.5;
}

.challenge-btn.active {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border: 2rpx solid #2196f3;
  box-shadow: 0 4rpx 16rpx rgba(33, 150, 243, 0.3);
}

.challenge-btn.active:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.3);
}

.btn-text {
  font-size: 32rpx;
  font-weight: 500;
}

.primary-btn .btn-text {
  color: #333;
  font-weight: bold;
}

.challenge-btn .btn-text {
  color: #666;
  font-weight: 500;
}

.challenge-btn.active .btn-text {
  color: #2196f3;
  font-weight: bold;
}

/* 弹窗样式 - 复用主页的样式 */
.type-picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.type-picker-popup {
  background: #ffffff;
  border-radius: 20rpx;
  width: 95%;
  max-width: 700rpx;
  max-height: 85vh;
  overflow: hidden;
  animation: popup-in 0.3s ease-out;
}

@keyframes popup-in {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.type-picker-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.type-picker-list {
  max-height: 40vh;
  overflow-y: auto;
  padding: 20rpx 15rpx 10rpx 15rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.type-picker-item-competition {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14rpx 16rpx;
  margin-bottom: 12rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  width: calc(50% - 6rpx);
  box-sizing: border-box;
  min-height: 80rpx;
}

.type-picker-item-competition.active {
  background: rgba(255, 249, 196, 0.8);
  border: 2rpx solid #ffd54f;
  box-shadow: 0 2rpx 8rpx rgba(255, 213, 79, 0.3);
}

.type-item-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.type-item-quantity-selector {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-left: 8rpx;
}

.quantity-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.quantity-btn.decrease {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

.quantity-btn.increase {
  background: #667eea;
  color: #ffffff;
}

.quantity-btn:active {
  transform: scale(0.9);
}

.quantity-value {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  min-width: 40rpx;
  text-align: center;
}

/* 题库名称输入区域 */
.competition-name-section {
  padding: 0 20rpx;
  background: #fafafa;
  margin: 20rpx 0;
}

.name-input-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.name-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.required {
  color: #ff4757;
  font-weight: bold;
}

.name-input-container {
  flex: 1;
  position: relative;
}

.name-input {
  width: 100%;
  height: 80rpx;
  background: #ffffff;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 20rpx 0 20rpx;
  padding-right: 80rpx; /* 为计数器留出空间 */
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.name-input:focus {
  border-color: #ffd54f;
  box-shadow: 0 0 0 4rpx rgba(255, 213, 79, 0.2);
}

.name-counter {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 22rpx;
  color: #999;
  pointer-events: none;
  z-index: 1;
}

.type-picker-divider {
  height: 2rpx;
  background: #f0f0f0;
}

.type-picker-btns {
  display: flex;
  height: 100rpx;
}

.type-picker-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.type-picker-btn:first-child {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.type-picker-btn.start {
  color: #667eea;
  font-weight: bold;
}

.type-picker-btn:active {
  background: #f8f9fa;
}
