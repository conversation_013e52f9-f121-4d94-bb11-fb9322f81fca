const express = require('express');
const router = express.Router();
const db = require('../utils/db');
const { validateOpenid } = require('../middleware/auth');

// 提交答题记录并更新用户统计数据
router.post('/submit', validateOpenid, async (req, res) => {
  const { openid, riddle_id, user_answer, is_correct, score, time_used, table_name } = req.body;
  
  console.log('收到答题提交请求:', {
    openid,
    riddle_id,
    user_answer,
    is_correct,
    score,
    time_used,
    table_name
  });
  
  if (!openid || !riddle_id || user_answer === undefined || is_correct === undefined) {
    console.error('参数不完整:', req.body);
    return res.json({ code: 1, msg: '参数不完整' });
  }

  const connection = await db.pool.getConnection();
  console.log('获取数据库连接成功');
  
  try {
    console.log('开始事务');
    await connection.beginTransaction();

    // 1. 插入答题记录
    console.log('准备插入答题记录');
    const [result] = await connection.execute(
      `INSERT INTO user_answers 
       (user_id, riddle_id, table_name, user_answer, is_correct, score, time_used, created_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`,
      [openid, riddle_id, table_name, user_answer, is_correct, score, time_used]
    );
    console.log('答题记录插入成功:', result);

    // 2. 调用存储过程更新用户统计数据
    console.log('准备调用存储过程更新用户统计');
    await connection.execute(
      'CALL update_user_stats(?, ?, ?)',
      [openid, score, is_correct]
    );
    console.log('存储过程调用成功');

    // 3. 获取更新后的用户数据
    console.log('获取更新后的用户数据');
    const [userData] = await connection.execute(
      `SELECT 
        total_score, total_answers, correct_answers,
        today_score, today_answers, today_correct_answers,
        week_score, week_answers, week_correct_answers,
        month_score, month_answers, month_correct_answers
       FROM users WHERE id = ?`,
      [openid]
    );
    console.log('用户数据更新结果:', userData[0]);

    console.log('提交事务');
    await connection.commit();

    res.json({
      code: 0,
      data: {
        record_id: result.insertId,
        user_stats: userData[0]
      }
    });
    console.log('答题提交处理完成');

  } catch (err) {
    console.error('提交答题记录失败:', err);
    console.error('错误详情:', {
      message: err.message,
      code: err.code,
      sqlMessage: err.sqlMessage,
      sqlState: err.sqlState
    });
    
    await connection.rollback();
    console.log('事务已回滚');
    
    res.json({ 
      code: 1, 
      msg: '提交答题记录失败', 
      error: err.message 
    });
  } finally {
    console.log('释放数据库连接');
    connection.release();
  }
});

// 获取用户答题历史
router.get('/history', validateOpenid, async (req, res) => {
  const { openid } = req.query;
  const { page = 1, size = 20 } = req.query;
  const offset = (page - 1) * size;

  try {
    const [records] = await db.execute(
      `SELECT 
        ua.*, 
        r.question, r.answer, r.type, r.riddle_type,
        DATE_FORMAT(ua.created_at, '%Y-%m-%d %H:%i:%s') as answer_time
       FROM user_answers ua
       LEFT JOIN riddles r ON ua.riddle_id = r.id
       WHERE ua.user_id = ?
       ORDER BY ua.created_at DESC
       LIMIT ? OFFSET ?`,
      [openid, parseInt(size), offset]
    );

    const [total] = await db.execute(
      'SELECT COUNT(*) as total FROM user_answers WHERE user_id = ?',
      [openid]
    );

    res.json({
      code: 0,
      data: {
        list: records,
        total: total[0].total,
        page: parseInt(page),
        size: parseInt(size)
      }
    });

  } catch (err) {
    console.error('获取答题历史失败:', err);
    res.json({ 
      code: 1, 
      msg: '获取答题历史失败', 
      error: err.message 
    });
  }
});

module.exports = router; 