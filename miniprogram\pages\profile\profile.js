// pages/profile/profile.js
// 引入环境变量
const env = require('../../env.js')
const app = getApp()
const { processUserAvatar, generateFallbackAvatar } = require('../../utils/avatarHelper')
const authManager = require('../../utils/auth')

// 全量成就列表
const ALL_ACHIEVEMENTS = [
  { id: 1, name: '初出茅庐', icon: '🌱', description: '首次答题' },
  { id: 2, name: '十全十美', icon: '🔟', description: '累计答题10次' },
  { id: 3, name: '百战成钢', icon: '💯', description: '累计答题100次' },
  { id: 4, name: '一鸣惊人', icon: '🎉', description: '首次全对' },
  { id: 5, name: '连胜达人', icon: '🔥', description: '连胜5场' },
  { id: 6, name: '学霸', icon: '🎓', description: '正确率达到90%' },
  { id: 7, name: '坚持不懈', icon: '📅', description: '连续7天答题' },
  { id: 8, name: '高分王', icon: '🏅', description: '单场得分超过100' },
  { id: 9, name: '分享达人', icon: '🤝', description: '成功分享小程序' },
  { id: 10, name: '全能王', icon: '👑', description: '获得全部成就' }
]

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    hasUserInfo: false,
    userStats: {
      totalScore: 0,
      totalAnswers: 0,
      correctAnswers: 0,
      accuracy: 0,
      currentStreak: 0,
      bestStreak: 0,
      rank: 0
    },
    recentRecords: [], // 最近答题记录
    achievements: [], // 用户成就
    dailyStats: [], // 每日统计
    currentTab: 0, // 当前选中的标签页 0:统计 1:成就 2:记录
    loading: true,
    canIUseGetUserProfile: false,
    loginLoading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查是否可以使用getUserProfile
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
    this.initAuth()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时刷新数据
    this.refreshUserData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshUserData()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { userStats } = this.data
    return {
      title: `我在猜谜游戏中获得了${userStats.totalScore}分，快来挑战吧！`,
      path: '/pages/index/index',
      imageUrl: '/assets/images/share.png'
    }
  },

  // 初始化认证状态
  async initAuth() {
    try {
      // 检查登录状态
      const status = authManager.checkLoginStatus()
      
      if (status.isLoggedIn && status.hasUserInfo) {
        this.setUserInfo(status.userInfo)
        await this.getUserStats()
      } else if (status.isLoggedIn) {
        // 有openid但没有用户信息，尝试静默登录
        const loginResult = await authManager.silentLogin()
        if (loginResult.success) {
          // 仍然没有用户信息，设置为未登录状态
          this.setData({
            hasUserInfo: false,
            loading: false
          })
        }
      } else {
        this.setData({
          hasUserInfo: false,
          loading: false
        })
      }
    } catch (error) {
      console.error('初始化认证失败:', error)
      this.setData({
        hasUserInfo: false,
        loading: false
      })
    }
  },

  // 刷新用户数据
  async refreshUserData() {
    const status = authManager.checkLoginStatus()
    
    if (status.isLoggedIn && status.hasUserInfo) {
      this.setUserInfo(status.userInfo)
      await this.getUserStats()
    } else {
      this.setData({
        hasUserInfo: false,
        loading: false
      })
    }
  },

  // 设置用户信息
  setUserInfo(userInfo) {
    // 处理用户头像
    const processedUserInfo = processUserAvatar(userInfo)
    
    this.setData({
      userInfo: processedUserInfo,
      hasUserInfo: true,
      loading: false
    })
  },

  // 微信一键登录
  async doLogin() {
    if (this.data.loginLoading) return

    this.setData({ loginLoading: true })

    try {
      wx.showLoading({ title: '登录中...' })

      // 使用简单登录流程
      const result = await authManager.simpleLogin()
      
      if (result.success) {
        if (result.needSetProfile) {
          // 需要设置资料，跳转到资料设置页面
          wx.hideLoading()
          wx.showToast({
            title: '请完善资料',
            icon: 'none'
          })
          
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/profile-setup/profile-setup'
            })
          }, 1000)
        } else {
          // 已有资料，直接设置用户信息
          this.setUserInfo(result.userInfo)
          await this.getUserStats()
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          })
        }
      } else {
        wx.showToast({
          title: result.error || '登录失败',
          icon: 'none'
        })
      }
      
    } catch (error) {
      console.error('登录失败:', error)
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
      this.setData({ loginLoading: false })
    }
  },

  // 获取用户信息（兼容旧版本）
  getUserProfile() {
    this.doLogin()
  },

  // 编辑资料
  editProfile() {
    wx.navigateTo({
      url: '/pages/profile-setup/profile-setup?edit=true'
    })
  },

  // 头像加载错误处理
  onAvatarError(e) {
    // 生成备用头像（已经是完整URL）
    const fallbackAvatar = generateFallbackAvatar(app.globalData.openid || 'default')
    
    // 更新用户信息中的头像
    const updatedUserInfo = {
      ...this.data.userInfo,
      avatarUrl: fallbackAvatar,
      avatar_url: fallbackAvatar
    }
    
    this.setData({
      userInfo: updatedUserInfo
    })
    
    // 同时更新缓存
    wx.setStorageSync('userInfo', updatedUserInfo)
  },

  // 获取用户统计数据
  async getUserStats() {
    if (!app.globalData.openid) return

    wx.request({
      url: env.API_BASE_URL + '/api/user/stats',
      method: 'GET',
      data: { openid: app.globalData.openid },
      success: (res) => {
        if (res.data && res.data.code === 0) {
          const data = res.data.data
          // console.log('答题记录数据:', data.recent_records)
          // 合成全量成就展示
          const earnedMap = {}
          data.achievements.forEach(a => { earnedMap[a.id] = a })
          const allAchievements = ALL_ACHIEVEMENTS.map(item => {
            if (earnedMap[item.id]) {
              return {
                ...item,
                earned: true,
                achieved_at: earnedMap[item.id].achieved_at
              }
            } else {
              return {
                ...item,
                earned: false,
                achieved_at: ''
              }
            }
          })
          const achievementGrid = allAchievements.slice();
          while (achievementGrid.length < 12) achievementGrid.push(null);
          this.setData({
            userStats: {
              totalScore: data.total_score || 0,
              totalAnswers: data.total_answers || 0,
              correctAnswers: data.correct_answers || 0,
              accuracy: data.total_answers > 0 ? 
                Math.round((data.correct_answers / data.total_answers) * 100) : 0,
              currentStreak: data.current_streak || 0,
              bestStreak: data.best_streak || 0,
              rank: data.rank || 0
            },
            recentRecords: data.recent_records || [],
            achievements: data.achievements || [],
            dailyStats: data.daily_stats || [],
            allAchievements: allAchievements,
            achievementGrid: achievementGrid,
            earnedCount: data.achievements.length
          })
        }
      },
      fail: (err) => {
        console.error('获取用户统计失败', err)
      }
    })
  },

  // 切换标签页
  switchTab(e) {
    const index = Number(e.currentTarget.dataset.index)
    this.setData({ currentTab: index })
  },

  // 查看成就详情
  viewAchievement(e) {
    const achievement = e.currentTarget.dataset.item
    wx.showModal({
      title: achievement.name,
      content: achievement.description,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 查看答题记录详情
  viewRecord(e) {
    const record = e.currentTarget.dataset.item
    const content = `题目：${record.question_content}\n你的答案：${record.user_answer}\n正确答案：${record.correct_answer}\n用时：${record.time_used}秒`
    
    wx.showModal({
      title: record.is_correct ? '✅ 回答正确' : '❌ 回答错误',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？退出后将无法查看个人数据',
      success: (res) => {
        if (res.confirm) {
          authManager.logout()
          
          this.setData({
            userInfo: null,
            hasUserInfo: false,
            userStats: {
              totalScore: 0,
              totalAnswers: 0,
              correctAnswers: 0,
              accuracy: 0,
              currentStreak: 0,
              bestStreak: 0,
              rank: 0
            },
            recentRecords: [],
            achievements: [],
            dailyStats: []
          })
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除所有本地缓存吗？',
      success: (res) => {
        if (res.confirm) {
          wx.clearStorageSync()
          wx.showToast({
            title: '缓存已清除',
            icon: 'success'
          })
          // 重新初始化
          this.setData({
            userInfo: null,
            hasUserInfo: false
          })
          this.initAuth()
        }
      }
    })
  }
})