-- 字谜题库示例数据
INSERT INTO word_riddles (content, answer, hint, category, difficulty, points, riddle_type, status)
VALUES
('一口咬掉牛尾巴', '告', '口+牛去尾', '会意字', 2, 100, 'riddle', 1),
('有目共睹', '者', '有+目组成', '会意字', 2, 100, 'riddle', 1),
('十字对十字，太阳对月亮', '朝', '十+十+日+月', '会意字', 3, 100, 'riddle', 1),
('一人一张口，口下长只手', '拿', '人+口+手', '会意字', 2, 100, 'riddle', 1),
('山上还有山', '出', '山+山', '会意字', 1, 100, 'riddle', 1),
('十个哥哥', '克', '十+兄=克', '会意字', 2, 100, 'riddle', 1),
('四个人搬个木头', '杰', '四个人+木', '会意字', 2, 100, 'riddle', 1),
('一人在内', '肉', '人在内部', '会意字', 2, 100, 'riddle', 1),
('一加一', '王', '一+一=王', '会意字', 1, 100, 'riddle', 1),
('七十二小时', '晶', '三个日', '会意字', 3, 100, 'riddle', 1);

INSERT INTO idiom_riddles (content, type, answer, analysis, difficulty, points, options, riddle_type, status)
VALUES
('画蛇添足', 'text', '多此一举', '比喻做了多余的事，反而把事情弄糟', 2, 100, NULL, 'idiom', 1),
('守株待兔', 'text', '死守经验', '比喻死守狭隘经验，不知变通', 2, 100, NULL, 'idiom', 1),
('亡羊补牢', 'text', '及时补救', '比喻出了问题后及时补救', 2, 100, NULL, 'idiom', 1),
('掩耳盗铃', 'text', '自欺欺人', '比喻自己欺骗自己', 2, 100, NULL, 'idiom', 1),
('井底之蛙', 'text', '眼界狭小', '比喻见识狭窄', 2, 100, NULL, 'idiom', 1),
('狐假虎威', 'text', '仗势欺人', '比喻依仗别人的势力欺压人', 2, 100, NULL, 'idiom', 1),
('自相矛盾', 'text', '说话矛盾', '比喻自己说话做事前后抵触', 2, 100, NULL, 'idiom', 1),
('刻舟求剑', 'text', '死守教条', '比喻不懂变通', 2, 100, NULL, 'idiom', 1),
('杯弓蛇影', 'text', '疑神疑鬼', '比喻疑虑重重', 2, 100, NULL, 'idiom', 1),
('指鹿为马', 'text', '颠倒黑白', '比喻故意颠倒是非', 2, 100, NULL, 'idiom', 1);

INSERT INTO xiehouyu_riddles (content, type, answer, analysis, difficulty, points, options, riddle_type, status)
VALUES
('哑巴吃黄连', 'text', '有苦说不出', '比喻有苦说不出', 1, 100, NULL, 'xiehouyu', 1),
('泥菩萨过江', 'text', '自身难保', '比喻自身难保', 1, 100, NULL, 'xiehouyu', 1),
('和尚打伞', 'text', '无法无天', '比喻无法无天', 1, 100, NULL, 'xiehouyu', 1),
('老鼠掉进书箱里', 'text', '咬文嚼字', '比喻过分斟酌字句', 1, 100, NULL, 'xiehouyu', 1),
('外甥打灯笼', 'text', '照舅', '比喻照旧', 1, 100, NULL, 'xiehouyu', 1),
('八仙过海', 'text', '各显神通', '比喻各自有本领', 1, 100, NULL, 'xiehouyu', 1),
('兔子尾巴', 'text', '长不了', '比喻不能长久', 1, 100, NULL, 'xiehouyu', 1),
('竹篮打水', 'text', '一场空', '比喻白费力气', 1, 100, NULL, 'xiehouyu', 1),
('孔夫子搬家', 'text', '净是书', '比喻全是书', 1, 100, NULL, 'xiehouyu', 1),
('小葱拌豆腐', 'text', '一清二白', '比喻清白无瑕', 1, 100, NULL, 'xiehouyu', 1);

INSERT INTO animal_riddles (content, type, answer, analysis, difficulty, points, options, riddle_type, status)
VALUES
('头上有角，身上有毛，爱吃青草会产奶', 'text', '牛', '描述牛的特征', 1, 100, NULL, 'animal', 1),
('会飞不会走，爱吃虫子会筑巢', 'text', '燕子', '描述燕子的特征', 1, 100, NULL, 'animal', 1),
('会游泳不会飞，爱吃竹子会打洞', 'text', '熊猫', '描述熊猫的特征', 2, 100, NULL, 'animal', 1),
('身穿铠甲，行动缓慢，喜欢吃菜叶', 'text', '乌龟', '描述乌龟的特征', 1, 100, NULL, 'animal', 1),
('身上有斑点，跑得很快，是草原之王', 'text', '猎豹', '描述猎豹的特征', 2, 100, NULL, 'animal', 1),
('会变色，舌头长，喜欢吃虫子', 'text', '变色龙', '描述变色龙的特征', 2, 100, NULL, 'animal', 1),
('会爬树，喜欢吃香蕉，尾巴长', 'text', '猴子', '描述猴子的特征', 1, 100, NULL, 'animal', 1),
('会游泳，嘴巴扁，喜欢吃鱼', 'text', '鸭子', '描述鸭子的特征', 1, 100, NULL, 'animal', 1),
('会飞，晚上活动，喜欢吃蚊子', 'text', '蝙蝠', '描述蝙蝠的特征', 2, 100, NULL, 'animal', 1),
('身上有袋，跳跃能力强，是澳大利亚的代表动物', 'text', '袋鼠', '描述袋鼠的特征', 2, 100, NULL, 'animal', 1);

INSERT INTO fruit_riddles (content, type, answer, analysis, difficulty, points, options, riddle_type, status)
VALUES
('红红的，圆圆的，甜甜的，会流汁', 'text', '苹果', '描述苹果的特征', 1, 100, NULL, 'fruit', 1),
('黄黄的，弯弯的，甜甜的，会剥皮', 'text', '香蕉', '描述香蕉的特征', 1, 100, NULL, 'fruit', 1),
('红红的，圆圆的，酸酸的，会吐籽', 'text', '西瓜', '描述西瓜的特征', 1, 100, NULL, 'fruit', 1),
('紫色的，一串串的，皮薄多汁', 'text', '葡萄', '描述葡萄的特征', 1, 100, NULL, 'fruit', 1),
('外表毛茸茸，里面黄澄澄，味道酸甜', 'text', '猕猴桃', '描述猕猴桃的特征', 2, 100, NULL, 'fruit', 1),
('外皮橙色，果肉分瓣，富含维C', 'text', '橙子', '描述橙子的特征', 1, 100, NULL, 'fruit', 1),
('外皮红色，果肉白色，籽多，味甜', 'text', '荔枝', '描述荔枝的特征', 2, 100, NULL, 'fruit', 1),
('外皮黄绿，果肉脆甜，常见于秋季', 'text', '梨', '描述梨的特征', 1, 100, NULL, 'fruit', 1),
('外皮紫黑，果肉白，籽大', 'text', '山竹', '描述山竹的特征', 2, 100, NULL, 'fruit', 1),
('外皮红色，果肉多汁，常做果酱', 'text', '草莓', '描述草莓的特征', 1, 100, NULL, 'fruit', 1);

INSERT INTO place_riddles (content, type, answer, analysis, is_domestic, difficulty, points, options, riddle_type, status)
VALUES
('上有天堂，下有苏杭', 'text', '杭州', '中国著名旅游城市', 1, 2, 100, NULL, 'place', 1),
('东方巴黎', 'text', '上海', '中国经济中心', 1, 2, 100, NULL, 'place', 1),
('天府之国', 'text', '四川', '中国著名美食之都', 1, 2, 100, NULL, 'place', 1),
('自由女神像所在地', 'text', '纽约', '美国著名城市', 0, 2, 100, NULL, 'place', 1),
('铁塔之都', 'text', '巴黎', '法国首都', 0, 2, 100, NULL, 'place', 1),
('袋鼠之国', 'text', '澳大利亚', '大洋洲国家', 0, 2, 100, NULL, 'place', 1),
('金字塔之国', 'text', '埃及', '非洲著名国家', 0, 2, 100, NULL, 'place', 1),
('樱花之国', 'text', '日本', '亚洲岛国', 0, 2, 100, NULL, 'place', 1),
('冰雪之都', 'text', '哈尔滨', '中国著名冰雪城市', 1, 2, 100, NULL, 'place', 1),
('熊猫故乡', 'text', '成都', '中国四川省省会', 1, 2, 100, NULL, 'place', 1);

INSERT INTO person_riddles (content, type, answer, analysis, is_domestic, difficulty, points, options, riddle_type, status)
VALUES
('诗仙', 'text', '李白', '中国唐代著名诗人', 1, 2, 100, NULL, 'person', 1),
('诗圣', 'text', '杜甫', '中国唐代著名诗人', 1, 2, 100, NULL, 'person', 1),
('书圣', 'text', '王羲之', '中国东晋著名书法家', 1, 2, 100, NULL, 'person', 1),
('发明大王', 'text', '爱迪生', '美国著名发明家', 0, 2, 100, NULL, 'person', 1),
('相对论创立者', 'text', '爱因斯坦', '德国著名物理学家', 0, 2, 100, NULL, 'person', 1),
('苹果公司创始人', 'text', '乔布斯', '美国著名企业家', 0, 2, 100, NULL, 'person', 1),
('中国航天之父', 'text', '钱学森', '中国著名科学家', 1, 2, 100, NULL, 'person', 1),
('中国女排主教练', 'text', '郎平', '中国著名排球运动员', 1, 2, 100, NULL, 'person', 1),
('钢琴诗人', 'text', '肖邦', '波兰著名作曲家', 0, 2, 100, NULL, 'person', 1),
('世界足球先生', 'text', '梅西', '阿根廷著名足球运动员', 0, 2, 100, NULL, 'person', 1);

INSERT INTO tang_poetry_riddles (content, type, answer, analysis, difficulty, points, options, riddle_type, status)
VALUES
('床前明月光，疑是地上霜。举头望明月，低头思故乡。', 'text', '静夜思', '李白的名作，描写思乡之情', 1, 100, NULL, 'tang_poetry', 1),
('春眠不觉晓，处处闻啼鸟。夜来风雨声，花落知多少。', 'text', '春晓', '孟浩然的名作，描写春天早晨', 1, 100, NULL, 'tang_poetry', 1),
('白日依山尽，黄河入海流。欲穷千里目，更上一层楼。', 'text', '登鹳雀楼', '王之涣的名作，描写登高望远', 1, 100, NULL, 'tang_poetry', 1),
('两个黄鹂鸣翠柳，一行白鹭上青天。', 'text', '绝句', '杜甫的名作，描写春景', 1, 100, NULL, 'tang_poetry', 1),
('黄四娘家花满蹊，千朵万朵压枝低。', 'text', '江畔独步寻花', '杜甫的名作，描写春花', 1, 100, NULL, 'tang_poetry', 1),
('会当凌绝顶，一览众山小。', 'text', '望岳', '杜甫的名作，描写泰山壮丽', 1, 100, NULL, 'tang_poetry', 1),
('劝君更尽一杯酒，西出阳关无故人。', 'text', '送元二使安西', '王维的名作，送别诗', 1, 100, NULL, 'tang_poetry', 1),
('孤帆远影碧空尽，唯见长江天际流。', 'text', '黄鹤楼送孟浩然之广陵', '李白的名作，送别诗', 1, 100, NULL, 'tang_poetry', 1),
('大漠孤烟直，长河落日圆。', 'text', '使至塞上', '王维的名作，边塞诗', 1, 100, NULL, 'tang_poetry', 1),
('桃花潭水深千尺，不及汪伦送我情。', 'text', '赠汪伦', '李白的名作，友情诗', 1, 100, NULL, 'tang_poetry', 1);

INSERT INTO song_poetry_riddles (content, type, answer, analysis, difficulty, points, options, riddle_type, status)
VALUES
('明月几时有，把酒问青天。', 'text', '水调歌头', '苏轼的名作，描写中秋思乡', 2, 100, NULL, 'song_poetry', 1),
('大江东去，浪淘尽，千古风流人物。', 'text', '念奴娇·赤壁怀古', '苏轼的名作，怀古抒怀', 2, 100, NULL, 'song_poetry', 1),
('寻寻觅觅，冷冷清清，凄凄惨惨戚戚。', 'text', '声声慢', '李清照的名作，抒发愁绪', 2, 100, NULL, 'song_poetry', 1),
('昨夜雨疏风骤，浓睡不消残酒。', 'text', '如梦令', '李清照的名作，写春愁', 2, 100, NULL, 'song_poetry', 1),
('人生若只如初见，何事秋风悲画扇。', 'text', '木兰花令', '纳兰性德的名作，抒发离愁', 2, 100, NULL, 'song_poetry', 1),
('一剪梅，红藕香残玉簟秋。', 'text', '一剪梅', '李清照的名作，写秋思', 2, 100, NULL, 'song_poetry', 1),
('众里寻他千百度，蓦然回首，那人却在灯火阑珊处。', 'text', '青玉案·元夕', '辛弃疾的名作，写元宵夜', 2, 100, NULL, 'song_poetry', 1),
('无可奈何花落去，似曾相识燕归来。', 'text', '浣溪沙', '晏殊的名作，写春愁', 2, 100, NULL, 'song_poetry', 1),
('东风夜放花千树，更吹落，星如雨。', 'text', '青玉案·元夕', '辛弃疾的名作，写元宵夜', 2, 100, NULL, 'song_poetry', 1),
('人生自是有情痴，此恨不关风与月。', 'text', '玉楼春', '欧阳修的名作，写爱情', 2, 100, NULL, 'song_poetry', 1);

INSERT INTO flag_riddles (content, type, answer, analysis, difficulty, points, options, riddle_type, status)
VALUES
('/static/images/flag/china.png', 'image', '中国', '中国国旗，红底五星', 1, 100, NULL, 'flag', 1),
('/static/images/flag/usa.png', 'image', '美国', '美国国旗，星条旗', 1, 100, NULL, 'flag', 1),
('/static/images/flag/japan.png', 'image', '日本', '日本国旗，白底红圆', 1, 100, NULL, 'flag', 1),
('/static/images/flag/france.png', 'image', '法国', '法国国旗，蓝白红三色', 1, 100, NULL, 'flag', 1),
('/static/images/flag/germany.png', 'image', '德国', '德国国旗，黑红黄三色', 1, 100, NULL, 'flag', 1),
('/static/images/flag/uk.png', 'image', '英国', '英国国旗，米字旗', 1, 100, NULL, 'flag', 1),
('/static/images/flag/russia.png', 'image', '俄罗斯', '俄罗斯国旗，白蓝红三色', 1, 100, NULL, 'flag', 1),
('/static/images/flag/brazil.png', 'image', '巴西', '巴西国旗，绿色黄色', 1, 100, NULL, 'flag', 1),
('/static/images/flag/australia.png', 'image', '澳大利亚', '澳大利亚国旗，蓝底星星', 1, 100, NULL, 'flag', 1),
('/static/images/flag/canada.png', 'image', '加拿大', '加拿大国旗，枫叶', 1, 100, NULL, 'flag', 1);

INSERT INTO map_outline_riddles (content, type, answer, analysis, is_domestic, difficulty, points, options, riddle_type, status)
VALUES
('/static/images/map_outline/china.png', 'image', '中国', '中国地图轮廓', 1, 1, 100, NULL, 'map_outline', 1),
('/static/images/map_outline/usa.png', 'image', '美国', '美国地图轮廓', 0, 1, 100, NULL, 'map_outline', 1),
('/static/images/map_outline/france.png', 'image', '法国', '法国地图轮廓', 0, 1, 100, NULL, 'map_outline', 1),
('/static/images/map_outline/japan.png', 'image', '日本', '日本地图轮廓', 0, 1, 100, NULL, 'map_outline', 1),
('/static/images/map_outline/brazil.png', 'image', '巴西', '巴西地图轮廓', 0, 1, 100, NULL, 'map_outline', 1),
('/static/images/map_outline/australia.png', 'image', '澳大利亚', '澳大利亚地图轮廓', 0, 1, 100, NULL, 'map_outline', 1),
('/static/images/map_outline/uk.png', 'image', '英国', '英国地图轮廓', 0, 1, 100, NULL, 'map_outline', 1),
('/static/images/map_outline/russia.png', 'image', '俄罗斯', '俄罗斯地图轮廓', 0, 1, 100, NULL, 'map_outline', 1),
('/static/images/map_outline/india.png', 'image', '印度', '印度地图轮廓', 0, 1, 100, NULL, 'map_outline', 1),
('/static/images/map_outline/italy.png', 'image', '意大利', '意大利地图轮廓', 0, 1, 100, NULL, 'map_outline', 1);

INSERT INTO car_logo_riddles (content, type, answer, analysis, is_domestic, difficulty, points, options, riddle_type, status)
VALUES
('/static/images/car_logo/bmw.png', 'image', '宝马', '德国汽车品牌', 0, 1, 100, NULL, 'car_logo', 1),
('/static/images/car_logo/audi.png', 'image', '奥迪', '德国汽车品牌', 0, 1, 100, NULL, 'car_logo', 1),
('/static/images/car_logo/benz.png', 'image', '奔驰', '德国汽车品牌', 0, 1, 100, NULL, 'car_logo', 1),
('/static/images/car_logo/toyota.png', 'image', '丰田', '日本汽车品牌', 0, 1, 100, NULL, 'car_logo', 1),
('/static/images/car_logo/honda.png', 'image', '本田', '日本汽车品牌', 0, 1, 100, NULL, 'car_logo', 1),
('/static/images/car_logo/tesla.png', 'image', '特斯拉', '美国汽车品牌', 0, 1, 100, NULL, 'car_logo', 1),
('/static/images/car_logo/ford.png', 'image', '福特', '美国汽车品牌', 0, 1, 100, NULL, 'car_logo', 1),
('/static/images/car_logo/byd.png', 'image', '比亚迪', '中国汽车品牌', 1, 1, 100, NULL, 'car_logo', 1),
('/static/images/car_logo/hongqi.png', 'image', '红旗', '中国汽车品牌', 1, 1, 100, NULL, 'car_logo', 1),
('/static/images/car_logo/geely.png', 'image', '吉利', '中国汽车品牌', 1, 1, 100, NULL, 'car_logo', 1);

INSERT INTO brand_logo_riddles (content, type, answer, analysis, is_domestic, difficulty, points, options, riddle_type, status)
VALUES
('/static/images/brand_logo/apple.png', 'image', '苹果', '美国科技品牌', 0, 1, 100, NULL, 'brand_logo', 1),
('/static/images/brand_logo/huawei.png', 'image', '华为', '中国科技品牌', 1, 1, 100, NULL, 'brand_logo', 1),
('/static/images/brand_logo/samsung.png', 'image', '三星', '韩国科技品牌', 0, 1, 100, NULL, 'brand_logo', 1),
('/static/images/brand_logo/lenovo.png', 'image', '联想', '中国科技品牌', 1, 1, 100, NULL, 'brand_logo', 1),
('/static/images/brand_logo/sony.png', 'image', '索尼', '日本科技品牌', 0, 1, 100, NULL, 'brand_logo', 1),
('/static/images/brand_logo/microsoft.png', 'image', '微软', '美国科技品牌', 0, 1, 100, NULL, 'brand_logo', 1),
('/static/images/brand_logo/tencent.png', 'image', '腾讯', '中国互联网公司', 1, 1, 100, NULL, 'brand_logo', 1),
('/static/images/brand_logo/alibaba.png', 'image', '阿里巴巴', '中国互联网公司', 1, 1, 100, NULL, 'brand_logo', 1),
('/static/images/brand_logo/baidu.png', 'image', '百度', '中国互联网公司', 1, 1, 100, NULL, 'brand_logo', 1),
('/static/images/brand_logo/google.png', 'image', '谷歌', '美国互联网公司', 0, 1, 100, NULL, 'brand_logo', 1);

INSERT INTO movie_still_riddles (content, type, answer, analysis, difficulty, points, options, riddle_type, status)
VALUES
('/static/images/movie_still/titanic.jpg', 'image', '泰坦尼克号', '经典爱情电影', 1, 100, NULL, 'movie_still', 1),
('/static/images/movie_still/avatar.jpg', 'image', '阿凡达', '科幻电影', 1, 100, NULL, 'movie_still', 1),
('/static/images/movie_still/harrypotter.jpg', 'image', '哈利波特', '魔法题材电影', 1, 100, NULL, 'movie_still', 1),
('/static/images/movie_still/inception.jpg', 'image', '盗梦空间', '科幻悬疑电影', 1, 100, NULL, 'movie_still', 1),
('/static/images/movie_still/forrestgump.jpg', 'image', '阿甘正传', '励志电影', 1, 100, NULL, 'movie_still', 1),
('/static/images/movie_still/transformers.jpg', 'image', '变形金刚', '科幻动作电影', 1, 100, NULL, 'movie_still', 1),
('/static/images/movie_still/lotr.jpg', 'image', '指环王', '奇幻史诗电影', 1, 100, NULL, 'movie_still', 1),
('/static/images/movie_still/starwars.jpg', 'image', '星球大战', '科幻电影', 1, 100, NULL, 'movie_still', 1),
('/static/images/movie_still/avengers.jpg', 'image', '复仇者联盟', '超级英雄电影', 1, 100, NULL, 'movie_still', 1),
('/static/images/movie_still/spiritedaway.jpg', 'image', '千与千寻', '日本动画电影', 1, 100, NULL, 'movie_still', 1);

INSERT INTO app_icon_riddles (content, type, answer, analysis, is_domestic, difficulty, points, options, riddle_type, status)
VALUES
('/static/images/app_icon/wechat.png', 'image', '微信', '中国社交APP', 1, 1, 100, NULL, 'app_icon', 1),
('/static/images/app_icon/qq.png', 'image', 'QQ', '中国社交APP', 1, 1, 100, NULL, 'app_icon', 1),
('/static/images/app_icon/alipay.png', 'image', '支付宝', '中国支付APP', 1, 1, 100, NULL, 'app_icon', 1),
('/static/images/app_icon/taobao.png', 'image', '淘宝', '中国购物APP', 1, 1, 100, NULL, 'app_icon', 1),
('/static/images/app_icon/douyin.png', 'image', '抖音', '中国短视频APP', 1, 1, 100, NULL, 'app_icon', 1),
('/static/images/app_icon/instagram.png', 'image', 'Instagram', '美国社交APP', 0, 1, 100, NULL, 'app_icon', 1),
('/static/images/app_icon/facebook.png', 'image', 'Facebook', '美国社交APP', 0, 1, 100, NULL, 'app_icon', 1),
('/static/images/app_icon/twitter.png', 'image', 'Twitter', '美国社交APP', 0, 1, 100, NULL, 'app_icon', 1),
('/static/images/app_icon/whatsapp.png', 'image', 'WhatsApp', '美国通讯APP', 0, 1, 100, NULL, 'app_icon', 1),
('/static/images/app_icon/youtube.png', 'image', 'YouTube', '美国视频APP', 0, 1, 100, NULL, 'app_icon', 1);

INSERT INTO audio_song_riddles (content, type, answer, analysis, difficulty, points, options, riddle_type, status)
VALUES
('/static/audio/song/moon.mp3', 'audio', '月亮代表我的心', '邓丽君经典歌曲', 1, 100, NULL, 'audio_song', 1),
('/static/audio/song/qinghuaci.mp3', 'audio', '青花瓷', '周杰伦中国风歌曲', 1, 100, NULL, 'audio_song', 1),
('/static/audio/song/letitgo.mp3', 'audio', 'Let It Go', '冰雪奇缘主题曲', 1, 100, NULL, 'audio_song', 1),
('/static/audio/song/youth.mp3', 'audio', '青春修炼手册', 'TFBOYS代表作', 1, 100, NULL, 'audio_song', 1),
('/static/audio/song/shapeofyou.mp3', 'audio', 'Shape of You', 'Ed Sheeran流行歌曲', 1, 100, NULL, 'audio_song', 1),
('/static/audio/song/seeYouAgain.mp3', 'audio', 'See You Again', '速度与激情主题曲', 1, 100, NULL, 'audio_song', 1),
('/static/audio/song/hello.mp3', 'audio', 'Hello', 'Adele流行歌曲', 1, 100, NULL, 'audio_song', 1),
('/static/audio/song/uptownfunk.mp3', 'audio', 'Uptown Funk', 'Bruno Mars流行歌曲', 1, 100, NULL, 'audio_song', 1),
('/static/audio/song/tiankong.mp3', 'audio', '天空之城', '久石让经典钢琴曲', 1, 100, NULL, 'audio_song', 1),
('/static/audio/song/daoxiang.mp3', 'audio', '稻香', '周杰伦励志歌曲', 1, 100, NULL, 'audio_song', 1);

INSERT INTO audio_animal_riddles (content, type, answer, analysis, difficulty, points, options, riddle_type, status)
VALUES
('/static/audio/animal/cat.mp3', 'audio', '猫', '猫的叫声', 1, 100, NULL, 'audio_animal', 1),
('/static/audio/animal/dog.mp3', 'audio', '狗', '狗的叫声', 1, 100, NULL, 'audio_animal', 1),
('/static/audio/animal/cow.mp3', 'audio', '牛', '牛的叫声', 1, 100, NULL, 'audio_animal', 1),
('/static/audio/animal/horse.mp3', 'audio', '马', '马的嘶鸣', 1, 100, NULL, 'audio_animal', 1),
('/static/audio/animal/sheep.mp3', 'audio', '羊', '羊的叫声', 1, 100, NULL, 'audio_animal', 1),
('/static/audio/animal/duck.mp3', 'audio', '鸭子', '鸭子的叫声', 1, 100, NULL, 'audio_animal', 1),
('/static/audio/animal/chicken.mp3', 'audio', '鸡', '鸡的叫声', 1, 100, NULL, 'audio_animal', 1),
('/static/audio/animal/elephant.mp3', 'audio', '大象', '大象的叫声', 1, 100, NULL, 'audio_animal', 1),
('/static/audio/animal/lion.mp3', 'audio', '狮子', '狮子的吼声', 1, 100, NULL, 'audio_animal', 1),
('/static/audio/animal/monkey.mp3', 'audio', '猴子', '猴子的叫声', 1, 100, NULL, 'audio_animal', 1);

-- 用户表示例数据
INSERT INTO users (id, nickname, avatar_url, total_score, total_answers, correct_answers)
VALUES
('user001', '小明', '/static/avatar/1.png', 1200, 50, 40),
('user002', '小红', '/static/avatar/2.png', 1100, 48, 38),
('user003', '小刚', '/static/avatar/3.png', 1050, 45, 35),
('user004', '小美', '/static/avatar/4.png', 980, 40, 32),
('user005', '小强', '/static/avatar/5.png', 900, 38, 30),
('user006', '小丽', '/static/avatar/6.png', 850, 35, 28),
('user007', '小军', '/static/avatar/7.png', 800, 33, 26),
('user008', '小芳', '/static/avatar/8.png', 780, 32, 25),
('user009', '小伟', '/static/avatar/9.png', 750, 30, 24),
('user010', '小霞', '/static/avatar/10.png', 700, 28, 22),
('user011', '猜谜高手', '/static/avatar/11.png', 2000, 80, 75),
('user012', '智慧之星', '/static/avatar/12.png', 1800, 70, 65),
('user013', '古诗达人', '/static/avatar/13.png', 1650, 60, 55),
('user014', '成语专家', '/static/avatar/14.png', 1500, 65, 58),
('user015', '音乐天才', '/static/avatar/15.png', 1350, 55, 48),
('user016', '图片猜王', '/static/avatar/16.png', 1250, 52, 45),
('user017', '文字高手', '/static/avatar/17.png', 1150, 50, 42),
('user018', '知识达人', '/static/avatar/18.png', 1050, 45, 38),
('user019', '题目杀手', '/static/avatar/19.png', 950, 42, 35),
('user020', '挑战者', '/static/avatar/20.png', 850, 40, 32);

-- 用户答题记录示例数据
INSERT INTO user_answers (user_id, riddle_id, table_name, user_answer, is_correct, score, time_used)
VALUES
-- 用户001的答题记录 (成语题库)
('user001', 1, 'idiom_riddles', '多此一举', 1, 100, 15),
('user001', 2, 'idiom_riddles', '死守经验', 1, 100, 20),
('user001', 3, 'idiom_riddles', '及时补救', 1, 100, 18),
('user001', 4, 'idiom_riddles', '自欺欺人', 1, 100, 12),
('user001', 5, 'idiom_riddles', '眼界狭小', 1, 100, 25),

-- 用户002的答题记录 (成语题库)
('user002', 1, 'idiom_riddles', '多此一举', 1, 100, 18),
('user002', 2, 'idiom_riddles', '死守经验', 1, 100, 22),
('user002', 3, 'idiom_riddles', '及时补救', 0, 0, 30),
('user002', 4, 'idiom_riddles', '自欺欺人', 1, 100, 16),
('user002', 5, 'idiom_riddles', '眼界狭小', 1, 100, 28),

-- 用户003的答题记录 (歇后语题库)
('user003', 1, 'xiehouyu_riddles', '有苦说不出', 1, 100, 10),
('user003', 2, 'xiehouyu_riddles', '自身难保', 1, 100, 12),
('user003', 3, 'xiehouyu_riddles', '无法无天', 1, 100, 15),
('user003', 4, 'xiehouyu_riddles', '咬文嚼字', 0, 0, 35),
('user003', 5, 'xiehouyu_riddles', '照舅', 1, 100, 8),

-- 用户004的答题记录 (动物题库)
('user004', 1, 'animal_riddles', '牛', 1, 100, 5),
('user004', 2, 'animal_riddles', '燕子', 1, 100, 8),
('user004', 3, 'animal_riddles', '熊猫', 0, 0, 25),
('user004', 4, 'animal_riddles', '乌龟', 1, 100, 7),
('user004', 5, 'animal_riddles', '猎豹', 1, 100, 20),

-- 用户005的答题记录 (水果题库)
('user005', 1, 'fruit_riddles', '苹果', 1, 100, 6),
('user005', 2, 'fruit_riddles', '香蕉', 1, 100, 4),
('user005', 3, 'fruit_riddles', '西瓜', 1, 100, 7),
('user005', 4, 'fruit_riddles', '葡萄', 0, 0, 20),
('user005', 5, 'fruit_riddles', '猕猴桃', 1, 100, 30),

-- 更多用户的混合答题记录
('user006', 1, 'idiom_riddles', '多此一举', 1, 100, 12),
('user006', 1, 'flag_riddles', '中国', 1, 100, 8),
('user006', 2, 'flag_riddles', '美国', 1, 100, 10),
('user006', 1, 'audio_song_riddles', '月亮代表我的心', 1, 100, 25),

('user007', 1, 'tang_poetry_riddles', '静夜思', 1, 100, 15),
('user007', 2, 'tang_poetry_riddles', '春晓', 1, 100, 18),
('user007', 1, 'car_logo_riddles', '宝马', 1, 100, 5),
('user007', 2, 'car_logo_riddles', '奥迪', 0, 0, 15),

('user008', 1, 'place_riddles', '杭州', 1, 100, 20),
('user008', 2, 'place_riddles', '上海', 1, 100, 16),
('user008', 1, 'app_icon_riddles', '微信', 1, 100, 3),
('user008', 2, 'app_icon_riddles', 'QQ', 1, 100, 4);

-- 添加用户统计信息表
CREATE TABLE IF NOT EXISTS user_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    daily_score INT DEFAULT 0 COMMENT '当日得分',
    daily_answers INT DEFAULT 0 COMMENT '当日答题数',
    daily_correct INT DEFAULT 0 COMMENT '当日正确数',
    best_streak INT DEFAULT 0 COMMENT '最佳连胜',
    current_streak INT DEFAULT 0 COMMENT '当前连胜',
    favorite_type VARCHAR(50) COMMENT '最喜欢的题型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE KEY uk_user_date (user_id, date),
    INDEX idx_date (date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户每日统计表';

-- 用户每日统计示例数据
INSERT INTO user_stats (user_id, date, daily_score, daily_answers, daily_correct, best_streak, current_streak, favorite_type)
VALUES
('user001', '2024-01-15', 500, 10, 8, 8, 3, 'idiom'),
('user001', '2024-01-14', 400, 8, 6, 6, 0, 'idiom'),
('user001', '2024-01-13', 300, 6, 5, 5, 5, 'animal'),
('user002', '2024-01-15', 450, 9, 7, 5, 2, 'xiehouyu'),
('user002', '2024-01-14', 350, 7, 5, 4, 0, 'fruit'),
('user003', '2024-01-15', 400, 8, 6, 6, 1, 'flag'),
('user004', '2024-01-15', 380, 7, 6, 4, 4, 'animal'),
('user005', '2024-01-15', 320, 6, 5, 3, 0, 'fruit');

-- 成就系统表
CREATE TABLE IF NOT EXISTS achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '成就名称',
    description TEXT COMMENT '成就描述',
    icon VARCHAR(255) COMMENT '成就图标',
    condition_type ENUM('score', 'answers', 'streak', 'accuracy', 'type_master') NOT NULL COMMENT '条件类型',
    condition_value INT NOT NULL COMMENT '条件值',
    points INT DEFAULT 0 COMMENT '成就积分',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成就表';

-- 成就数据
INSERT INTO achievements (name, description, icon, condition_type, condition_value, points)
VALUES
('初出茅庐', '完成第一道题目', '🌱', 'answers', 1, 10),
('小试牛刀', '累计答对10道题', '🔰', 'answers', 10, 20),
('渐入佳境', '累计答对50道题', '📈', 'answers', 50, 50),
('炉火纯青', '累计答对100道题', '🔥', 'answers', 100, 100),
('登峰造极', '累计答对500道题', '🏔️', 'answers', 500, 200),
('百分百', '单轮答题正确率100%', '💯', 'accuracy', 100, 30),
('连胜王', '连续答对10题', '🏆', 'streak', 10, 40),
('成语大师', '成语类题目答对50题', '📚', 'type_master', 50, 60),
('古诗词达人', '诗词类题目答对30题', '🎭', 'type_master', 30, 60),
('图像识别专家', '图片类题目答对40题', '🖼️', 'type_master', 40, 60);

-- 用户成就表
CREATE TABLE IF NOT EXISTS user_achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50) NOT NULL,
    achievement_id INT NOT NULL,
    achieved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (achievement_id) REFERENCES achievements(id),
    UNIQUE KEY uk_user_achievement (user_id, achievement_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户成就表';

-- 用户成就示例数据
INSERT INTO user_achievements (user_id, achievement_id)
VALUES
('user001', 1), ('user001', 2), ('user001', 3), ('user001', 6), ('user001', 8),
('user002', 1), ('user002', 2), ('user002', 3), ('user002', 7),
('user003', 1), ('user003', 2), ('user003', 9),
('user004', 1), ('user004', 2), ('user004', 10),
('user005', 1), ('user005', 2);
