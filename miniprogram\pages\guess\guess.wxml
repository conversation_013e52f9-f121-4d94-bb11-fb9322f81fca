<view class="container">
  <!-- 顶部信息 -->
  <view class="header" style="--progress-percent: {{progressPercent}}%;">
    <view class="score">得分: {{score}}</view>
    <view class="progress">{{currentIndex+1}}/{{totalCount}}</view>
  </view>

  <!-- 固定高度题目区域 -->
  <view class="question-area-fixed">
    <!-- 上一题答案悬浮显示 -->
    <view class="last-answer" wx:if="{{lastAnswer}}">
      <text>上一题: {{lastAnswer}}</text>
    </view>

    <!-- 连对次数悬浮显示 -->
    <view class="streak-counter">
      <text>{{streak}} 连胜</text>
    </view>

    <!-- 音频类题目的音乐信息板块 -->
    <block wx:if="{{currentQuestion.type === 'audio'}}">
      <!-- 使用自定义音频播放UI -->
      <view class="audio-container">
        <!-- 音频图标 -->
        <image src="/assets/images/music.png" class="audio-icon"></image>
        <view class="audio-info">
          <view class="audio-title">{{currentQuestion.title || '未知歌曲'}}</view>
          <view class="audio-artist">{{currentQuestion.artist || '未知作者'}}</view>
        </view>
        <view class="audio-duration">{{audioCurrentTime || '00'}} / {{audioDuration || '00:00'}}</view>
      </view>
      <!-- 音频进度条和控制区 -->
      <view class="audio-controls">
        <view class="audio-progress-bar">
          <view class="progress-time">{{audioCurrentTime || '00:00'}}</view>
          <view class="progress-bar-line"
                bindtap="onProgressBarTap"
                catchtouchstart="onProgressTouchStart"
                catchtouchmove="onProgressTouchMove"
                catchtouchend="onProgressTouchEnd">
            <view class="progress-line" style="width: {{audioProgress}}%;"></view>
            <view class="progress-dot {{isDraggingProgress ? 'dragging' : ''}}" style="left: {{audioProgress}}%;"></view>
          </view>
          <view class="progress-time">{{audioDuration || '00:00'}}</view>
        </view>
        <view class="audio-play-btn-bottom" bindtap="toggleAudioPlayback">
           <image src="/assets/images/{{isPlayingAudio ? 'pause' : 'play'}}.png" class="play-icon"></image>
        </view>
      </view>
    </block>

    <block wx:if="{{currentQuestion.type === 'image'}}">
      <view class="image-container">
        <image
          class="app-icon"
          src="{{currentQuestion.content}}"
          mode="aspectFit"
          style="height: 320rpx; width: auto;"
          webp="{{true}}"
          referrerpolicy="no-referrer"
          lazy-load="{{true}}"
          binderror="onImageError"
          bindtap="showImagePreview"
        ></image>
      </view>
    </block>
    <block wx:elif="{{currentQuestion.type === 'text'}}">
      <view class="question-text center-content">{{currentQuestion.content}}</view>
    </block>

    <!-- 答案类型和长度提示，后面追加提示内容 -->
    <view class="answer-type-tip">
      <text class="type-hint">🎯 猜一{{currentQuestion.answer_type || '谜题'}}</text>
      <text class="length-hint">（{{currentQuestion.answer.length}}个字）</text>
      <block wx:if="{{hintText}}">
        <text class="hint-content">💡 {{hintText}}</text>
      </block>
    </view>
  </view>

  <!-- 分格输入区 -->
  <view class="input-box-single">
    <view class="input-cells">
      <block wx:for="{{currentQuestion.answer.length}}" wx:key="index">
        <view class="input-cell-single{{userInputStr.length === index && userInputStr.length < currentQuestion.answer.length ? ' active' : ''}}">
          {{userInputStr[index] || ''}}
        </view>
      </block>
    </view>
    <input
      class="input-single"
      style="width: {{inputBoxWidth}}rpx;"
      maxlength="{{currentQuestion.answer.length}}"
      value="{{userInputStr}}"
      focus="true"
      bindinput="onSingleInput"
      disabled="{{showResult}}"
      confirm-hold="true"
      cursor-spacing="0"
    />
  </view>

  <!-- 按钮区 -->
  <view class="btn-area">
    <button class="skip-btn{{skipDisabled ? ' skip-btn-disabled' : ' skip-btn-active'}}{{skipCount <= 0 ? ' skip-btn-used' : ''}}" bindtap="skipQuestion" disabled="{{skipDisabled || skipCount <= 0}}">
      跳过（剩余{{skipCount}}次）
    </button>
    <button class="hint-btn" bindtap="showHint">提示（剩余{{hintCount}}次）</button>
  </view>
  <view class="hint-text">{{hintText}}</view>

  <!-- 结果显示 -->
  <view class="result-area" wx:if="{{showResult}}">
    <view class="result-text {{isCorrect ? 'correct' : 'wrong'}}">
      {{isCorrect ? '答对啦！' : '答错啦！'}}
    </view>
    <view class="correct-answer">正确答案：{{correctAnswer}}</view>
  </view>

  <!-- 哭脸动画 -->
  <image wx:if="{{showSadFace}}" class="sad-face" src="/assets/images/sad.gif" mode="aspectFit"/>

  <!-- 多彩彩带绽放动画 -->
  <view wx:if="{{showCelebrate}}" class="confetti-container">
    <view wx:for="{{confettiPieces}}" wx:key="id"
          class="confetti-piece burst-{{item.animationType}}"
          style="left: {{item.left}}%; top: {{item.top}}%; background-color: {{item.color}}; width: {{item.size}}rpx; height: {{item.size}}rpx; animation-delay: {{item.delay}}s;">
    </view>
  </view>

  <!-- 图片预览弹窗 -->
  <view class="image-preview-modal" wx:if="{{showImagePreview}}" bindtap="hideImagePreview" catchtouchmove="preventDefault">
    <view class="image-container-wrapper">
      <view class="image-transform-container" style="transform: scale({{imageScale}}) translate({{imageTranslateX}}px, {{imageTranslateY}}px);">
        <image
          class="preview-image"
          src="{{currentQuestion.content}}"
          mode="widthFix"
          catchtouchstart="handleTouchStart"
          catchtouchmove="handleTouchMove"
          catchtouchend="handleTouchEnd"
        ></image>
      </view>
    </view>
  </view>
</view> 