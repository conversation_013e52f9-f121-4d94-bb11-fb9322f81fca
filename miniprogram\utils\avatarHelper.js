/**
 * 前端头像处理工具函数
 */

const env = require('../env.js')

// 生成备用头像URL
function generateFallbackAvatar(userId) {
  // 基于用户ID生成1-75之间的固定随机数
  let hash = 0;
  if (userId) {
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
  }
  
  // 将hash转换为1-75之间的正数
  const avatarIndex = Math.abs(hash % 75) + 1;
  
  return `${env.API_BASE_URL}/static/avatar/face (${avatarIndex}).jpg`;
}

// 处理头像URL，确保是完整的URL
function processAvatarUrl(avatarUrl, userId, options = {}) {
  // 如果用户有真实头像且不为空字符串，处理头像URL
  if (avatarUrl && avatarUrl.trim() !== '') {
    let processedUrl = ''
    
    // 如果是旧的备用头像格式，直接返回null，使用新的备用头像
    if (avatarUrl.includes('/static/avatar/') && avatarUrl.endsWith('.png')) {
      return generateFallbackAvatar(userId || '')
    }
    
    // 如果已经是完整URL，直接使用
    if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
      processedUrl = avatarUrl
    } else if (avatarUrl.startsWith('/static/')) {
      // 相对路径转换为完整URL
      processedUrl = `${env.API_BASE_URL}${avatarUrl}`
    } else if (avatarUrl.startsWith('static/')) {
      // 没有前缀斜杠的相对路径
      processedUrl = `${env.API_BASE_URL}/${avatarUrl}`
    } else {
      // 其他格式，尝试拼接
      processedUrl = `${env.API_BASE_URL}${avatarUrl.startsWith('/') ? '' : '/'}${avatarUrl}`
    }
    
    // 如果是服务器头像且需要避免缓存，添加时间戳
    if (options.avoidCache && processedUrl.includes('/static/pic/')) {
      const timestamp = Date.now()
      processedUrl = processedUrl.includes('?') 
        ? `${processedUrl}&t=${timestamp}` 
        : `${processedUrl}?t=${timestamp}`
    }
    
    return processedUrl
  }
  
  // 否则使用备用头像
  return generateFallbackAvatar(userId || '')
}

// 处理用户头像，返回可用的头像URL（支持多种字段名）
function processUserAvatar(user, options = {}) {
  if (!user) return user;
  
  // 兼容多种字段名
  const avatarUrl = user.avatarUrl || user.avatar_url || user.avatarurl || '';
  const userId = user.id || user.user_id || user.openid || '';
  
  const processedAvatarUrl = processAvatarUrl(avatarUrl, userId, options);
  
  // 返回统一格式的用户对象
  return {
    ...user,
    avatarUrl: processedAvatarUrl,
    avatar_url: processedAvatarUrl
  };
}

// 批量处理用户头像
function processUsersAvatars(users) {
  if (!Array.isArray(users)) return users;
  
  return users.map(user => processUserAvatar(user));
}

module.exports = {
  generateFallbackAvatar,
  processAvatarUrl,
  processUserAvatar,
  processUsersAvatars
}; 