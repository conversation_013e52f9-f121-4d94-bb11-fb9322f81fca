const mysql = require('mysql2/promise');
const dbConfig = require('../config/db');

// 保存答题记录
exports.submitAnswer = async (data) => {
  const conn = await mysql.createConnection(dbConfig);
  const {
    openid, riddle_id, user_answer, is_correct, score, time_used
  } = data;

  // 插入答题记录
  await conn.execute(
    'INSERT INTO user_answers (user_id, riddle_id, user_answer, is_correct, score, time_used) VALUES (?, ?, ?, ?, ?, ?)',
    [openid, riddle_id, user_answer, is_correct, score, time_used]
  );

  // 更新用户积分和答题统计（如果用户存在）
  await conn.execute(
    `UPDATE users SET 
      total_score = total_score + ?,
      total_answers = total_answers + 1,
      correct_answers = correct_answers + ?
     WHERE id = ?`,
    [score, is_correct ? 1 : 0, openid]
  );

  await conn.end();
  return { success: true };
};