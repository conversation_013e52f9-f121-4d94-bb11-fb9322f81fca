-- 重构后的用户表结构
-- 版本：2.0
-- 更新日期：2024-03-21

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `guess`
--

-- --------------------------------------------------------

--
-- 临时禁用外键检查
--

SET FOREIGN_KEY_CHECKS = 0;

--
-- 表的结构 `users`
--

DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  -- 基础信息
  `id` varchar(50) NOT NULL COMMENT '用户openid',
  `nickname` varchar(100) DEFAULT NULL COMMENT '昵称',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
  
  -- 总统计数据
  `total_score` int(11) DEFAULT '0' COMMENT '总积分',
  `total_answers` int(11) DEFAULT '0' COMMENT '总答题数',
  `correct_answers` int(11) DEFAULT '0' COMMENT '总正确答题数',
  `total_updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '总数据更新时间',
  
  -- 今日统计数据
  `today_score` int(11) DEFAULT '0' COMMENT '今日积分',
  `today_answers` int(11) DEFAULT '0' COMMENT '今日答题数',
  `today_correct_answers` int(11) DEFAULT '0' COMMENT '今日正确答题数',
  `today_updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '今日数据更新时间',
  
  -- 本周统计数据
  `week_score` int(11) DEFAULT '0' COMMENT '本周积分',
  `week_answers` int(11) DEFAULT '0' COMMENT '本周答题数',
  `week_correct_answers` int(11) DEFAULT '0' COMMENT '本周正确答题数',
  `week_updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '本周数据更新时间',
  
  -- 本月统计数据
  `month_score` int(11) DEFAULT '0' COMMENT '本月积分',
  `month_answers` int(11) DEFAULT '0' COMMENT '本月答题数',
  `month_correct_answers` int(11) DEFAULT '0' COMMENT '本月正确答题数',
  `month_updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '本月数据更新时间',
  
  -- 时间戳
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  
  -- 主键和索引
  PRIMARY KEY (`id`),
  KEY `idx_total_score` (`total_score`),
  KEY `idx_today_score` (`today_score`),
  KEY `idx_week_score` (`week_score`),
  KEY `idx_month_score` (`month_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

--
-- 转存表中的数据 `users`
--

INSERT INTO `users` (
  `id`, `nickname`, `avatar_url`,
  `total_score`, `total_answers`, `correct_answers`, `total_updated_at`,
  `today_score`, `today_answers`, `today_correct_answers`, `today_updated_at`,
  `week_score`, `week_answers`, `week_correct_answers`, `week_updated_at`,
  `month_score`, `month_answers`, `month_correct_answers`, `month_updated_at`,
  `created_at`, `updated_at`
) VALUES

('oQH__6wrOuw0camNtJWY7nFLmJQo', '娃哈哈', 'https://wx.izzs.cn/static/pic/oQH__6wrOuw0camNtJWY7nFLmJQo.jpg', 
  700, 10, 7, NOW(),
  100, 2, 1, NOW(),
  300, 5, 4, NOW(),
  500, 8, 6, NOW(),
  NOW(), NOW()),


('user001', '小明', '/static/avatar/1.png', 
  1200, 50, 40, NOW(),
  150, 5, 4, NOW(),
  450, 15, 12, NOW(),
  800, 30, 25, NOW(),
  NOW(), NOW()),
('user002', '小红', '/static/avatar/2.png', 
  1100, 48, 38, NOW(),
  120, 4, 3, NOW(),
  400, 14, 11, NOW(),
  750, 28, 23, NOW(),
  NOW(), NOW()),
('user003', '小刚', '/static/avatar/3.png', 
  1050, 45, 35, NOW(),
  100, 3, 2, NOW(),
  380, 13, 10, NOW(),
  700, 25, 20, NOW(),
  NOW(), NOW()),
('user004', '小美', '/static/avatar/4.png', 
  980, 40, 32, NOW(),
  90, 3, 2, NOW(),
  350, 12, 9, NOW(),
  650, 22, 18, NOW(),
  NOW(), NOW()),
('user005', '小强', '/static/avatar/5.png', 
  900, 38, 30, NOW(),
  80, 2, 1, NOW(),
  320, 11, 8, NOW(),
  600, 20, 16, NOW(),
  NOW(), NOW()),
('user006', '小丽', '/static/avatar/6.png', 
  850, 35, 28, NOW(),
  70, 2, 1, NOW(),
  300, 10, 7, NOW(),
  550, 18, 14, NOW(),
  NOW(), NOW()),
('user007', '小军', '/static/avatar/7.png', 
  800, 33, 26, NOW(),
  60, 2, 1, NOW(),
  280, 9, 6, NOW(),
  500, 16, 12, NOW(),
  NOW(), NOW()),
('user008', '小芳', '/static/avatar/8.png', 
  780, 32, 25, NOW(),
  50, 1, 1, NOW(),
  250, 8, 5, NOW(),
  450, 14, 10, NOW(),
  NOW(), NOW()),
('user009', '小伟', '/static/avatar/9.png', 
  750, 30, 24, NOW(),
  40, 1, 0, NOW(),
  220, 7, 4, NOW(),
  400, 12, 8, NOW(),
  NOW(), NOW()),
('user010', '小霞', '/static/avatar/10.png', 
  700, 28, 22, NOW(),
  30, 1, 0, NOW(),
  200, 6, 3, NOW(),
  350, 10, 6, NOW(),
  NOW(), NOW()),
('user011', '猜谜高手', '/static/avatar/11.png', 
  2000, 80, 75, NOW(),
  200, 6, 5, NOW(),
  800, 25, 23, NOW(),
  1500, 50, 45, NOW(),
  NOW(), NOW()),
('user012', '智慧之星', '/static/avatar/12.png', 
  1800, 70, 65, NOW(),
  180, 5, 4, NOW(),
  700, 22, 20, NOW(),
  1300, 45, 40, NOW(),
  NOW(), NOW()),
('user013', '古诗达人', '/static/avatar/13.png', 
  1650, 60, 55, NOW(),
  160, 4, 3, NOW(),
  600, 20, 18, NOW(),
  1200, 40, 35, NOW(),
  NOW(), NOW()),
('user014', '成语专家', '/static/avatar/14.png', 
  1500, 65, 58, NOW(),
  140, 4, 3, NOW(),
  550, 18, 16, NOW(),
  1100, 38, 33, NOW(),
  NOW(), NOW()),
('user015', '音乐天才', '/static/avatar/15.png', 
  1350, 55, 48, NOW(),
  130, 3, 2, NOW(),
  500, 16, 14, NOW(),
  1000, 35, 30, NOW(),
  NOW(), NOW()),
('user016', '图片猜王', '/static/avatar/16.png', 
  1250, 52, 45, NOW(),
  120, 3, 2, NOW(),
  450, 15, 13, NOW(),
  900, 32, 28, NOW(),
  NOW(), NOW()),
('user017', '文字高手', '/static/avatar/17.png', 
  1150, 50, 42, NOW(),
  110, 3, 2, NOW(),
  400, 14, 12, NOW(),
  800, 30, 25, NOW(),
  NOW(), NOW()),
('user018', '知识达人', '/static/avatar/18.png', 
  1050, 45, 38, NOW(),
  100, 2, 1, NOW(),
  350, 12, 10, NOW(),
  700, 25, 22, NOW(),
  NOW(), NOW()),
('user019', '题目杀手', '/static/avatar/19.png', 
  950, 42, 35, NOW(),
  90, 2, 1, NOW(),
  300, 10, 8, NOW(),
  600, 22, 18, NOW(),
  NOW(), NOW()),
('user020', '挑战者', '/static/avatar/20.png', 
  850, 40, 32, NOW(),
  80, 2, 1, NOW(),
  250, 8, 6, NOW(),
  500, 18, 15, NOW(),
  NOW(), NOW());

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */; 