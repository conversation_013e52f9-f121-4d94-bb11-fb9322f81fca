const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { parse } = require('csv-parse');
const mysql = require('mysql2/promise');
const dbConfig = require('../config/db');

// 创建数据库连接池
const pool = mysql.createPool({
    ...dbConfig,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

// 配置文件上传
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '../../uploads');
        require('fs').promises.mkdir(uploadDir, { recursive: true })
            .then(() => cb(null, uploadDir))
            .catch(err => cb(err));
    },
    filename: function (req, file, cb) {
        cb(null, Date.now() + '-' + file.originalname);
    }
});

const upload = multer({
    storage: storage,
    fileFilter: (req, file, cb) => {
        if (file.mimetype === 'text/csv' || file.mimetype === 'application/vnd.ms-excel') {
            cb(null, true);
        } else {
            cb(new Error('只接受CSV文件'));
        }
    }
});

// 获取表状态
router.get('/tables/status', async (req, res) => {
    let connection;
    try {
        connection = await pool.getConnection();
        console.log('数据库连接成功');

        const [tables] = await connection.query('SHOW TABLES');
        console.log('获取到的表列表:', tables);

        const status = {};
        for (const table of tables) {
            const tableName = Object.values(table)[0];
            console.log('处理表:', tableName);

            try {
                const [count] = await connection.query(`SELECT COUNT(*) as count FROM \`${tableName}\``);
                // 使用SHOW FULL COLUMNS获取包含注释的字段信息
                const [columns] = await connection.query(`SHOW FULL COLUMNS FROM \`${tableName}\``);

                let latestUpdatedAt = null;
                // 检查表是否存在 updated_at 字段
                const hasUpdatedAt = columns.some(col => col.Field === 'updated_at');

                if (hasUpdatedAt) {
                    // 获取最新一条记录的 updated_at
                    const [latestRecord] = await connection.query(
                        `SELECT updated_at FROM \`${tableName}\` ORDER BY updated_at DESC LIMIT 1`
                    );
                    if (latestRecord.length > 0 && latestRecord[0].updated_at) {
                        latestUpdatedAt = latestRecord[0].updated_at;
                    }
                } else {
                    // 如果没有 updated_at 字段，尝试获取 created_at 作为次选
                    const hasCreatedAt = columns.some(col => col.Field === 'created_at');
                    if (hasCreatedAt) {
                        const [latestRecord] = await connection.query(
                            `SELECT created_at FROM \`${tableName}\` ORDER BY created_at DESC LIMIT 1`
                        );
                        if (latestRecord.length > 0 && latestRecord[0].created_at) {
                            latestUpdatedAt = latestRecord[0].created_at;
                        }
                    }
                }

                status[tableName] = {
                    count: count[0].count,
                    columns: columns.map(col => ({
                        name: col.Field,
                        type: col.Type,
                        null: col.Null === 'YES',
                        key: col.Key,
                        default: col.Default,
                        extra: col.Extra,
                        comment: col.Comment || '' // 添加字段注释
                    })),
                    latest_updated_at: latestUpdatedAt
                };
                console.log(`表 ${tableName} 处理完成:`, status[tableName]);
            } catch (error) {
                console.error(`处理表 ${tableName} 时出错:`, error);
                // 继续处理其他表
                continue;
            }
        }

        res.json({ success: true, data: status });
    } catch (error) {
        console.error('获取表状态失败:', error);
        res.status(500).json({ 
            success: false, 
            message: '获取表状态失败：' + error.message,
            error: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    } finally {
        if (connection) {
            connection.release();
        }
    }
});

// 获取指定表的最新10条记录
router.get('/tables/:tableName/latest', async (req, res) => {
    const tableName = req.params.tableName;
    const limit = parseInt(req.query.limit) || 10;
    let connection;

    // Basic validation for table name to prevent SQL injection
    if (!/^[a-zA-Z0-9_]+$/.test(tableName)) {
        return res.status(400).json({ success: false, message: '无效的表名' });
    }

    try {
        connection = await pool.getConnection();
        
        // Verify if the table exists and get columns
        const [describeResult] = await connection.query(`DESCRIBE \`${tableName}\``);
        if (describeResult.length === 0) {
            connection.release();
            return res.status(404).json({ success: false, message: '表不存在' });
        }
        const columns = describeResult.map(col => ({ name: col.Field, type: col.Type }));

        // Get the latest records. Assuming 'id' is a common primary key for ordering.
        // If 'id' doesn't exist, we might need a different strategy, or order by a timestamp if available.
        // For simplicity, we'll try to order by 'id' if it exists, otherwise just limit.
        
        const columnNames = columns.map(col => `\`${col.name}\``).join(', ');
        let orderByClause = '';
        if (columns.some(col => col.name === 'id')) {
             orderByClause = 'ORDER BY id DESC';
        } else if (columns.some(col => col.name === 'created_at')) {
             orderByClause = 'ORDER BY created_at DESC';
        } else if (columns.some(col => col.name === 'updated_at')) {
             orderByClause = 'ORDER BY updated_at DESC';
        }

        const [records] = await connection.query(
            `SELECT ${columnNames} FROM \`${tableName}\` ${orderByClause} LIMIT ?`,
            [limit]
        );

        // Format timestamp fields to ISO string for consistent handling in frontend
        const formattedRecords = records.map(record => {
            const formattedRecord = { ...record };
            for (const col of columns) {
                if ((col.name === 'created_at' || col.name === 'updated_at' || col.name === 'login_time' || col.name === 'achieved_at') && record[col.name]) {
                    try {
                        // Ensure the value is a Date object or can be converted
                        const date = new Date(record[col.name]);
                        if (!isNaN(date.getTime())) {
                             formattedRecord[col.name] = date.toISOString();
                        } else {
                             formattedRecord[col.name] = record[col.name]; // Keep original if invalid date
                        }
                    } catch (e) {
                        console.error(`Error formatting date for column ${col.name}:`, record[col.name], e);
                        formattedRecord[col.name] = record[col.name]; // Keep original on error
                    }
                }
            }
            return formattedRecord;
        });

        res.json({ success: true, data: { columns, records: formattedRecords } });

    } catch (error) {
        console.error(`Error fetching latest records for table ${tableName}:`, error);
        res.status(500).json({ 
            success: false, 
            message: '获取数据失败：' + error.message,
            error: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    } finally {
        if (connection) {
            connection.release();
        }
    }
});

// 获取表结构
router.get('/tables/:tableName/structure', async (req, res) => {
    const tableName = req.params.tableName;
    let connection;

    // 基本验证表名以防止SQL注入
    if (!/^[a-zA-Z0-9_]+$/.test(tableName)) {
        return res.status(400).json({ success: false, message: '无效的表名' });
    }

    try {
        connection = await pool.getConnection();
        
        // 使用SHOW FULL COLUMNS获取包含注释的字段信息
        const [columns] = await connection.query(`SHOW FULL COLUMNS FROM \`${tableName}\``);
        
        // 格式化列信息
        const formattedColumns = columns.map(col => ({
            name: col.Field,
            type: col.Type,
            null: col.Null === 'YES',
            key: col.Key,
            default: col.Default,
            extra: col.Extra,
            comment: col.Comment || '' // 添加字段注释
        }));

        res.json({ 
            success: true, 
            data: { 
                tableName,
                columns: formattedColumns
            }
        });

    } catch (error) {
        console.error(`获取表 ${tableName} 结构失败:`, error);
        res.status(500).json({ 
            success: false, 
            message: '获取表结构失败：' + error.message,
            error: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    } finally {
        if (connection) {
            connection.release();
        }
    }
});

// 处理CSV上传
router.post('/upload', upload.single('file'), async (req, res) => {
    if (!req.file) {
        return res.status(400).json({ success: false, message: '没有上传文件' });
    }

    const tableName = req.body.table;
    if (!tableName) {
        return res.status(400).json({ success: false, message: '未指定数据表' });
    }

    // 解析字段映射和默认值
    let mapping = {};
    let defaults = {};
    try {
        if (req.body.mapping) {
            mapping = JSON.parse(req.body.mapping);
        }
        if (req.body.defaults) {
            defaults = JSON.parse(req.body.defaults);
        }
    } catch (error) {
        return res.status(400).json({ 
            success: false, 
            message: '字段映射或默认值格式错误：' + error.message 
        });
    }

    let connection;
    const startTime = Date.now();
    try {
        connection = await pool.getConnection();
        console.log('数据库连接成功，开始处理CSV上传');

        // 获取表结构
        const [columns] = await connection.query(`DESCRIBE \`${tableName}\``);
        const columnNames = columns.map(col => col.Field);
        console.log('表结构:', columnNames);

        // 检查是否存在content和answer字段
        const hasContentAndAnswer = columnNames.includes('content') && columnNames.includes('answer');
        if (!hasContentAndAnswer) {
            throw new Error('数据表必须包含content和answer字段');
        }

        // 读取CSV文件
        const records = [];
        const parser = fs.createReadStream(req.file.path)
            .pipe(parse({
                columns: headers => {
                    return headers.map(header => {
                        return header.replace(/^\uFEFF/, '')
                                   .replace(/^["']|["']$/g, '')
                                   .trim();
                    });
                },
                skip_empty_lines: true,
                trim: true
            }));

        for await (const record of parser) {
            records.push(record);
        }

        if (records.length === 0) {
            throw new Error('CSV文件为空');
        }

        console.log('CSV记录数:', records.length);
        console.log('CSV头部:', Object.keys(records[0]));

        // 验证字段映射
        const csvHeaders = Object.keys(records[0]);
        const mappedColumns = Object.entries(mapping).map(([tableCol, csvCol]) => ({
            tableCol,
            csvCol,
            isRequired: columns.find(c => c.Field === tableCol)?.Null === 'NO' && !columns.find(c => c.Field === tableCol)?.Default
        }));

        // 检查必填字段
        const missingRequired = mappedColumns
            .filter(m => {
                if (!m.isRequired) return false;
                return !csvHeaders.some(h => h.toLowerCase() === m.csvCol.toLowerCase());
            })
            .map(m => m.tableCol);
        
        if (missingRequired.length > 0) {
            throw new Error(`以下必填字段在CSV中缺失：${missingRequired.join(', ')}`);
        }

        // 开始事务
        await connection.beginTransaction();
        console.log('开始事务');

        let stats = {
            total: records.length,
            updated: 0,
            inserted: 0
        };

        for (const record of records) {
            const params = {};
            const insertFields = [];
            const insertValues = [];
            const updateFields = [];
            const updateValues = [];
            
            // 处理映射字段（不区分大小写）
            for (const [tableCol, csvCol] of Object.entries(mapping)) {
                const matchedCsvCol = Object.keys(record).find(h => 
                    h.toLowerCase() === csvCol.toLowerCase()
                );
                if (matchedCsvCol && record[matchedCsvCol] !== undefined) {
                    const column = columns.find(c => c.Field === tableCol);
                    if (column) {
                        if (column.Extra === 'auto_increment' || 
                            (column.Type.includes('timestamp') && column.Default === 'CURRENT_TIMESTAMP')) {
                            continue;
                        }
                        params[tableCol] = record[matchedCsvCol];
                        insertFields.push(`\`${tableCol}\``);
                        insertValues.push('?');
                        updateFields.push(`\`${tableCol}\` = ?`);
                        updateValues.push(record[matchedCsvCol]);
                    }
                }
            }

            // 处理默认值
            for (const [col, value] of Object.entries(defaults)) {
                const column = columns.find(c => c.Field === col);
                if (column && 
                    column.Extra !== 'auto_increment' && 
                    !(column.Type.includes('timestamp') && column.Default === 'CURRENT_TIMESTAMP')) {
                    if (params[col] === undefined) {
                        params[col] = value;
                        insertFields.push(`\`${col}\``);
                        insertValues.push('?');
                        updateFields.push(`\`${col}\` = ?`);
                        updateValues.push(value);
                    }
                }
            }

            try {
                // 检查是否存在相同的content和answer记录
                if (params.content && params.answer) {
                    const [existingRecords] = await connection.query(
                        `SELECT id FROM \`${tableName}\` WHERE content = ? AND answer = ?`,
                        [params.content, params.answer]
                    );

                    if (existingRecords.length > 0) {
                        // 如果存在相同记录，更新该记录
                        const existingId = existingRecords[0].id;
                        const [result] = await connection.query(
                            `UPDATE \`${tableName}\` SET ${updateFields.join(', ')} WHERE id = ?`,
                            [...updateValues, existingId]
                        );
                        if (result.affectedRows > 0) {
                            stats.updated++;
                        }
                        continue;
                    }
                }

                // 如果不存在相同记录，创建新记录
                await connection.query(
                    `INSERT INTO \`${tableName}\` (${insertFields.join(', ')}) VALUES (${insertValues.join(', ')})`,
                    updateValues
                );
                stats.inserted++;
            } catch (error) {
                console.error('处理记录时出错:', error);
                throw error;
            }
        }

        await connection.commit();
        console.log('事务提交成功');

        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);

        res.json({
            success: true,
            message: '数据更新成功',
            stats: {
                total: stats.total,
                updated: stats.updated,
                inserted: stats.inserted,
                duration: `${duration}秒`
            },
            summary: `读取到${stats.total}条记录：其中更新了${stats.updated}条已存在记录，创建了${stats.inserted}条新记录，总计耗时${duration}秒。`
        });

    } catch (error) {
        if (connection) {
            await connection.rollback();
        }
        console.error('处理CSV文件失败:', error);
        res.status(500).json({
            success: false,
            message: '更新失败：' + error.message,
            error: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    } finally {
        if (connection) {
            connection.release();
        }
        // 删除临时文件
        if (req.file && req.file.path) {
            require('fs').promises.unlink(req.file.path).catch(err => {
                console.error('删除临时文件失败:', err);
            });
        }
    }
});

module.exports = router; 