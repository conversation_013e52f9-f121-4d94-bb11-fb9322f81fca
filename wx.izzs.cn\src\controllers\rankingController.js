const rankingService = require('../services/rankingService');

// 获取排行榜
exports.getRanking = async (req, res) => {
  try {
    const { period = 'all', userId } = req.query;
    const data = await rankingService.getRanking(period, userId);
    res.json({ 
      code: 0, 
      data: {
        list: data.rankingList,
        userRank: data.userRank
      }
    });
  } catch (err) {
    console.error('获取排行榜失败:', err);
    res.json({ 
      code: 1, 
      msg: '获取排行榜失败', 
      error: err.message 
    });
  }
}; 