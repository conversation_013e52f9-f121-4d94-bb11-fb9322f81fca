const profileService = require('../services/profileService');

// 获取个人信息
exports.getProfile = async (req, res) => {
  try {
    const { userId } = req.query;
    if (!userId) {
      return res.json({ code: 1, msg: '用户ID不能为空' });
    }
    
    const data = await profileService.getProfile(userId);
    res.json({ code: 0, data });
  } catch (err) {
    console.error('获取个人信息失败:', err);
    res.json({ 
      code: 1, 
      msg: '获取个人信息失败', 
      error: err.message 
    });
  }
};

// 获取个人成就
exports.getAchievements = async (req, res) => {
  try {
    const { userId } = req.query;
    if (!userId) {
      return res.json({ code: 1, msg: '用户ID不能为空' });
    }
    
    const data = await profileService.getAchievements(userId);
    res.json({ code: 0, data });
  } catch (err) {
    console.error('获取个人成就失败:', err);
    res.json({ 
      code: 1, 
      msg: '获取个人成就失败', 
      error: err.message 
    });
  }
};

// 获取答题记录
exports.getAnswerRecords = async (req, res) => {
  try {
    const { userId, page = 1, limit = 20 } = req.query;
    if (!userId) {
      return res.json({ code: 1, msg: '用户ID不能为空' });
    }
    
    const data = await profileService.getAnswerRecords(userId, parseInt(page), parseInt(limit));
    res.json({ code: 0, data });
  } catch (err) {
    console.error('获取答题记录失败:', err);
    res.json({ 
      code: 1, 
      msg: '获取答题记录失败', 
      error: err.message 
    });
  }
};

// 获取个人统计
exports.getStats = async (req, res) => {
  try {
    const { userId } = req.query;
    if (!userId) {
      return res.json({ code: 1, msg: '用户ID不能为空' });
    }
    
    const data = await profileService.getStats(userId);
    res.json({ code: 0, data });
  } catch (err) {
    console.error('获取个人统计失败:', err);
    res.json({ 
      code: 1, 
      msg: '获取个人统计失败', 
      error: err.message 
    });
  }
}; 