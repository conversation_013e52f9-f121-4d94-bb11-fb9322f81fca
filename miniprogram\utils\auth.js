/**
 * 微信登录工具类
 */
const env = require('../env.js')
const { processUserAvatar } = require('./avatarHelper')

class AuthManager {
  constructor() {
    this.loginPromise = null
  }

  /**
   * 微信一键登录
   */
  async wxLogin() {
    // 如果正在登录中，返回同一个 Promise
    if (this.loginPromise) {
      return this.loginPromise
    }

    this.loginPromise = this._doLogin()
    
    try {
      const result = await this.loginPromise
      return result
    } finally {
      this.loginPromise = null
    }
  }

  /**
   * 执行登录流程
   */
  async _doLogin() {
    try {
      // 1. 调用 wx.login 获取 code
      const loginRes = await this._wxLogin()
      const code = loginRes.code

      if (!code) {
        throw new Error('获取登录凭证失败')
      }

      // 2. 发送 code 到后端获取 openid
      const authRes = await this._sendCodeToServer(code)
      
      if (authRes.code !== 0) {
        throw new Error(authRes.msg || '登录失败')
      }

      const { openid, session_key } = authRes.data

      // 3. 保存登录信息到全局和本地存储
      const app = getApp()
      app.globalData.openid = openid
      app.globalData.sessionKey = session_key
      
      wx.setStorageSync('openid', openid)
      wx.setStorageSync('sessionKey', session_key)
      wx.setStorageSync('loginTime', Date.now())

      return {
        success: true,
        openid,
        session_key
      }

    } catch (error) {
      console.error('登录失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 简单登录流程（仅获取openid，引导用户设置资料）
   */
  async simpleLogin() {
    try {
      // 1. 微信登录获取 openid
      const loginResult = await this.wxLogin()
      
      if (!loginResult.success) {
        return {
          success: false,
          error: loginResult.error
        }
      }

      // 2. 检查用户是否已设置过资料
      const profileResult = await this._getUserProfile(loginResult.openid)
      
      if (profileResult.success && profileResult.userInfo) {
        // 用户已有资料，处理头像URL并返回
        const processedUserInfo = processUserAvatar(profileResult.userInfo)
        
        wx.setStorageSync('userInfo', processedUserInfo)
        wx.setStorageSync('hasUserInfo', true)
        
        return {
          success: true,
          openid: loginResult.openid,
          userInfo: processedUserInfo,
          needSetProfile: false
        }
      } else {
        // 用户需要设置资料
        return {
          success: true,
          openid: loginResult.openid,
          userInfo: null,
          needSetProfile: true
        }
      }

    } catch (error) {
      console.error('简单登录流程失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 保存用户资料（用于头像昵称填写后）
   */
  async saveUserProfile(userInfo) {
    try {
      const app = getApp()
      const openid = app.globalData.openid || wx.getStorageSync('openid')
      
      if (!openid) {
        throw new Error('用户未登录')
      }

      // 保存用户信息到后端
      const saveResult = await this._saveUserInfo({
        openid: openid,
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl
      })

      if (!saveResult.success) {
        throw new Error(saveResult.error || '保存用户资料失败')
      }

      // 处理头像URL并保存到本地
      const processedUserInfo = processUserAvatar(userInfo)
      wx.setStorageSync('userInfo', processedUserInfo)
      wx.setStorageSync('hasUserInfo', true)

      return {
        success: true,
        userInfo: processedUserInfo
      }

    } catch (error) {
      console.error('保存用户资料失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp()
    const openid = app.globalData.openid || wx.getStorageSync('openid')
    let userInfo = wx.getStorageSync('userInfo')
    const loginTime = wx.getStorageSync('loginTime')

    // 检查登录是否过期（7天）
    const isExpired = loginTime && (Date.now() - loginTime > 7 * 24 * 60 * 60 * 1000)

    // 如果有用户信息，确保头像URL是处理过的
    if (userInfo) {
      userInfo = processUserAvatar(userInfo)
    }

    return {
      isLoggedIn: !!openid && !isExpired,
      hasUserInfo: !!userInfo,
      openid,
      userInfo,
      isExpired
    }
  }

  /**
   * 静默登录（仅获取 openid）
   */
  async silentLogin() {
    const status = this.checkLoginStatus()
    
    if (status.isLoggedIn && !status.isExpired) {
      // 登录状态有效，直接返回
      const app = getApp()
      app.globalData.openid = status.openid
      return {
        success: true,
        openid: status.openid
      }
    }

    // 需要重新登录
    return await this.wxLogin()
  }

  /**
   * 退出登录
   */
  logout() {
    const app = getApp()
    app.globalData.openid = ''
    app.globalData.sessionKey = ''
    
    wx.removeStorageSync('openid')
    wx.removeStorageSync('sessionKey')
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('hasUserInfo')
    wx.removeStorageSync('loginTime')
  }

  // ==================== 私有方法 ====================

  /**
   * 调用 wx.login
   */
  _wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  }

  /**
   * 发送 code 到服务器
   */
  async _sendCodeToServer(code) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${env.API_BASE_URL}/api/auth/login`,
        method: 'POST',
        data: { code },
        success: (res) => {
          resolve(res.data)
        },
        fail: (err) => {
          reject(new Error(err.errMsg || '网络请求失败'))
        }
      })
    })
  }

  /**
   * 获取用户资料
   */
  async _getUserProfile(openid) {
    return new Promise((resolve) => {
      wx.request({
        url: `${env.API_BASE_URL}/api/user/profile`,
        method: 'GET',
        data: { openid },
        success: (res) => {
          if (res.data.code === 0 && res.data.data) {
            resolve({ 
              success: true, 
              userInfo: res.data.data 
            })
          } else {
            resolve({ 
              success: false, 
              userInfo: null 
            })
          }
        },
        fail: (err) => {
          resolve({ 
            success: false, 
            error: err.errMsg || '网络请求失败' 
          })
        }
      })
    })
  }

  /**
   * 保存用户信息到后端
   */
  async _saveUserInfo(userData) {
    return new Promise((resolve) => {
      wx.request({
        url: `${env.API_BASE_URL}/api/user/save`,
        method: 'POST',
        data: userData,
        success: (res) => {
          if (res.data.code === 0) {
            resolve({ success: true })
          } else {
            resolve({ 
              success: false, 
              error: res.data.msg || '保存失败' 
            })
          }
        },
        fail: (err) => {
          resolve({ 
            success: false, 
            error: err.errMsg || '网络请求失败' 
          })
        }
      })
    })
  }
}

// 创建单例实例
const authManager = new AuthManager()

module.exports = authManager 