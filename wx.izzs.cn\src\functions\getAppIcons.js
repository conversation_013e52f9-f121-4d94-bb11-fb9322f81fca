const cloud = require('wx-server-sdk')
const fs = require('fs')
const path = require('path')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 获取APP图标列表
exports.main = async (event, context) => {
  try {
    // 读取APP图标目录
    const appIconsDir = path.join(__dirname, '../../static/images/app/cnapp')
    const files = fs.readdirSync(appIconsDir)
    
    // 过滤出PNG文件并提取APP名称
    const icons = files
      .filter(file => file.endsWith('.png'))
      .map(file => ({
        name: file.replace('.png', ''),
        url: `cloud://${cloud.DYNAMIC_CURRENT_ENV}.${cloud.DYNAMIC_CURRENT_ENV}/static/images/app/cnapp/${file}`
      }))
    
    return {
      success: true,
      icons
    }
  } catch (error) {
    console.error('获取APP图标列表失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
} 