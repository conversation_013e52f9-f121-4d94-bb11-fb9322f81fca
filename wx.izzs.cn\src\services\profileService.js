const mysql = require('mysql2/promise');
const dbConfig = require('../config/db');
const { processUserAvatar, processUsersAvatars } = require('../utils/avatarHelper');

// 获取个人信息
exports.getProfile = async (userId) => {
  const conn = await mysql.createConnection(dbConfig);
  
  try {
    // 获取用户基本信息
    const [userRows] = await conn.execute(`
      SELECT 
        id,
        nickname,
        avatar_url,
        total_score,
        total_answers,
        correct_answers,
        created_at
      FROM users 
      WHERE id = ?
    `, [userId]);
    
    if (userRows.length === 0) {
      return null;
    }
    
    let user = userRows[0];
    
    // 处理用户头像
    user = processUserAvatar(user);
    
    // 计算正确率
    user.accuracy = user.total_answers > 0 ? 
      Math.round((user.correct_answers / user.total_answers) * 100) : 0;
    
    // 获取用户排名
    const [rankRows] = await conn.execute(`
      SELECT COUNT(*) + 1 as rank
      FROM users 
      WHERE total_score > ?
    `, [user.total_score]);
    
    user.rank = rankRows[0].rank;
    
    // 获取今日统计
    const [todayRows] = await conn.execute(`
      SELECT 
        daily_score,
        daily_answers,
        daily_correct,
        current_streak
      FROM user_stats 
      WHERE user_id = ? AND date = CURDATE()
    `, [userId]);
    
    user.todayStats = todayRows.length > 0 ? todayRows[0] : {
      daily_score: 0,
      daily_answers: 0,
      daily_correct: 0,
      current_streak: 0
    };
    
    // 获取成就数量
    const [achievementRows] = await conn.execute(`
      SELECT COUNT(*) as achievement_count
      FROM user_achievements 
      WHERE user_id = ?
    `, [userId]);
    
    user.achievementCount = achievementRows[0].achievement_count;
    
    return user;
    
  } finally {
    await conn.end();
  }
};

// 获取个人成就
exports.getAchievements = async (userId) => {
  const conn = await mysql.createConnection(dbConfig);
  
  try {
    // 获取所有成就及用户获得情况
    const [rows] = await conn.execute(`
      SELECT 
        a.id,
        a.name,
        a.description,
        a.icon,
        a.points,
        ua.achieved_at,
        CASE WHEN ua.achievement_id IS NOT NULL THEN 1 ELSE 0 END as achieved
      FROM achievements a
      LEFT JOIN user_achievements ua ON a.id = ua.achievement_id AND ua.user_id = ?
      ORDER BY achieved DESC, a.id ASC
    `, [userId]);
    
    return rows;
    
  } finally {
    await conn.end();
  }
};

// 获取答题记录
exports.getAnswerRecords = async (userId, page = 1, limit = 20) => {
  const conn = await mysql.createConnection(dbConfig);
  
  try {
    const offset = (page - 1) * limit;
    
    // 获取答题记录
    const [rows] = await conn.execute(`
      SELECT 
        ua.id,
        ua.riddle_id,
        ua.table_name,
        ua.user_answer,
        ua.is_correct,
        ua.score,
        ua.time_used,
        ua.created_at,
        CASE 
          WHEN ua.table_name = 'idiom_riddles' THEN '成语'
          WHEN ua.table_name = 'xiehouyu_riddles' THEN '歇后语'
          WHEN ua.table_name = 'animal_riddles' THEN '动物'
          WHEN ua.table_name = 'fruit_riddles' THEN '水果'
          WHEN ua.table_name = 'place_riddles' THEN '地名'
          WHEN ua.table_name = 'person_riddles' THEN '人名'
          WHEN ua.table_name = 'tang_poetry_riddles' THEN '唐诗'
          WHEN ua.table_name = 'song_poetry_riddles' THEN '宋词'
          WHEN ua.table_name = 'flag_riddles' THEN '国旗'
          WHEN ua.table_name = 'map_outline_riddles' THEN '地图轮廓'
          WHEN ua.table_name = 'car_logo_riddles' THEN '车标'
          WHEN ua.table_name = 'brand_logo_riddles' THEN '品牌logo'
          WHEN ua.table_name = 'movie_still_riddles' THEN '电影剧照'
          WHEN ua.table_name = 'app_icon_riddles' THEN 'APP图标'
          WHEN ua.table_name = 'audio_song_riddles' THEN '歌曲'
          WHEN ua.table_name = 'audio_animal_riddles' THEN '动物叫声'
          WHEN ua.table_name = 'word_riddles' THEN '字谜'
          ELSE '其他'
        END as type_name
      FROM user_answers ua
      WHERE ua.user_id = ?
      ORDER BY ua.created_at DESC
      LIMIT ? OFFSET ?
    `, [userId, limit, offset]);
    
    // 获取总数
    const [countRows] = await conn.execute(`
      SELECT COUNT(*) as total
      FROM user_answers 
      WHERE user_id = ?
    `, [userId]);
    
    return {
      records: rows,
      total: countRows[0].total,
      page,
      limit,
      totalPages: Math.ceil(countRows[0].total / limit)
    };
    
  } finally {
    await conn.end();
  }
};

// 获取个人统计
exports.getStats = async (userId) => {
  const conn = await mysql.createConnection(dbConfig);
  
  try {
    // 获取最近7天统计
    const [dailyRows] = await conn.execute(`
      SELECT 
        date,
        daily_score,
        daily_answers,
        daily_correct
      FROM user_stats 
      WHERE user_id = ? AND date >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
      ORDER BY date ASC
    `, [userId]);
    
    // 获取各类型答题统计
    const [typeRows] = await conn.execute(`
      SELECT 
        ua.table_name,
        COUNT(*) as total_count,
        SUM(ua.is_correct) as correct_count,
        AVG(ua.time_used) as avg_time,
        CASE 
          WHEN ua.table_name = 'idiom_riddles' THEN '成语'
          WHEN ua.table_name = 'xiehouyu_riddles' THEN '歇后语'
          WHEN ua.table_name = 'animal_riddles' THEN '动物'
          WHEN ua.table_name = 'fruit_riddles' THEN '水果'
          WHEN ua.table_name = 'place_riddles' THEN '地名'
          WHEN ua.table_name = 'person_riddles' THEN '人名'
          WHEN ua.table_name = 'tang_poetry_riddles' THEN '唐诗'
          WHEN ua.table_name = 'song_poetry_riddles' THEN '宋词'
          WHEN ua.table_name = 'flag_riddles' THEN '国旗'
          WHEN ua.table_name = 'map_outline_riddles' THEN '地图轮廓'
          WHEN ua.table_name = 'car_logo_riddles' THEN '车标'
          WHEN ua.table_name = 'brand_logo_riddles' THEN '品牌logo'
          WHEN ua.table_name = 'movie_still_riddles' THEN '电影剧照'
          WHEN ua.table_name = 'app_icon_riddles' THEN 'APP图标'
          WHEN ua.table_name = 'audio_song_riddles' THEN '歌曲'
          WHEN ua.table_name = 'audio_animal_riddles' THEN '动物叫声'
          WHEN ua.table_name = 'word_riddles' THEN '字谜'
          ELSE '其他'
        END as type_name
      FROM user_answers ua
      WHERE ua.user_id = ?
      GROUP BY ua.table_name
      ORDER BY total_count DESC
    `, [userId]);
    
    // 计算各类型正确率
    const typeStats = typeRows.map(row => ({
      ...row,
      accuracy: row.total_count > 0 ? Math.round((row.correct_count / row.total_count) * 100) : 0,
      avg_time: Math.round(row.avg_time || 0)
    }));
    
    return {
      dailyStats: dailyRows,
      typeStats
    };
    
  } finally {
    await conn.end();
  }
}; 