<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-header">
    <view wx:if="{{!hasUserInfo}}" class="login-section">
      <view class="login-avatar">👤</view>
      <view class="login-info">
        <text class="login-title">登录后查看详细数据</text>
        <text class="login-desc">保存答题记录，参与排名</text>
      </view>
      <button 
        class="login-btn" 
        bindtap="doLogin"
        loading="{{loginLoading}}"
        disabled="{{loginLoading}}"
      >
        {{loginLoading ? '登录中...' : '微信一键登录'}}
      </button>
    </view>

    <view wx:else class="user-info">
      <view class="user-main">
        <image 
          class="user-avatar" 
          src="{{userInfo.avatarUrl}}" 
          mode="aspectFill" 
          binderror="onAvatarError"
        />
        <view class="user-details">
          <text class="user-nickname">{{userInfo.nickName}}</text>
          <view class="user-rank">
            <text class="rank-icon">👑</text>
            <text class="rank-text">排名第 {{userStats.rank || '--'}} 位</text>
          </view>
        </view>
        <view class="edit-profile" bindtap="editProfile">
          <text class="edit-icon">✏️</text>
        </view>
      </view>
      <view class="total-score">
        <text class="score-number">{{userStats.totalScore}}</text>
        <text class="score-label">总积分</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 登录后的内容 -->
  <view wx:elif="{{hasUserInfo}}" class="profile-content">
    <!-- 统计数据卡片 -->
    <view class="stats-cards">
      <view class="stat-card">
        <text class="stat-number">{{userStats.totalAnswers}}</text>
        <text class="stat-label">答题总数</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{userStats.correctAnswers}}</text>
        <text class="stat-label">答对题数</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{userStats.accuracy}}%</text>
        <text class="stat-label">正确率</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{userStats.bestStreak}}</text>
        <text class="stat-label">最佳连胜</text>
      </view>
    </view>

    <!-- 标签页切换 -->
    <view class="tabs-container">
      <view class="tabs">
        <view 
          class="tab-item {{currentTab === 0 ? 'active' : ''}}"
          data-index="0"
          bindtap="switchTab"
        >
          📊 数据统计
        </view>
        <view 
          class="tab-item {{currentTab === 1 ? 'active' : ''}}"
          data-index="1"
          bindtap="switchTab"
        >
          🏆 我的成就
        </view>
        <view 
          class="tab-item {{currentTab === 2 ? 'active' : ''}}"
          data-index="2"
          bindtap="switchTab"
        >
          📝 答题记录
        </view>
      </view>
    </view>

    <!-- 标签页内容容器 - 固定大小，常驻显示 -->
    <view class="tab-content-container">
      <view class="tab-content">
        <!-- 数据统计页面 -->
        <view hidden="{{currentTab !== 0}}">
          <!-- 连胜信息 -->
          <view class="streak-section">
            <view class="section-header">
              <text class="section-title">🔥 连胜状态</text>
            </view>
            <view class="streak-info">
              <view class="streak-item">
                <text class="streak-label">当前连胜</text>
                <text class="streak-value current">{{userStats.currentStreak}}</text>
              </view>
              <view class="streak-item">
                <text class="streak-label">历史最佳</text>
                <text class="streak-value best">{{userStats.bestStreak}}</text>
              </view>
            </view>
          </view>
          <!-- 每日统计 -->
          <view class="daily-stats-section">
            <view class="section-header">
              <text class="section-title">📈 最近表现</text>
            </view>
            <view class="content-area">
              <view wx:if="{{dailyStats.length > 0}}" class="daily-stats">
                <view 
                  wx:for="{{dailyStats}}" 
                  wx:key="date"
                  class="daily-stat-item"
                >
                  <view class="stat-date">{{item.date}}</view>
                  <view class="stat-details">
                    <text class="stat-score">{{item.daily_score}}分</text>
                    <text class="stat-count">{{item.daily_answers}}题</text>
                    <text class="stat-accuracy">正确率{{item.daily_correct}}/{{item.daily_answers}}</text>
                  </view>
                </view>
              </view>
              <view wx:else class="empty-state">
                <image class="empty-image" src="/assets/images/noData.png" mode="aspectFit" />
                <text class="empty-text">暂无数据统计</text>
                <text class="empty-desc">开始答题来查看表现吧！</text>
              </view>
            </view>
          </view>
        </view>
        <!-- 成就页面 -->
        <view hidden="{{currentTab !== 1}}">
          <view class="achievements-section">
            <view class="section-header">
              <text class="section-title">🏆 成就徽章</text>
              <text class="achievement-count">{{earnedCount}}/{{allAchievements.length}}</text>
            </view>
            <view class="achievements-grid">
              <block wx:for="{{achievementGrid}}" wx:key="index">
                <view
                  wx:if="{{item}}"
                  class="achievement-item {{item.earned ? 'earned' : 'locked'}}"
                  data-item="{{item}}"
                  bindtap="viewAchievement"
                >
                  <text class="achievement-icon">{{item.icon}}</text>
                  <text class="achievement-name">{{item.name}}</text>
                  <text class="achievement-date">{{item.earned ? item.achieved_at : '未获得'}}</text>
                  <view wx:if="{{!item.earned}}" class="achievement-lock">🔒</view>
                </view>
                <view wx:else class="achievement-item empty"></view>
              </block>
            </view>
          </view>
        </view>
        <!-- 答题记录页面 -->
        <view hidden="{{currentTab !== 2}}">
          <view class="records-section">
            <view class="section-header">
              <text class="section-title">📝 最近答题</text>
            </view>
            <view class="records-list">
              <block wx:for="{{recentRecords}}" wx:key="id">
                <view
                  class="record-item {{item.is_correct ? 'correct' : 'wrong'}}"
                  data-item="{{item}}"
                  bindtap="viewRecord"
                >
                  <view class="record-result">
                    <text class="result-icon">{{item.is_correct ? '✅' : '❌'}}</text>
                  </view>
                  <view class="record-content">
                    <text wx:if="{{item.question_content}}" class="record-question">{{item.question_content}}</text>
                    <view class="record-details">
                      <text class="record-answer">答案：{{item.user_answer}}</text>
                      <text class="record-time">用时：{{item.time_used}}秒</text>
                    </view>
                  </view>
                  <view class="record-score">
                    <text class="score-value">{{item.score}}</text>
                    <text class="score-unit">分</text>
                  </view>
                </view>
              </block>
              <view wx:if="{{recentRecords.length === 0}}" class="empty-state">
                <image class="empty-image" src="/assets/images/noData.png" mode="aspectFit" />
                <text class="empty-text">暂无答题记录</text>
                <text class="empty-desc">开始答题来查看记录吧！</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 设置区域 -->
    <view class="settings-section">
      <view class="section-header">
        <view class="section-title-with-icon">
          <text class="section-icon">⚙️</text>
          <text class="section-title">设置</text>
        </view>
      </view>
      <view class="settings-list">
        <view class="setting-item" bindtap="editProfile">
          <view class="setting-left">
            <text class="setting-icon">✏️</text>
            <text class="setting-label">编辑资料</text>
          </view>
          <view class="setting-arrow">
            <text class="arrow-icon">></text>
          </view>
        </view>
        <view class="setting-item" bindtap="logout">
          <view class="setting-left">
            <text class="setting-icon">🚪</text>
            <text class="setting-label">退出登录</text>
          </view>
          <view class="setting-arrow">
            <text class="arrow-icon">></text>
          </view>
        </view>
        <view class="setting-item" bindtap="clearCache">
          <view class="setting-left">
            <text class="setting-icon">🗑️</text>
            <text class="setting-label">清除缓存</text>
          </view>
          <view class="setting-arrow">
            <text class="arrow-icon">></text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 未登录状态提示 -->
  <view wx:else class="not-login-content">
    <view class="not-login-icon">🎯</view>
    <text class="not-login-title">登录后体验更多功能</text>
    <text class="not-login-desc">查看详细统计、获得成就、参与排名</text>
  </view>
</view>