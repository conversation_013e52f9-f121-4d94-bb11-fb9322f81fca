-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-05-26 00:19:18
-- 服务器版本： 5.7.40-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `guess`
--

-- --------------------------------------------------------

--
-- 表的结构 `user_answers`
--

CREATE TABLE `user_answers` (
  `id` int(11) NOT NULL,
  `user_id` varchar(50) NOT NULL COMMENT '用户ID',
  `riddle_id` int(11) NOT NULL COMMENT '谜题ID',
  `table_name` varchar(50) DEFAULT NULL COMMENT '题目来源表名',
  `user_answer` varchar(255) NOT NULL COMMENT '用户答案',
  `is_correct` tinyint(4) DEFAULT '0' COMMENT '是否正确',
  `score` int(11) DEFAULT '0' COMMENT '得分',
  `time_used` int(11) DEFAULT '0' COMMENT '用时(秒)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户答题记录表';

--
-- 转存表中的数据 `user_answers`
--

INSERT INTO `user_answers` (`id`, `user_id`, `riddle_id`, `table_name`, `user_answer`, `is_correct`, `score`, `time_used`, `created_at`) VALUES
(1, 'user001', 1, 'idiom_riddles', '多此一举', 1, 100, 15, '2025-05-23 11:47:57'),
(2, 'user001', 2, 'idiom_riddles', '死守经验', 1, 100, 20, '2025-05-23 11:47:57'),
(3, 'user001', 3, 'idiom_riddles', '及时补救', 1, 100, 18, '2025-05-23 11:47:57'),
(4, 'user001', 4, 'idiom_riddles', '自欺欺人', 1, 100, 12, '2025-05-23 11:47:57'),
(5, 'user001', 5, 'idiom_riddles', '眼界狭小', 1, 100, 25, '2025-05-23 11:47:57'),
(6, 'user002', 1, 'idiom_riddles', '多此一举', 1, 100, 18, '2025-05-23 11:47:57'),
(7, 'user002', 2, 'idiom_riddles', '死守经验', 1, 100, 22, '2025-05-23 11:47:57'),
(8, 'user002', 3, 'idiom_riddles', '及时补救', 0, 0, 30, '2025-05-23 11:47:57'),
(9, 'user002', 4, 'idiom_riddles', '自欺欺人', 1, 100, 16, '2025-05-23 11:47:57'),
(10, 'user002', 5, 'idiom_riddles', '眼界狭小', 1, 100, 28, '2025-05-23 11:47:57'),
(11, 'user003', 1, 'xiehouyu_riddles', '有苦说不出', 1, 100, 10, '2025-05-23 11:47:57'),
(12, 'user003', 2, 'xiehouyu_riddles', '自身难保', 1, 100, 12, '2025-05-23 11:47:57'),
(13, 'user003', 3, 'xiehouyu_riddles', '无法无天', 1, 100, 15, '2025-05-23 11:47:57'),
(14, 'user003', 4, 'xiehouyu_riddles', '咬文嚼字', 0, 0, 35, '2025-05-23 11:47:57'),
(15, 'user003', 5, 'xiehouyu_riddles', '照舅', 1, 100, 8, '2025-05-23 11:47:57'),
(16, 'user004', 1, 'animal_riddles', '牛', 1, 100, 5, '2025-05-23 11:47:57'),
(17, 'user004', 2, 'animal_riddles', '燕子', 1, 100, 8, '2025-05-23 11:47:57'),
(18, 'user004', 3, 'animal_riddles', '熊猫', 0, 0, 25, '2025-05-23 11:47:57'),
(19, 'user004', 4, 'animal_riddles', '乌龟', 1, 100, 7, '2025-05-23 11:47:57'),
(20, 'user004', 5, 'animal_riddles', '猎豹', 1, 100, 20, '2025-05-23 11:47:57'),
(21, 'user005', 1, 'fruit_riddles', '苹果', 1, 100, 6, '2025-05-23 11:47:57'),
(22, 'user005', 2, 'fruit_riddles', '香蕉', 1, 100, 4, '2025-05-23 11:47:57'),
(23, 'user005', 3, 'fruit_riddles', '西瓜', 1, 100, 7, '2025-05-23 11:47:57'),
(24, 'user005', 4, 'fruit_riddles', '葡萄', 0, 0, 20, '2025-05-23 11:47:57'),
(25, 'user005', 5, 'fruit_riddles', '猕猴桃', 1, 100, 30, '2025-05-23 11:47:57'),
(26, 'user006', 1, 'idiom_riddles', '多此一举', 1, 100, 12, '2025-05-23 11:47:57'),
(27, 'user006', 1, 'flag_riddles', '中国', 1, 100, 8, '2025-05-23 11:47:57'),
(28, 'user006', 2, 'flag_riddles', '美国', 1, 100, 10, '2025-05-23 11:47:57'),
(29, 'user006', 1, 'audio_song_riddles', '月亮代表我的心', 1, 100, 25, '2025-05-23 11:47:57'),
(30, 'user007', 1, 'tang_poetry_riddles', '静夜思', 1, 100, 15, '2025-05-23 11:47:57'),
(31, 'user007', 2, 'tang_poetry_riddles', '春晓', 1, 100, 18, '2025-05-23 11:47:57'),
(32, 'user007', 1, 'car_logo_riddles', '宝马', 1, 100, 5, '2025-05-23 11:47:57'),
(33, 'user007', 2, 'car_logo_riddles', '奥迪', 0, 0, 15, '2025-05-23 11:47:57'),
(34, 'user008', 1, 'place_riddles', '杭州', 1, 100, 20, '2025-05-23 11:47:57'),
(35, 'user008', 2, 'place_riddles', '上海', 1, 100, 16, '2025-05-23 11:47:57'),
(36, 'user008', 1, 'app_icon_riddles', '微信', 1, 100, 3, '2025-05-23 11:47:57'),
(37, 'user008', 2, 'app_icon_riddles', 'QQ', 1, 100, 4, '2025-05-23 11:47:57'),
(57, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 53, 'map_outline_riddles', '澳门', 1, 100, 354, '2025-05-25 03:11:15'),
(58, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 27, 'map_outline_riddles', '天津', 1, 100, 28, '2025-05-25 03:11:44'),
(59, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 24, 'map_outline_riddles', '呼和浩特', 1, 100, 9, '2025-05-25 03:38:17'),
(60, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 25, 'map_outline_riddles', '哈尔滨', 1, 100, 6, '2025-05-25 03:41:26'),
(61, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 16, 'map_outline_riddles', '江苏', 0, 0, 19, '2025-05-25 04:08:24'),
(62, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 38, 'map_outline_riddles', '新疆', 1, 100, 4, '2025-05-25 04:08:29'),
(63, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 1064, 'app_icon_riddles', '中华万年历', 1, 100, 7, '2025-05-25 04:35:36'),
(64, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 20, 'map_outline_riddles', '台北', 1, 100, 9, '2025-05-25 04:36:24');

--
-- 转储表的索引
--

--
-- 表的索引 `user_answers`
--
ALTER TABLE `user_answers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_riddle_id` (`riddle_id`),
  ADD KEY `idx_table_name` (`table_name`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `user_answers`
--
ALTER TABLE `user_answers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=65;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
