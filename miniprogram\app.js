// app.js
App({
  globalData: {
    userInfo: null,
    openid: null,
    currentRiddleType: '',
    competitionTypes: '', // 竞技模式选择的题型
    competitionQuantities: {} // 竞技模式各题型数量
  },
  
  onLaunch() {
    // 获取本地存储的用户数据
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.globalData.userInfo = userInfo
    }
    
    // 获取本地存储的游戏数据
    const gameData = wx.getStorageSync('gameData')
    if (gameData) {
      this.globalData.gameData = gameData
    }
  }
}) 