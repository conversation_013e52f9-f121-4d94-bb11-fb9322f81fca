// APP图标管理器
const appIconManager = {
  // 已答过的APP名称缓存
  answeredApps: [],
  
  // 当前轮次的APP列表
  currentRoundApps: [],
  
  // 从本地存储加载已答过的APP
  loadAnsweredApps() {
    const answered = wx.getStorageSync('answeredApps') || []
    this.answeredApps = answered
    return answered
  },
  
  // 保存已答过的APP到本地存储
  saveAnsweredApps() {
    wx.setStorageSync('answeredApps', this.answeredApps)
  },
  
  // 获取所有可用的APP图标
  async getAllAppIcons() {
    try {
      const baseUrl = 'https://wx.izzs.cn'
      // 用Promise封装wx.request
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: `${baseUrl}/api/app-icons`,
          method: 'GET',
          header: {
            'content-type': 'application/json'
          },
          success: resolve,
          fail: reject
        })
      })
      if (res.statusCode === 200 && res.data && res.data.success) {
        return res.data.icons || []
      } else {
        console.error('获取APP图标列表失败:', res)
        throw new Error('获取APP图标失败')
      }
    } catch (error) {
      console.error('获取APP图标列表异常:', error)
      return []
    }
  },
  
  // 随机选择10个未答过的APP，增加递归保护，最多重试3次
  async getRandomApps(retryCount = 0) {
    if (retryCount > 3) {
      console.error('获取APP图标重试次数过多，终止递归')
      return []
    }

    const allIcons = await this.getAllAppIcons()
    const availableApps = allIcons.filter(app => !this.answeredApps.includes(app.name))
    // 如果可用APP不足10个，重置已答过的APP列表
    if (availableApps.length < 10) {
      console.log('可用APP不足10个，重置已答列表')
      this.answeredApps = []
      this.saveAnsweredApps()
      return this.getRandomApps(retryCount + 1)
    }
    // 随机选择10个APP
    const selectedApps = []
    const tempApps = [...availableApps]
    for (let i = 0; i < 10; i++) {
      const randomIndex = Math.floor(Math.random() * tempApps.length)
      selectedApps.push(tempApps[randomIndex])
      tempApps.splice(randomIndex, 1)
    }
    console.log('随机选择的10个APP:', selectedApps)
    this.currentRoundApps = selectedApps
    return selectedApps
  },
  
  // 验证答案
  checkAnswer(appName, userAnswer) {
    const isCorrect = appName === userAnswer
    if (isCorrect) {
      // 答对后添加到已答列表
      if (!this.answeredApps.includes(appName)) {
        this.answeredApps.push(appName)
        this.saveAnsweredApps()
      }
    }
    return isCorrect
  }
}

export default appIconManager 