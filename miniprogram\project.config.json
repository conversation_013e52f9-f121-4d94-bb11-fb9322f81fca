{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "showES6CompileOption": false}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "wx62d23585f8bf3cb0", "projectname": "猜谜多合一", "condition": {}, "env": {"development": {"NODE_ENV": "development"}, "production": {"NODE_ENV": "production"}, "API_BASE_URL": "https://wx.izzs.cn"}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}