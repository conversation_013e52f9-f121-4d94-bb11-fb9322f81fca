/* 页面容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

/* 页面标题 */
.page-header {
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
}

.page-title {
  font-size: 44rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.3);
}

/* 标签页容器 */
.tabs-container {
  padding: 0 30rpx 30rpx;
  transition: all 0.3s ease;
  z-index: 100;
}

.tabs-container.fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding: 0 30rpx;
  z-index: 100;
}

.tabs-placeholder {
  height: 120rpx; /* 与 tabs-container 高度相同 */
}

.tabs {
  display: flex;
  background: rgba(255,255,255,0.2);
  border-radius: 50rpx;
  padding: 8rpx;
  backdrop-filter: blur(10rpx);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  border-radius: 42rpx;
  font-size: 30rpx;
  color: rgba(0,0,0,1);
  font-weight: bold;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #fff;
  color: #667eea;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255,255,255,0.3);
  border-top: 6rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #fff;
  font-size: 28rpx;
  margin-top: 20rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  color: #fff;
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

.retry-btn {
  background: #fff;
  color: #667eea;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

/* 排行榜内容 */
.ranking-content {
  background: #f5f7fa;
  border-radius: 40rpx 40rpx 0 0;
  min-height: calc(100vh - 200rpx);
  padding: 0 0 100rpx;
}

/* 领奖台 */
.podium-container {
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.podium {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 400rpx;
  position: relative;
}

.podium-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 20rpx;
  position: relative;
}

.avatar-container {
  position: relative;
  margin-bottom: 30rpx;
  padding-bottom: 10rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 6rpx solid #fff;
  box-shadow: 0 8rpx 20rpx rgba(0,0,0,0.2);
  position: relative;
  z-index: 0;
}

.first .avatar {
  width: 140rpx;
  height: 140rpx;
  border: 8rpx solid #fff;
}

.medal {
  position: absolute;
  bottom: -15rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 44rpx;
  z-index: 1;
}

.crown {
  position: absolute;
  top: -60rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 60rpx;
  z-index: 2;
  text-shadow: 0 4rpx 8rpx rgba(0,0,0,0.2);
}

.nickname {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-align: center;
  max-width: 140rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.score {
  color: rgba(255,255,255,0.9);
  font-size: 20rpx;
  margin-bottom: 20rpx;
}

.podium-base {
  width: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 12rpx 12rpx 0 0;
}

.first-base {
  height: 120rpx;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
}

.second-base {
  height: 100rpx;
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #333;
}

.third-base {
  height: 80rpx;
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: #333;
}

/* 排行榜列表 */
.ranking-list {
  background: #fff;
  margin: 0 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 25rpx rgba(0,0,0,0.1);
}

.list-header {
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.list-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 2rpx solid #f8f9fa;
  transition: background 0.2s ease;
}

.ranking-item:active {
  background: #f8f9fa;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-item.top-three {
  background: linear-gradient(90deg, rgba(255,215,0,0.1), rgba(255,255,255,0));
}

.rank-info {
  width: 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
}

.rank-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.rank-medal {
  font-size: 24rpx;
  margin-top: 4rpx;
}

.user-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 2rpx solid #f0f0f0;
}

.user-details {
  flex: 1;
}

.user-nickname {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.user-stats {
  font-size: 22rpx;
  color: #999;
}

.score-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.total-score {
  font-size: 32rpx;
  font-weight: bold;
  color: #667eea;
}

.score-label {
  font-size: 20rpx;
  color: #999;
  margin-top: 2rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

/* 当前用户排名 */
.user-rank-container {
  margin: 30rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 25rpx rgba(102,126,234,0.3);
}

.user-rank-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10rpx);
}

.user-rank-item .rank-number {
  color: #fff;
}

.user-rank-item .user-nickname {
  color: #fff;
}

.user-rank-item .user-stats {
  color: rgba(255,255,255,0.8);
}

.user-rank-item .total-score {
  color: #fff;
}

.user-rank-item .score-label {
  color: rgba(255,255,255,0.8);
}

/* 加载更多相关样式 */
.load-more, .loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
}

.load-more-text, .loading-more-text, .no-more-text {
  font-size: 24rpx;
  color: #999;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 4rpx;
  margin: 0 auto 10rpx;
}

.loading-more {
  display: flex;
  flex-direction: column;
  align-items: center;
} 