-- 创建数据库
CREATE DATABASE IF NOT EXIS<PERSON> guess DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE guess;

-- 谜题主表(所有类型的基础表)
CREATE TABLE riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    type ENUM('text', 'image', 'audio') NOT NULL COMMENT '谜题类型',
    sub_type VARCHAR(50) NOT NULL COMMENT '子类型(字谜/诗词/国旗等)',
    content TEXT NOT NULL COMMENT '谜题内容(文字/图片URL/音频URL)',
    answer_type VARCHAR(50) NOT NULL COMMENT '答案类型(动物/水果/地名等)',
    answer VARCHAR(255) NOT NULL COMMENT '正确答案',
    analysis TEXT COMMENT '解析说明',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项JSON数组(仅选项模式使用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    INDEX idx_type (type),
    INDEX idx_sub_type (sub_type),
    INDEX idx_answer_type (answer_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='谜题主表';

-- 字谜表
CREATE TABLE word_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content TEXT NOT NULL COMMENT '谜面',
    type ENUM('text','image','audio') NOT NULL COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '正确答案',
    hint TEXT COMMENT '提示',
    category VARCHAR(50) COMMENT '分类(形声字/会意字等)',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    riddle_type VARCHAR(20) DEFAULT 'riddle' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字谜表';

-- 成语题库
CREATE TABLE idiom_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content TEXT NOT NULL COMMENT '题目内容',
    type ENUM('text','image','audio') NOT NULL COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '正确答案',
    analysis TEXT COMMENT '解析说明',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'idiom' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成语题库';

-- 歇后语题库
CREATE TABLE xiehouyu_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content TEXT NOT NULL COMMENT '题目内容',
    type ENUM('text','image','audio') NOT NULL COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '正确答案',
    analysis TEXT COMMENT '解析说明',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'xiehouyu' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='歇后语题库';

-- 动物题库
CREATE TABLE animal_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content TEXT NOT NULL COMMENT '题目内容',
    type ENUM('text','image','audio') NOT NULL COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '正确答案',
    analysis TEXT COMMENT '解析说明',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'animal' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动物题库';

-- 水果题库
CREATE TABLE fruit_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content TEXT NOT NULL COMMENT '题目内容',
    type ENUM('text','image','audio') NOT NULL COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '正确答案',
    analysis TEXT COMMENT '解析说明',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'fruit' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='水果题库';

-- 地名题库
CREATE TABLE place_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content TEXT NOT NULL COMMENT '题目内容',
    type ENUM('text','image','audio') NOT NULL COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '正确答案',
    analysis TEXT COMMENT '解析说明',
    is_domestic TINYINT DEFAULT 1 COMMENT '是否国内(1国内/0国外)',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'place' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地名题库';

-- 人名题库
CREATE TABLE person_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content TEXT NOT NULL COMMENT '题目内容',
    type ENUM('text','image','audio') NOT NULL COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '正确答案',
    analysis TEXT COMMENT '解析说明',
    is_domestic TINYINT DEFAULT 1 COMMENT '是否国内(1国内/0国外)',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'person' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人名题库';

-- 唐诗题库
CREATE TABLE tang_poetry_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content TEXT NOT NULL COMMENT '题目内容',
    type ENUM('text') NOT NULL DEFAULT 'text' COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '诗名',
    analysis TEXT COMMENT '解析说明',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'tang_poetry' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='唐诗题库';

-- 宋词题库
CREATE TABLE song_poetry_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content TEXT NOT NULL COMMENT '题目内容',
    type ENUM('text') NOT NULL DEFAULT 'text' COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '词名',
    analysis TEXT COMMENT '解析说明',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'song_poetry' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='宋词题库';

-- 国旗题库
CREATE TABLE flag_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content VARCHAR(255) NOT NULL COMMENT '图片路径',
    type ENUM('image') NOT NULL DEFAULT 'image' COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '国家名称',
    analysis TEXT COMMENT '解析说明',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'flag' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='国旗题库';

-- 地图轮廓题库
CREATE TABLE map_outline_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content VARCHAR(255) NOT NULL COMMENT '图片路径',
    type ENUM('image') NOT NULL DEFAULT 'image' COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '地名',
    analysis TEXT COMMENT '解析说明',
    is_domestic TINYINT DEFAULT 1 COMMENT '是否国内(1国内/0国外)',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'map_outline' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地图轮廓题库';

-- 车标题库
CREATE TABLE car_logo_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content VARCHAR(255) NOT NULL COMMENT '图片路径',
    type ENUM('image') NOT NULL DEFAULT 'image' COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '品牌名称',
    analysis TEXT COMMENT '解析说明',
    is_domestic TINYINT DEFAULT 1 COMMENT '是否国内(1国内/0国外)',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'car_logo' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车标题库';

-- 品牌logo题库
CREATE TABLE brand_logo_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content VARCHAR(255) NOT NULL COMMENT '图片路径',
    type ENUM('image') NOT NULL DEFAULT 'image' COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '品牌名称',
    analysis TEXT COMMENT '解析说明',
    is_domestic TINYINT DEFAULT 1 COMMENT '是否国内(1国内/0国外)',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'brand_logo' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='品牌logo题库';

-- 电影剧照题库
CREATE TABLE movie_still_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content VARCHAR(255) NOT NULL COMMENT '图片路径',
    type ENUM('image') NOT NULL DEFAULT 'image' COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '电影名称',
    analysis TEXT COMMENT '解析说明',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'movie_still' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电影剧照题库';

-- APP图标题库
CREATE TABLE app_icon_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content VARCHAR(255) NOT NULL COMMENT '图片路径',
    type ENUM('image') NOT NULL DEFAULT 'image' COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT 'APP名称',
    analysis TEXT COMMENT '解析说明',
    is_domestic TINYINT DEFAULT 1 COMMENT '是否国内(1国内/0国外)',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'app_icon' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='APP图标题库';

-- 歌曲音频题库
CREATE TABLE audio_song_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content VARCHAR(255) NOT NULL COMMENT '音频路径',
    type ENUM('audio') NOT NULL DEFAULT 'audio' COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '歌曲名称',
    analysis TEXT COMMENT '解析说明',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'audio_song' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='歌曲音频题库';

-- 动物音频题库
CREATE TABLE audio_animal_riddles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content VARCHAR(255) NOT NULL COMMENT '音频路径',
    type ENUM('audio') NOT NULL DEFAULT 'audio' COMMENT '题目类型',
    answer VARCHAR(255) NOT NULL COMMENT '动物名称',
    analysis TEXT COMMENT '解析说明',
    difficulty TINYINT DEFAULT 3 COMMENT '难度(1-5)',
    points INT DEFAULT 100 COMMENT '分值',
    options JSON COMMENT '选项（如有）',
    riddle_type VARCHAR(20) DEFAULT 'audio_animal' COMMENT '题目分类标识',
    status TINYINT DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动物音频题库';

-- 用户答题记录表
CREATE TABLE user_answers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    riddle_id INT NOT NULL COMMENT '谜题ID',
    table_name VARCHAR(50) COMMENT '题目来源表名',
    user_answer VARCHAR(255) NOT NULL COMMENT '用户答案',
    is_correct TINYINT DEFAULT 0 COMMENT '是否正确',
    score INT DEFAULT 0 COMMENT '得分',
    time_used INT DEFAULT 0 COMMENT '用时(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_riddle_id (riddle_id),
    INDEX idx_table_name (table_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户答题记录表';

-- 用户表
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY COMMENT '用户ID(微信openid)',
    nickname VARCHAR(100) COMMENT '昵称',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    total_score INT DEFAULT 0 COMMENT '总积分',
    total_answers INT DEFAULT 0 COMMENT '总答题数',
    correct_answers INT DEFAULT 0 COMMENT '正确答题数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_total_score (total_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表'; 

-- 创建登录日志表
CREATE TABLE IF NOT EXISTS login_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(64) NOT NULL COMMENT '用户openid',
  login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  ip_address VARCHAR(45) DEFAULT '' COMMENT 'IP地址',
  user_agent TEXT DEFAULT '' COMMENT '用户代理信息',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_login_time (login_time),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录日志表'; 