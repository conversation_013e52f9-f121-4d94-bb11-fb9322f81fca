const express = require('express');
const router = express.Router();
const db = require('../utils/db');
const { validateOpenid } = require('../middleware/auth');

// 获取排行榜数据
router.get('/', validateOpenid, async (req, res) => {
  const { period = 'all' } = req.query;
  const { openid } = req.query;
  
  try {
    let scoreField, answersField, correctField;
    
    // 根据周期选择对应的字段
    switch (period) {
      case 'today':
        scoreField = 'today_score';
        answersField = 'today_answers';
        correctField = 'today_correct_answers';
        break;
      case 'week':
        scoreField = 'week_score';
        answersField = 'week_answers';
        correctField = 'week_correct_answers';
        break;
      case 'month':
        scoreField = 'month_score';
        answersField = 'month_answers';
        correctField = 'month_correct_answers';
        break;
      default: // all
        scoreField = 'total_score';
        answersField = 'total_answers';
        correctField = 'correct_answers';
    }

    // 获取排行榜数据
    const rankingList = await db.execute(
      `SELECT 
        id, nickname, avatar_url,
        ${scoreField} as score,
        ${answersField} as total_answers,
        ${correctField} as correct_answers
       FROM users 
       WHERE ${scoreField} > 0
       ORDER BY ${scoreField} DESC, ${correctField} DESC
       LIMIT 50`
    );

    // 获取用户排名
    let userRank = null;
    if (openid) {
      const userData = await db.execute(
        `SELECT 
          id, nickname, avatar_url,
          ${scoreField} as score,
          ${answersField} as total_answers,
          ${correctField} as correct_answers,
          (
            SELECT COUNT(*) + 1
            FROM users u2
            WHERE u2.${scoreField} > u1.${scoreField}
            OR (u2.${scoreField} = u1.${scoreField} AND u2.${correctField} > u1.${correctField})
          ) as rank
         FROM users u1
         WHERE id = ?`,
        [openid]
      );
      
      if (userData.length > 0) {
        userRank = userData[0];
      }
    }

    res.json({
      code: 0,
      data: {
        list: rankingList,
        userRank,
        period
      }
    });

  } catch (err) {
    console.error('获取排行榜数据失败:', err);
    res.json({ 
      code: 1, 
      msg: '获取排行榜数据失败', 
      error: err.message 
    });
  }
});

module.exports = router; 