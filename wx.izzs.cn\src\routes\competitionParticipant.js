const express = require('express');
const router = express.Router();
const db = require('../utils/db');

/**
 * 更新参与者数据
 * POST /api/competition-participant/update
 */
router.post('/update', async (req, res) => {
  try {
    const { 
      library_id, 
      user_openid, 
      user_name,
      score = 0,
      correct_count = 0,
      total_questions = 0,
      accuracy = 0.00,
      completion_time = 0,
      status = 1
    } = req.body;
    
    // 参数验证
    if (!library_id || !user_openid || !user_name) {
      return res.status(400).json({
        code: -1,
        message: '参数不完整：library_id, user_openid, user_name 为必填项'
      });
    }
    
    // 检查题库是否存在
    const libSql = 'SELECT id FROM comp_libraries WHERE id = ? AND status = 1';
    const libraries = await db.execute(libSql, [library_id]);

    if (libraries.length === 0) {
      return res.status(404).json({
        code: -1,
        message: '题库不存在'
      });
    }

    // 检查用户是否已经有参与记录
    const checkSql = 'SELECT id, score FROM comp_participants WHERE library_id = ? AND user_openid = ?';
    const existing = await db.execute(checkSql, [library_id, user_openid]);

    if (existing.length > 0) {
      // 已有记录，检查是否需要更新
      const existingRecord = existing[0];
      const existingScore = existingRecord.score || 0;
      
      // 只有当新得分大于等于现有得分时才更新
      if (score >= existingScore) {
        const updateSql = `
          UPDATE comp_participants 
          SET 
            user_name = ?,
            score = ?,
            correct_count = ?,
            total_questions = ?,
            accuracy = ?,
            completion_time = ?,
            status = ?,
            completed_at = CASE WHEN ? = 2 THEN NOW() ELSE completed_at END
          WHERE library_id = ? AND user_openid = ?
        `;
        
        await db.execute(updateSql, [
          user_name,
          score,
          correct_count,
          total_questions,
          accuracy,
          completion_time,
          status,
          status, // 用于判断是否设置completed_at
          library_id,
          user_openid
        ]);
        
        res.json({
          code: 0,
          message: '参与者数据更新成功',
          data: {
            library_id: library_id,
            user_openid: user_openid,
            score: score,
            updated: true,
            reason: '得分提升，数据已更新'
          }
        });
      } else {
        res.json({
          code: 0,
          message: '参与者数据未更新',
          data: {
            library_id: library_id,
            user_openid: user_openid,
            score: existingScore,
            updated: false,
            reason: '新得分未超过现有得分，跳过更新'
          }
        });
      }
    } else {
      // 没有记录，创建新记录
      const insertSql = `
        INSERT INTO comp_participants (
          library_id, user_openid, user_name,
          score, correct_count, total_questions, accuracy,
          completion_time, status,
          completed_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const completedAt = status === 2 ? new Date() : null;
      
      await db.execute(insertSql, [
        library_id,
        user_openid,
        user_name,
        score,
        correct_count,
        total_questions,
        accuracy,
        completion_time,
        status,
        completedAt
      ]);
      
      // 如果是新参与者，更新题库参与人数
      const updateLibSql = `
        UPDATE comp_libraries
        SET participant_count = participant_count + 1,
            updated_at = NOW()
        WHERE id = ?
      `;
      await db.execute(updateLibSql, [library_id]);
      
      res.json({
        code: 0,
        message: '参与者数据创建成功',
        data: {
          library_id: library_id,
          user_openid: user_openid,
          score: score,
          updated: true,
          reason: '新参与者，数据已创建'
        }
      });
    }
    
  } catch (error) {
    console.error('更新参与者数据失败:', error);
    res.status(500).json({
      code: -1,
      message: '更新参与者数据失败',
      error: error.message
    });
  }
});

/**
 * 获取参与者排行榜
 * GET /api/competition-participant/ranking/:libraryId
 */
router.get('/ranking/:libraryId', async (req, res) => {
  try {
    const { libraryId } = req.params;
    const { limit = 10 } = req.query;
    
    // 检查题库是否存在
    const libSql = 'SELECT name FROM comp_libraries WHERE id = ? AND status = 1';
    const libraries = await db.execute(libSql, [libraryId]);

    if (libraries.length === 0) {
      return res.status(404).json({
        code: -1,
        message: '题库不存在'
      });
    }

    // 获取排行榜数据
    const sql = `
      SELECT 
        user_name,
        score,
        correct_count,
        total_questions,
        accuracy,
        completion_time,
        completed_at
      FROM comp_participants
      WHERE library_id = ? AND status = 2
      ORDER BY score DESC, completion_time ASC, completed_at ASC
      LIMIT ?
    `;
    
    const participants = await db.execute(sql, [libraryId, parseInt(limit)]);
    
    // 格式化数据
    const ranking = participants.map((participant, index) => ({
      rank: index + 1,
      user_name: participant.user_name,
      score: participant.score,
      correct_count: participant.correct_count,
      total_questions: participant.total_questions,
      accuracy: participant.accuracy,
      completion_time: participant.completion_time,
      completed_at: participant.completed_at
    }));
    
    res.json({
      code: 0,
      message: '获取排行榜成功',
      data: {
        library_name: libraries[0].name,
        ranking: ranking,
        total_participants: ranking.length
      }
    });
    
  } catch (error) {
    console.error('获取参与者排行榜失败:', error);
    res.status(500).json({
      code: -1,
      message: '获取参与者排行榜失败',
      error: error.message
    });
  }
});

/**
 * 获取用户在指定题库的参与记录
 * GET /api/competition-participant/record
 */
router.get('/record', async (req, res) => {
  try {
    const { library_id, user_openid } = req.query;
    
    if (!library_id || !user_openid) {
      return res.status(400).json({
        code: -1,
        message: '参数不完整：library_id 和 user_openid 为必填项'
      });
    }
    
    // 获取参与记录
    const sql = `
      SELECT 
        p.*,
        l.name as library_name
      FROM comp_participants p
      LEFT JOIN comp_libraries l ON p.library_id = l.id
      WHERE p.library_id = ? AND p.user_openid = ?
    `;
    
    const records = await db.execute(sql, [library_id, user_openid]);
    
    if (records.length === 0) {
      return res.json({
        code: 0,
        message: '未找到参与记录',
        data: null
      });
    }
    
    const record = records[0];
    
    res.json({
      code: 0,
      message: '获取参与记录成功',
      data: {
        library_id: record.library_id,
        library_name: record.library_name,
        user_openid: record.user_openid,
        user_name: record.user_name,
        score: record.score,
        correct_count: record.correct_count,
        total_questions: record.total_questions,
        accuracy: record.accuracy,
        completion_time: record.completion_time,
        status: record.status,
        created_at: record.created_at,
        completed_at: record.completed_at
      }
    });
    
  } catch (error) {
    console.error('获取参与记录失败:', error);
    res.status(500).json({
      code: -1,
      message: '获取参与记录失败',
      error: error.message
    });
  }
});

module.exports = router;
