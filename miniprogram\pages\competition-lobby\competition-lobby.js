// 获取应用实例
const app = getApp()
const authManager = require('../../utils/auth')
const env = require('../../env.js')

Page({
  data: {
    userCompetitions: [], // 用户创建的题库记录
    showTypePicker: false, // 是否显示类型选择弹窗
    typePickerList: [], // 当前主类型下的子类型列表
    typePickerTitle: '请选择竞技题目类型和数量', // 弹窗标题
    selectedTypes: [], // 选中的子类型
    typeQuantities: {}, // 存储每个类型的数量
    loading: false, // 加载状态
    competitionName: '', // 题库名称
    selectedLibraryId: null, // 当前选中的题库ID
  },

  // 主类型与子类型映射（竞技模式）
  typeMap: [
    // 文字类
    { label: '字谜', value: 'riddle' },
    { label: '成语', value: 'idiom' },
    { label: '歇后语', value: 'xiehouyu' },
    { label: '动物', value: 'animal' },
    { label: '水果', value: 'fruit' },
    { label: '地名', value: 'place' },
    { label: '人名', value: 'person' },
    { label: '唐诗', value: 'tang_poetry' },
    { label: '宋词', value: 'song_poetry' },
    // 图片类
    { label: '国旗', value: 'flag' },
    { label: '地图轮廓', value: 'map_outline' },
    { label: '车标', value: 'car_logo' },
    { label: '品牌logo', value: 'brand_logo' },
    { label: '电影剧照', value: 'movie_still' },
    { label: 'APP图标', value: 'app_icon' },
    // 音频类
    { label: '歌曲', value: 'audio_song' },
    { label: '动物叫声', value: 'audio_animal' }
  ],

  onLoad() {
    this.loadUserCompetitions()
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadUserCompetitions()
  },

  // 加载用户创建的题库记录
  async loadUserCompetitions() {
    this.setData({ loading: true })

    try {
      const response = await this.getCompetitionLibraries()

      if (response.code === 0) {
        // 处理类型标签的显示名称和数量
        const formattedData = response.data.libraries.map(lib => ({
          ...lib,
          types: this.formatTypeLabels(lib.types),
          typesWithQuantity: this.formatTypesWithQuantity(lib.types, lib.quantities),
          createTime: this.formatDateTime(lib.createTime)
        }))

        this.setData({
          userCompetitions: formattedData,
          loading: false
        })
      } else {
        throw new Error(response.message || '获取题库列表失败')
      }
    } catch (error) {
      console.error('加载题库列表失败:', error)
      this.setData({
        userCompetitions: [],
        loading: false
      })
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  },

  // 调用获取题库列表API
  getCompetitionLibraries() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: env.API_BASE_URL + '/api/competition-library/list',
        method: 'GET',
        data: {
          page: 1,
          limit: 50
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(new Error(`HTTP ${res.statusCode}`))
          }
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  },

  // 格式化类型标签显示名称
  formatTypeLabels(types) {
    const typeMap = {
      'riddle': '字谜',
      'idiom': '成语',
      'xiehouyu': '歇后语',
      'animal': '动物',
      'fruit': '水果',
      'place': '地名',
      'person': '人名',
      'tang_poetry': '唐诗',
      'song_poetry': '宋词',
      'flag': '国旗',
      'map_outline': '地图轮廓',
      'car_logo': '车标',
      'brand_logo': '品牌logo',
      'movie_still': '电影剧照',
      'app_icon': 'APP图标',
      'audio_song': '歌曲',
      'audio_animal': '动物叫声'
    }

    return types.map(type => typeMap[type] || type)
  },

  // 格式化类型标签带数量显示
  formatTypesWithQuantity(types, quantities) {
    const typeMap = {
      'riddle': '字谜',
      'idiom': '成语',
      'xiehouyu': '歇后语',
      'animal': '动物',
      'fruit': '水果',
      'place': '地名',
      'person': '人名',
      'tang_poetry': '唐诗',
      'song_poetry': '宋词',
      'flag': '国旗',
      'map_outline': '地图轮廓',
      'car_logo': '车标',
      'brand_logo': '品牌logo',
      'movie_still': '电影剧照',
      'app_icon': 'APP图标',
      'audio_song': '歌曲',
      'audio_animal': '动物叫声'
    }

    // 预定义的浅彩色背景
    const lightColors = [
      '#FFE5E5', // 浅红
      '#E5F3FF', // 浅蓝
      '#E5FFE5', // 浅绿
      '#FFF5E5', // 浅橙
      '#F0E5FF', // 浅紫
      '#E5FFFF', // 浅青
      '#FFE5F5', // 浅粉
      '#F5FFE5', // 浅黄绿
      '#FFE5CC', // 浅桃
      '#E5E5FF', // 浅蓝紫
      '#CCFFE5', // 浅薄荷绿
      '#FFE5DD', // 浅珊瑚
      '#E5FFCC', // 浅柠檬绿
      '#DDCCFF', // 浅薰衣草
      '#CCFFFF', // 浅天蓝
      '#FFCCDD', // 浅玫瑰
      '#FFFFCC'  // 浅黄
    ]

    return types.map((type, index) => {
      const label = typeMap[type] || type
      const quantity = quantities[type] || 0
      const colorIndex = index % lightColors.length
      return {
        label: label,
        quantity: quantity,
        display: `${label} ×${quantity}`,
        backgroundColor: lightColors[colorIndex]
      }
    })
  },

  // 格式化日期时间
  formatDateTime(dateTime) {
    const date = new Date(dateTime)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}`
  },

  // 点击题库项，展开/收起详情
  onLibraryItemTap(e) {
    const libraryId = e.currentTarget.dataset.id

    // 如果点击的是当前选中的题库，则收起
    if (this.data.selectedLibraryId === libraryId) {
      this.setData({
        selectedLibraryId: null
      })
    } else {
      // 否则展开新的题库
      this.setData({
        selectedLibraryId: libraryId
      })
    }
  },

  // 加入挑战（右侧按钮）
  onJoinChallenge() {
    if (!this.data.selectedLibraryId) {
      wx.showToast({ title: '请先选择一个题库', icon: 'none' })
      return
    }

    const competition = this.data.userCompetitions.find(item => item.id === this.data.selectedLibraryId)

    if (!competition) {
      wx.showToast({ title: '题库不存在', icon: 'none' })
      return
    }

    // 检查登录状态
    const status = authManager.checkLoginStatus()
    if (!status.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '需要登录才能参与同题竞技',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/profile/profile'
            })
          }
        }
      })
      return
    }

    // 直接跳转到竞技页面
    const types = Object.keys(competition.quantities).join(',')
    app.globalData.competitionTypes = types
    app.globalData.competitionQuantities = competition.quantities

    wx.navigateTo({
      url: '/pages/competition/competition?types=' + types + '&competitionId=' + this.data.selectedLibraryId
    })

    // 后台调用API更新数据（不阻塞用户操作）
    this.joinCompetitionInBackground()
  },

  // 后台加入题库（不影响用户体验）
  async joinCompetitionInBackground() {
    try {
      const status = authManager.checkLoginStatus()
      if (!status.isLoggedIn) return

      // 后台调用API
      await this.joinCompetitionLibrary({
        library_id: this.data.selectedLibraryId,
        user_openid: app.globalData.openid,
        user_name: status.userInfo.nickName || status.userInfo.nickname || '匿名用户'
      })

      // 延迟刷新题库列表，避免影响跳转
      setTimeout(() => {
        this.loadUserCompetitions()
      }, 2000) // 2秒后刷新，确保用户已经跳转到答题页面
    } catch (error) {
      console.error('后台加入题库失败:', error)
      // 静默处理错误，不影响用户体验
    }
  },

  // 点击创建题库按钮
  async onCreateCompetition() {
    // 检查登录状态
    const status = authManager.checkLoginStatus()
    if (!status.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '需要登录才能创建题库',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/profile/profile'
            })
          }
        }
      })
      return
    }

    // 检查今日是否已创建题库
    const hasCreatedToday = await this.checkTodayCreationLimit()
    if (hasCreatedToday) {
      this.showCreationLimitModal()
      return
    }

    // 显示类型选择弹窗
    const typeListWithSelected = this.typeMap.map(item => ({
      ...item,
      isSelected: false,
      quantity: 0
    }))

    this.setData({
      showTypePicker: true,
      typePickerList: typeListWithSelected,
      selectedTypes: [],
      typeQuantities: {},
      competitionName: '' // 重置题库名称
    })
  },

  // 增加类型数量
  increaseTypeQuantity(e) {
    const value = e.currentTarget.dataset.value
    const updatedList = this.data.typePickerList.map(item => {
      if (item.value === value) {
        return {
          ...item,
          quantity: (item.quantity || 0) + 5,
          isSelected: true // 自动选中
        }
      }
      return item
    })

    // 获取所有选中的值
    const newSelected = updatedList.filter(item => item.isSelected).map(item => item.value)
    
    // 更新数量映射
    const quantities = {}
    updatedList.forEach(item => {
      if (item.isSelected && item.quantity > 0) {
        quantities[item.value] = item.quantity
      }
    })

    // 添加振动反馈
    wx.vibrateShort({
      type: 'light',
      fail: () => {}
    })

    this.setData({
      typePickerList: updatedList,
      selectedTypes: newSelected,
      typeQuantities: quantities
    })
  },

  // 减少类型数量
  decreaseTypeQuantity(e) {
    const value = e.currentTarget.dataset.value
    const updatedList = this.data.typePickerList.map(item => {
      if (item.value === value) {
        const newQuantity = Math.max((item.quantity || 0) - 5, 0)
        return {
          ...item,
          quantity: newQuantity,
          isSelected: newQuantity > 0 // 数量为0时取消选中
        }
      }
      return item
    })

    // 获取所有选中的值
    const newSelected = updatedList.filter(item => item.isSelected).map(item => item.value)
    
    // 更新数量映射
    const quantities = {}
    updatedList.forEach(item => {
      if (item.isSelected && item.quantity > 0) {
        quantities[item.value] = item.quantity
      }
    })

    // 添加振动反馈
    wx.vibrateShort({
      type: 'light',
      fail: () => {}
    })

    this.setData({
      typePickerList: updatedList,
      selectedTypes: newSelected,
      typeQuantities: quantities
    })
  },

  // 关闭弹窗
  closeTypePicker() {
    this.setData({
      showTypePicker: false,
      competitionName: '' // 关闭时重置名称
    })
  },

  // 题库名称输入处理
  onCompetitionNameInput(e) {
    let value = e.detail.value
    // 只允许中文、英文、数字，限制2-10字符
    value = value.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '')
    if (value.length > 10) {
      value = value.substring(0, 10)
    }
    this.setData({
      competitionName: value
    })
  },

  // 确认创建题库
  async onConfirmCreate() {
    // 验证题库名称
    if (!this.data.competitionName || this.data.competitionName.trim().length < 2) {
      wx.showToast({ title: '请输入2-10字符的题库名称', icon: 'none' })
      return
    }

    if (this.data.selectedTypes.length === 0) {
      wx.showToast({ title: '请至少选择一个类型', icon: 'none' })
      return
    }

    const hasQuantity = Object.values(this.data.typeQuantities).some(qty => qty > 0)
    if (!hasQuantity) {
      wx.showToast({ title: '请至少选择一种题型的数量', icon: 'none' })
      return
    }

    const competitionName = this.data.competitionName // 保存名称

    // 显示加载状态
    wx.showLoading({ title: '创建中...' })

    try {
      // 获取用户信息
      const status = authManager.checkLoginStatus()
      if (!status.isLoggedIn || !status.userInfo) {
        wx.hideLoading()
        wx.showToast({ title: '请先登录', icon: 'none' })
        return
      }

      // 调用创建题库API
      const response = await this.createCompetitionLibrary({
        name: competitionName,
        creator_openid: app.globalData.openid,
        creator_name: status.userInfo.nickName || status.userInfo.nickname || '匿名用户',
        types: this.data.selectedTypes,
        quantities: this.data.typeQuantities
      })

      wx.hideLoading()

      if (response.code === 0) {
        // 创建成功
        this.setData({
          showTypePicker: false,
          competitionName: '' // 重置名称
        })

        wx.showToast({
          title: `创建"${competitionName}"成功`,
          icon: 'success',
          duration: 1500
        })

        // 刷新题库列表（刷新后的数据会包含新创建的记录，用于下次创建时的校验）
        setTimeout(() => {
          this.loadUserCompetitions()
        }, 1000)
      } else {
        wx.showToast({
          title: response.message || '创建失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('创建题库失败:', error)
      wx.showToast({
        title: '创建失败，请重试',
        icon: 'none'
      })
    }
  },

  // 调用创建题库API
  createCompetitionLibrary(data) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: env.API_BASE_URL + '/api/competition-library/create',
        method: 'POST',
        data: data,
        header: {
          'Content-Type': 'application/json'
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(new Error(`HTTP ${res.statusCode}`))
          }
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  },

  // 调用加入题库API
  joinCompetitionLibrary(data) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: env.API_BASE_URL + '/api/competition-library/join',
        method: 'POST',
        data: data,
        header: {
          'Content-Type': 'application/json'
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(new Error(`HTTP ${res.statusCode}`))
          }
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  },



  // 下拉刷新
  onPullDownRefresh() {
    this.loadUserCompetitions()
    wx.stopPullDownRefresh()
  },

  // 检查今日创建限制（使用后端API查询）
  async checkTodayCreationLimit() {
    try {
      const currentUserOpenid = app.globalData.openid

      if (!currentUserOpenid) {
        return false // 未登录用户不限制
      }

      // 调用后端API检查今日创建记录
      const response = await this.checkTodayCreationAPI(currentUserOpenid)

      if (response.code === 0) {
        return response.data.hasCreatedToday // 后端返回是否今日已创建
      } else {
        console.warn('检查今日创建记录失败:', response.message)
        return false // 查询失败时不限制，避免误拦截
      }
    } catch (error) {
      console.error('检查今日创建记录出错:', error)
      return false // 出错时不限制，避免误拦截
    }
  },

  // 调用后端API检查今日创建记录
  checkTodayCreationAPI(openid) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: env.API_BASE_URL + '/api/competition-library/check-today-creation',
        method: 'GET',
        data: {
          openid: openid
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(new Error(`HTTP ${res.statusCode}`))
          }
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  },

  // 显示创建限制弹窗
  showCreationLimitModal() {
    wx.showModal({
      title: '创建限制',
      content: '您今日已创建了专属题库，请明日再来创建，或者邀请朋友创建，您在竞技大厅加入挑战即可！',
      confirmText: '我知道了',
      showCancel: false
    })
  },

  // 空方法用于catchtap防止报错
  noop() {}
})
