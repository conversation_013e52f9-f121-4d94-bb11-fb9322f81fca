const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');

// 获取国旗图片
router.get('/:filename', async (req, res) => {
  // 禁止缓存，强制每次都返回200
  res.setHeader('Cache-Control', 'no-store');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  
  try {
    // 解码文件名，处理 URL 编码的中文字符
    const filename = decodeURIComponent(req.params.filename);
    console.log('原始请求文件名:', req.params.filename);
    console.log('解码后文件名:', filename);
    
    // 构建完整的文件路径
    const flagPath = path.join(__dirname, '../../static/images/flag', filename);
    console.log('完整的文件路径:', flagPath);
    
    // 检查文件是否存在
    try {
      await fs.access(flagPath);
    } catch (error) {
      console.error('文件不存在:', flagPath);
      // 尝试使用原始编码的文件名
      const encodedPath = path.join(__dirname, '../../static/images/flag', req.params.filename);
      try {
        await fs.access(encodedPath);
        console.log('使用编码文件名找到文件:', encodedPath);
        // 设置正确的 Content-Type
        res.setHeader('Content-Type', 'image/png');
        // 直接发送文件
        return res.sendFile(encodedPath);
      } catch (encodedError) {
        console.error('使用编码文件名也未找到文件:', encodedPath);
        return res.status(404).json({
          success: false,
          error: '图片不存在',
          requested: req.params.filename,
          decoded: filename
        });
      }
    }
    
    // 设置正确的 Content-Type
    res.setHeader('Content-Type', 'image/png');
    
    // 直接发送文件
    res.sendFile(flagPath);
    
  } catch (error) {
    console.error('获取国旗图片失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      requested: req.params.filename
    });
  }
});

module.exports = router; 