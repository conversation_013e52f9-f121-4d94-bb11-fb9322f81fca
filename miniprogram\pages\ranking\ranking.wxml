<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">🏆 排行榜</text>
  </view>

  <!-- 标签页切换 -->
  <view class="tabs-container {{isTabFixed ? 'fixed' : ''}}">
    <view class="tabs">
      <view 
        wx:for="{{tabs}}" 
        wx:key="index"
        class="tab-item {{currentTab === index ? 'active' : ''}}"
        data-index="{{index}}"
        bindtap="switchTab"
      >
        {{item}}
      </view>
    </view>
  </view>

  <!-- 占位元素，用于防止吸顶时页面跳动 -->
  <view wx:if="{{isTabFixed}}" class="tabs-placeholder"></view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <text class="error-icon">😅</text>
    <text class="error-text">加载失败，请重试</text>
    <button class="retry-btn" bindtap="retryLoad">重新加载</button>
  </view>

  <!-- 排行榜内容 -->
  <view wx:else class="ranking-content">
    <!-- 前三名特殊展示 -->
    <view wx:if="{{rankingList.length > 0}}" class="podium-container">
      <view class="podium">
        <!-- 第二名 -->
        <view wx:if="{{rankingList[1]}}" class="podium-item second">
          <view class="avatar-container">
            <image 
              class="avatar" 
              src="{{rankingList[1].avatar_url}}" 
              mode="aspectFill" 
              data-userid="{{rankingList[1].id}}"
              binderror="onAvatarError"
            />
            <text class="medal">🥈</text>
          </view>
          <text class="nickname">{{rankingList[1].nickname}}</text>
          <text class="score">{{rankingList[1].score}}分</text>
          <view class="podium-base second-base">2</view>
        </view>

        <!-- 第一名 -->
        <view wx:if="{{rankingList[0]}}" class="podium-item first">
          <view class="avatar-container">
            <image 
              class="avatar" 
              src="{{rankingList[0].avatar_url}}" 
              mode="aspectFill" 
              data-userid="{{rankingList[0].id}}"
              binderror="onAvatarError"
            />
            <text class="medal">🥇</text>
            <view class="crown">👑</view>
          </view>
          <text class="nickname">{{rankingList[0].nickname}}</text>
          <text class="score">{{rankingList[0].score}}分</text>
          <view class="podium-base first-base">1</view>
        </view>

        <!-- 第三名 -->
        <view wx:if="{{rankingList[2]}}" class="podium-item third">
          <view class="avatar-container">
            <image 
              class="avatar" 
              src="{{rankingList[2].avatar_url}}" 
              mode="aspectFill" 
              data-userid="{{rankingList[2].id}}"
              binderror="onAvatarError"
            />
            <text class="medal">🥉</text>
          </view>
          <text class="nickname">{{rankingList[2].nickname}}</text>
          <text class="score">{{rankingList[2].score}}分</text>
          <view class="podium-base third-base">3</view>
        </view>
      </view>
    </view>

    <!-- 排行榜列表 -->
    <view class="ranking-list">
      <view class="list-header">
        <text class="list-title">完整排名</text>
      </view>
      
      <view 
        wx:for="{{rankingList}}" 
        wx:key="id"
        class="ranking-item {{item.rank <= 3 ? 'top-three' : ''}}"
        data-userid="{{item.id}}"
        bindtap="viewUserDetail"
      >
        <view class="rank-info">
          <text class="rank-number">{{item.rank}}</text>
          <text wx:if="{{item.medal}}" class="rank-medal">{{item.medal}}</text>
        </view>

        <view class="user-info">
          <image 
            class="user-avatar" 
            src="{{item.avatar_url}}" 
            mode="aspectFill" 
            data-userid="{{item.id}}"
            data-index="{{index}}"
            binderror="onAvatarError"
          />
          <view class="user-details">
            <text class="user-nickname">{{item.nickname}}</text>
            <text class="user-stats">{{item.periodText}}</text>
          </view>
        </view>

        <view class="score-info">
          <text class="total-score">{{item.score}}</text>
          <text class="score-label">分</text>
        </view>
      </view>

      <!-- 加载更多提示 -->
      <view wx:if="{{hasMore && !loadingMore}}" class="load-more">
        <text class="load-more-text">上拉加载更多</text>
      </view>
      
      <!-- 加载更多中提示 -->
      <view wx:if="{{loadingMore}}" class="loading-more">
        <view class="loading-spinner small"></view>
        <text class="loading-more-text">加载中...</text>
      </view>

      <!-- 没有更多数据提示 -->
      <view wx:if="{{!hasMore && rankingList.length > 0}}" class="no-more">
        <text class="no-more-text">没有更多数据了</text>
      </view>

      <!-- 空状态 -->
      <view wx:if="{{rankingList.length === 0}}" class="empty-container">
        <text class="empty-icon">📊</text>
        <text class="empty-text">暂无排行榜数据</text>
      </view>
    </view>
  </view>
</view> 