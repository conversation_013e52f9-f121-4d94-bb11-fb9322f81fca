<!-- 主页布局 -->
<view class="container">
  <!-- 背景图片 -->
  <image class="background-image" src="{{backgroundImage}}" mode="aspectFill"></image>
  <!-- 半透明遮罩 -->
  <view class="background-mask"></view>
  
  <!-- 顶部用户信息 -->
  <view class="user-info">
    <!-- 头像区域 -->
    <view class="user-avatar" bindtap="{{hasUserInfo ? 'navigateToProfileSetup' : 'doLogin'}}">
      <image 
        wx:if="{{userInfo.avatarUrl}}" 
        src="{{userInfo.avatarUrl}}" 
        mode="aspectFill"
        binderror="onAvatarError"
      ></image>
      <view wx:else class="avatar-placeholder">
        <text class="avatar-icon">👤</text>
      </view>
      <view class="avatar-status" wx:if="{{!hasUserInfo}}">
        <text class="status-text">点击登录</text>
      </view>
    </view>

    <!-- 用户数据区域 -->
    <view class="user-stats" bindtap="navigateToProfile">
      <block wx:if="{{hasUserInfo}}">
        <view class="stat-item">
          <text class="stat-value">{{userStats.totalScore}}</text>
          <text class="stat-label">总积分</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.correctAnswers}}</text>
          <text class="stat-label">答对题数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.rank}}</text>
          <text class="stat-label">当前排名</text>
        </view>
      </block>
      <view wx:else class="login-prompt">
        <text class="login-desc">登录后可保存记录和参与排名</text>
      </view>
    </view>
  </view>

  <!-- 标题 -->
  <view class="header">
    <text class="title">猜谜识图 All in 1</text>
    <text class="subtitle">挑战你的知识储备</text>
  </view>

  <!-- 谜题类型卡片 -->
  <view class="card-container">
    <!-- 文字类谜题卡片 -->
    <view class="card text-card" bindtap="navigateToGame" data-type="text">
      <view class="card-icon">📝</view>
      <view class="card-content">
        <text class="card-title">文字解谜</text>
        <text class="card-subtitle">字谜、成语、歇后语、唐诗宋词、人名等</text>
      </view>
      <view class="card-arrow">></view>
    </view>

    <!-- 图片类谜题卡片 -->
    <view class="card image-card" bindtap="navigateToGame" data-type="image">
      <view class="card-icon">🖼️</view>
      <view class="card-content">
        <text class="card-title">识图辩物</text>
        <text class="card-subtitle">国旗、地图、车标、Logo、剧照、APP等</text>
      </view>
      <view class="card-arrow">></view>
    </view>

    <!-- 音频类谜题卡片 -->
    <view class="card audio-card" bindtap="navigateToGame" data-type="audio">
      <view class="card-icon">🎵</view>
      <view class="card-content">
        <text class="card-title">听声辩物</text>
        <text class="card-subtitle">听歌识曲、听声辩物等</text>
      </view>
      <view class="card-arrow">></view>
    </view>
    
    <!-- 同题竞技卡片 -->
    <view class="card competition-card" bindtap="navigateToGame" data-type="competition">
      <view class="card-icon">🏆</view>
      <view class="card-content">
        <text class="card-title">同题竞技</text>
        <text class="card-subtitle">自由组合题目类型和数量，挑战细分领域No.1</text>
      </view>
      <view class="card-arrow">></view>
    </view>
  </view>

  <!-- 类型选择弹窗 -->
  <view wx:if="{{showTypePicker}}" class="type-picker-mask" catchtap="closeTypePicker">
    <view class="type-picker-popup {{isCompetitionMode ? 'competition-mode' : ''}}" catchtap="noop">
      <view class="type-picker-title">{{typePickerTitle}}</view>
      <view class="type-picker-list">
        <block wx:for="{{typePickerList}}" wx:for-item="typeItem" wx:for-index="typeIndex" wx:key="value">
          <!-- 竞技模式 - 带数量选择器的项 -->
          <view wx:if="{{isCompetitionMode}}" class="type-picker-item-competition {{typeItem.isSelected ? 'active' : ''}}">
            <view class="type-item-label">{{typeItem.label}}</view>
            <view class="type-item-quantity-selector">
              <view class="quantity-btn decrease" catchtap="decreaseTypeQuantity" data-value="{{typeItem.value}}">-</view>
              <view class="quantity-value">{{typeItem.quantity}}</view>
              <view class="quantity-btn increase" catchtap="increaseTypeQuantity" data-value="{{typeItem.value}}">+</view>
            </view>
          </view>
          
          <!-- 普通模式 - 只有选择状态的项 -->
          <view wx:else class="type-picker-item {{typeItem.isSelected ? 'active' : ''}}" 
                data-value="{{typeItem.value}}" 
                catchtap="onTypePickerToggle">
            {{typeItem.label}}
          </view>
        </block>
      </view>
      <view class="type-picker-divider"></view>
      <view class="type-picker-btns">
        <view class="type-picker-btn" catchtap="closeTypePicker">取消</view>
        <view class="type-picker-btn start" catchtap="onTypePickerStart">{{isCompetitionMode ? '创建' : '开始'}}</view>
      </view>
    </view>
  </view>
</view> 