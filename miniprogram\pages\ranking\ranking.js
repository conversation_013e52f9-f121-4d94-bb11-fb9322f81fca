// 引入环境变量
const env = require('../../env.js')
const app = getApp()
const { processUsersAvatars, processUserAvatar, generateFallbackAvatar } = require('../../utils/avatarHelper')
const authManager = require('../../utils/auth')

Page({
  data: {
    currentTab: 0, // 当前选中的标签页
    tabs: ['总榜', '今日', '本周', '本月'],
    rankingList: [], // 排行榜数据
    loading: true,
    error: false,
    currentPeriod: 'all',
    // 分页相关
    page: 1,
    pageSize: 20,
    hasMore: true,
    loadingMore: false,
    // 吸顶相关
    isTabFixed: false,
    tabOffsetTop: 0
  },

  async onLoad() {
    try {
      wx.showLoading({ title: '加载中...' })
      await this.initAuth()
      // 获取标签页位置信息
      this.getTabOffsetTop()
      await this.getRankingData()
    } catch (error) {
      console.error('排行榜初始化失败:', error)
      this.handleError()
    } finally {
      wx.hideLoading()
    }
  },

  onShow() {
    // 每次显示时检查登录状态
    this.checkAuthStatus()
  },

  // 初始化认证状态
  async initAuth() {
    try {
      // 静默登录，确保有openid用于排行榜显示
      const result = await authManager.silentLogin()
      if (!result.success) {
        throw new Error('登录失败')
      }
    } catch (error) {
      console.error('初始化认证失败:', error)
      throw error
    }
  },

  // 检查认证状态
  checkAuthStatus() {
    const status = authManager.checkLoginStatus()
    if (status.isLoggedIn) {
      // 如果登录状态发生变化，重新获取排行榜数据
      this.getRankingData()
    }
  },

  // 获取标签页位置信息
  getTabOffsetTop() {
    const query = wx.createSelectorQuery()
    query.select('.tabs-container').boundingClientRect()
    query.exec(res => {
      if (res[0]) {
        this.setData({
          tabOffsetTop: res[0].top
        })
      }
    })
  },

  // 监听页面滚动
  onPageScroll(e) {
    const { scrollTop } = e
    const { tabOffsetTop } = this.data
    // 当滚动位置超过标签页位置时，固定标签页
    this.setData({
      isTabFixed: scrollTop >= tabOffsetTop - 20 // 20rpx 的间距
    })
  },

  // 切换标签页
  switchTab(e) {
    const index = Number(e.currentTarget.dataset.index)
    if (this.data.currentTab === index) return
    
    this.setData({
      currentTab: index,
      loading: true,
      error: false,
      rankingList: [],
      page: 1,
      hasMore: true
    })
    
    this.getRankingData()
  },

  // 获取排行榜数据
  async getRankingData(isLoadMore = false) {
    if (isLoadMore && !this.data.hasMore) return
    
    const { currentTab, tabs, page, pageSize } = this.data
    const safeTab = Math.max(0, Math.min(currentTab, tabs.length - 1))
    let period = 'all'
    switch (safeTab) {
      case 1: period = 'today'; break
      case 2: period = 'week'; break
      case 3: period = 'month'; break
    }

    if (isLoadMore) {
      this.setData({ loadingMore: true })
    } else {
      this.setData({ loading: true, error: false })
    }

    try {
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: env.API_BASE_URL + '/api/ranking',
          method: 'GET',
          data: { 
            period,
            openid: app.globalData.openid,
            page,
            pageSize
          },
          success: resolve,
          fail: reject
        })
      })

      if (res.data && res.data.code === 0) {
        let newList = res.data.data.list || []
        // 处理头像
        newList = processUsersAvatars(newList)

        // 处理数据
        const processedList = newList.map((item, index) => ({
          ...item,
          rank: (page - 1) * pageSize + index + 1,
          medal: this.getMedal((page - 1) * pageSize + index + 1),
          accuracy: item.total_answers > 0 ? 
            Math.round((item.correct_answers / item.total_answers) * 100) : 0,
          periodText: this.getPeriodText(safeTab, item)
        }))

        // 更新数据
        this.setData({
          rankingList: isLoadMore ? [...this.data.rankingList, ...processedList] : processedList,
          hasMore: newList.length === pageSize,
          loading: false,
          loadingMore: false,
          error: false,
          currentPeriod: period
        })
      } else {
        throw new Error(res.data?.msg || '获取排行榜失败')
      }
    } catch (error) {
      console.error('获取排行榜数据失败:', error)
      this.handleError()
      if (isLoadMore) {
        this.setData({ loadingMore: false })
      }
    }
  },

  // 获取周期提示文本
  getPeriodText(tabIndex, item) {
    const { tabs } = this.data;
    const period = tabs[tabIndex];
    
    // 根据不同的周期返回不同的提示文本
    switch (period) {
      case '今日':
        return `今日答题${item.total_answers}次，正确${item.correct_answers}次`;
      case '本周':
        return `本周答题${item.total_answers}次，正确${item.correct_answers}次`;
      case '本月':
        return `本月答题${item.total_answers}次，正确${item.correct_answers}次`;
      default: // 总榜
        return `累计答题${item.total_answers}次，正确${item.correct_answers}次`;
    }
  },

  // 头像加载错误处理
  onAvatarError(e) {
    const { dataset } = e.currentTarget
    const { userid, index } = dataset
    
    // 生成备用头像
    const fallbackAvatar = generateFallbackAvatar(userid)
    
    if (typeof index !== 'undefined') {
      // 排行榜列表中的头像
      const updatedList = [...this.data.rankingList]
      if (updatedList[index]) {
        updatedList[index].avatar_url = fallbackAvatar
        this.setData({ rankingList: updatedList })
      }
    } else {
      // 用户排名中的头像
      if (this.data.userRank && this.data.userRank.id === userid) {
        this.setData({
          'userRank.avatar_url': fallbackAvatar
        })
      }
    }
  },

  // 获取奖章图标
  getMedal(rank) {
    switch (rank) {
      case 1: return '🥇'
      case 2: return '🥈'
      case 3: return '🥉'
      default: return ''
    }
  },

  // 处理错误
  handleError() {
    this.setData({
      loading: false,
      error: true
    })
  },

  // 重新加载
  retryLoad() {
    this.setData({ 
      loading: true, 
      error: false 
    })
    this.getRankingData()
  },

  // 触底加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.setData({
        page: this.data.page + 1
      }, () => {
        this.getRankingData(true)
      })
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({
      page: 1,
      hasMore: true
    }, () => {
      this.getRankingData()
      wx.stopPullDownRefresh()
    })
  },

  // 查看用户详情
  viewUserDetail(e) {
    const userId = e.currentTarget.dataset.userid
    // 这里可以跳转到用户详情页或显示用户信息弹窗
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '快来看看猜谜排行榜，挑战猜谜达人！',
      path: '/pages/ranking/ranking',
      imageUrl: '/assets/images/share.png'
    }
  }
}) 