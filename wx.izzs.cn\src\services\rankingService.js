const mysql = require('mysql2/promise');
const dbConfig = require('../config/db');
const { processUserAvatar, processUsersAvatars } = require('../utils/avatarHelper');

// 获取排行榜
exports.getRanking = async (period = 'all', userId = null) => {
  const conn = await mysql.createConnection(dbConfig);
  
  try {
    let whereClause = '';
    let params = [];
    
    // 根据时间段构建查询条件
    switch (period) {
      case 'today':
        whereClause = `WHERE DATE(us.date) = CURDATE()`;
        break;
      case 'week':
        whereClause = `WHERE YEARWEEK(us.date, 1) = YEARWEEK(CURDATE(), 1)`;
        break;
      case 'month':
        whereClause = `WHERE YEAR(us.date) = YEAR(CURDATE()) AND MONTH(us.date) = MONTH(CURDATE())`;
        break;
      case 'all':
      default:
        // 使用用户总积分排序
        break;
    }
    
    let rankingList = [];
    let userRank = null;
    
    if (period === 'all') {
      // 总排行榜 - 使用用户表的总积分
      const [rows] = await conn.execute(`
        SELECT 
          u.id,
          u.nickname,
          u.avatar_url,
          u.total_score,
          u.total_answers,
          u.correct_answers
        FROM users u 
        WHERE u.total_score > 0
        ORDER BY u.total_score DESC, u.correct_answers DESC 
        LIMIT 100
      `);
      rankingList = processUsersAvatars(rows);
      
      // 查询用户排名
      if (userId) {
        const [userRows] = await conn.execute(`
          SELECT 
            u.id,
            u.nickname,
            u.avatar_url,
            u.total_score,
            u.total_answers,
            u.correct_answers,
            (SELECT COUNT(*) + 1 FROM users u2 WHERE u2.total_score > u.total_score) as rank
          FROM users u 
          WHERE u.id = ?
        `, [userId]);
        
        if (userRows.length > 0) {
          userRank = processUserAvatar(userRows[0]);
        }
      }
    } else {
      // 按时间段排行榜 - 使用用户统计表
      const [rows] = await conn.execute(`
        SELECT 
          u.id,
          u.nickname,
          u.avatar_url,
          COALESCE(SUM(us.daily_score), 0) as total_score,
          COALESCE(SUM(us.daily_answers), 0) as total_answers,
          COALESCE(SUM(us.daily_correct), 0) as correct_answers
        FROM users u 
        LEFT JOIN user_stats us ON u.id = us.user_id ${whereClause}
        GROUP BY u.id, u.nickname, u.avatar_url
        HAVING total_score > 0
        ORDER BY total_score DESC, correct_answers DESC 
        LIMIT 100
      `);
      rankingList = processUsersAvatars(rows);
      
      // 查询用户在该时间段的排名
      if (userId) {
        const [userRows] = await conn.execute(`
          SELECT 
            u.id,
            u.nickname,
            u.avatar_url,
            COALESCE(SUM(us.daily_score), 0) as total_score,
            COALESCE(SUM(us.daily_answers), 0) as total_answers,
            COALESCE(SUM(us.daily_correct), 0) as correct_answers
          FROM users u 
          LEFT JOIN user_stats us ON u.id = us.user_id ${whereClause}
          WHERE u.id = ?
          GROUP BY u.id, u.nickname, u.avatar_url
        `, [userId]);
        
        if (userRows.length > 0 && userRows[0].total_score > 0) {
          // 计算排名
          const [rankRows] = await conn.execute(`
            SELECT COUNT(*) + 1 as rank
            FROM (
              SELECT 
                u.id,
                COALESCE(SUM(us.daily_score), 0) as total_score
              FROM users u 
              LEFT JOIN user_stats us ON u.id = us.user_id ${whereClause}
              GROUP BY u.id
              HAVING total_score > ?
            ) as ranked_users
          `, [userRows[0].total_score]);
          
          userRank = processUserAvatar({
            ...userRows[0],
            rank: rankRows[0].rank
          });
        }
      }
    }
    
    return {
      rankingList,
      userRank
    };
    
  } finally {
    await conn.end();
  }
}; 