# 头像上传功能部署说明

## 新增功能
- 用户头像上传到服务器 `/static/pic` 目录
- 以用户 openid 为文件名保存
- 自动更新数据库中的头像URL
- 支持 jpg, jpeg, png, gif, webp 格式
- 文件大小限制 5MB

## 部署步骤

### 1. 安装新依赖
```bash
cd wx.izzs.cn
npm install multer@1.4.5-lts.1
```

### 2. 创建头像存储目录
```bash
mkdir -p static/pic
chmod 755 static/pic
```

### 3. 重启服务
```bash
# 如果使用 PM2
pm2 restart guessing-game-server

# 如果使用 systemd
sudo systemctl restart guessing-game-server

# 如果手动运行
# 先停止当前进程，然后重新启动
npm start
```

### 4. 验证部署
- 访问 `http://your-domain.com/static/pic/` 应该返回404（目录为空）
- 小程序中选择头像应该能够成功上传
- 检查 `static/pic` 目录下是否生成了以 openid 命名的头像文件

## 文件结构
```
wx.izzs.cn/
├── static/
│   └── pic/                    # 头像存储目录
│       ├── openid1.jpg        # 用户头像文件
│       ├── openid2.png
│       └── ...
├── src/
│   ├── controllers/
│   │   └── userController.js   # 新增头像上传功能
│   ├── services/
│   │   └── userService.js      # 新增头像URL更新方法
│   └── routes/
│       └── user.js             # 新增头像上传路由
└── package.json                # 新增 multer 依赖
```

## API 接口

### 上传头像
```
POST /api/user/upload-avatar
Content-Type: multipart/form-data

参数:
- avatar: 头像文件
- openid: 用户ID

返回:
{
  "code": 0,
  "data": {
    "avatarUrl": "/static/pic/openid.jpg"
  },
  "msg": "头像上传成功"
}
```

## 注意事项

1. **目录权限**: 确保 `static/pic` 目录有写入权限
2. **文件大小**: 当前限制为 5MB，可在控制器中调整
3. **文件格式**: 支持常见图片格式，可在控制器中扩展
4. **旧文件**: 同一用户上传新头像会覆盖旧文件
5. **URL一致性**: 数据库中的头像URL格式为 `/static/pic/openid.ext`

## 故障排除

### 头像上传失败
1. 检查 `static/pic` 目录是否存在且有写入权限
2. 检查文件大小是否超过 5MB
3. 检查文件格式是否支持
4. 查看服务器日志获取详细错误信息

### 头像显示不了
1. 检查静态文件服务是否正常运行
2. 检查文件是否实际保存到 `static/pic` 目录
3. 检查数据库中的头像URL是否正确
4. 检查服务器防火墙设置

### 数据库更新失败
1. 检查数据库连接是否正常
2. 检查用户是否存在
3. 查看数据库日志获取详细错误信息 