-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-05-24 22:51:30
-- 服务器版本： 5.7.40-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `guess`
--

-- --------------------------------------------------------

--
-- 表的结构 `achievements`
--

CREATE TABLE `achievements` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '成就名称',
  `description` text COMMENT '成就描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '成就图标',
  `condition_type` enum('score','answers','streak','accuracy','type_master') NOT NULL COMMENT '条件类型',
  `condition_value` int(11) NOT NULL COMMENT '条件值',
  `points` int(11) DEFAULT '0' COMMENT '成就积分',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成就表';

--
-- 转存表中的数据 `achievements`
--

INSERT INTO `achievements` (`id`, `name`, `description`, `icon`, `condition_type`, `condition_value`, `points`, `created_at`) VALUES
(1, '初出茅庐', '完成第一道题目', '🌱', 'answers', 1, 10, '2025-05-23 11:47:57'),
(2, '小试牛刀', '累计答对10道题', '🔰', 'answers', 10, 20, '2025-05-23 11:47:57'),
(3, '渐入佳境', '累计答对50道题', '📈', 'answers', 50, 50, '2025-05-23 11:47:57'),
(4, '炉火纯青', '累计答对100道题', '🔥', 'answers', 100, 100, '2025-05-23 11:47:57'),
(5, '登峰造极', '累计答对500道题', '🏔️', 'answers', 500, 200, '2025-05-23 11:47:57'),
(6, '百分百', '单轮答题正确率100%', '💯', 'accuracy', 100, 30, '2025-05-23 11:47:57'),
(7, '连胜王', '连续答对10题', '🏆', 'streak', 10, 40, '2025-05-23 11:47:57'),
(8, '成语大师', '成语类题目答对50题', '📚', 'type_master', 50, 60, '2025-05-23 11:47:57'),
(9, '古诗词达人', '诗词类题目答对30题', '🎭', 'type_master', 30, 60, '2025-05-23 11:47:57'),
(10, '图像识别专家', '图片类题目答对40题', '🖼️', 'type_master', 40, 60, '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `animal_riddles`
--

CREATE TABLE `animal_riddles` (
  `id` int(11) NOT NULL,
  `content` text NOT NULL COMMENT '题目内容',
  `type` enum('text','image','audio') NOT NULL COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '正确答案',
  `analysis` text COMMENT '解析说明',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'animal' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动物题库';

--
-- 转存表中的数据 `animal_riddles`
--

INSERT INTO `animal_riddles` (`id`, `content`, `type`, `answer`, `analysis`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(1, '头上有角，身上有毛，爱吃青草会产奶', 'text', '牛', '描述牛的特征', 1, 100, NULL, 'animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(2, '会飞不会走，爱吃虫子会筑巢', 'text', '燕子', '描述燕子的特征', 1, 100, NULL, 'animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(3, '会游泳不会飞，爱吃竹子会打洞', 'text', '熊猫', '描述熊猫的特征', 2, 100, NULL, 'animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(4, '身穿铠甲，行动缓慢，喜欢吃菜叶', 'text', '乌龟', '描述乌龟的特征', 1, 100, NULL, 'animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(5, '身上有斑点，跑得很快，是草原之王', 'text', '猎豹', '描述猎豹的特征', 2, 100, NULL, 'animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(6, '会变色，舌头长，喜欢吃虫子', 'text', '变色龙', '描述变色龙的特征', 2, 100, NULL, 'animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(7, '会爬树，喜欢吃香蕉，尾巴长', 'text', '猴子', '描述猴子的特征', 1, 100, NULL, 'animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(8, '会游泳，嘴巴扁，喜欢吃鱼', 'text', '鸭子', '描述鸭子的特征', 1, 100, NULL, 'animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(9, '会飞，晚上活动，喜欢吃蚊子', 'text', '蝙蝠', '描述蝙蝠的特征', 2, 100, NULL, 'animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(10, '身上有袋，跳跃能力强，是澳大利亚的代表动物', 'text', '袋鼠', '描述袋鼠的特征', 2, 100, NULL, 'animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `app_icon_riddles`
--

CREATE TABLE `app_icon_riddles` (
  `id` int(11) NOT NULL,
  `content` varchar(255) NOT NULL COMMENT '图片路径',
  `type` enum('image') NOT NULL DEFAULT 'image' COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT 'APP名称',
  `analysis` text COMMENT '解析说明',
  `is_domestic` tinyint(4) DEFAULT '1' COMMENT '是否国内(1国内/0国外)',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'app_icon' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='APP图标题库';

--
-- 转存表中的数据 `app_icon_riddles`
--

INSERT INTO `app_icon_riddles` (`id`, `content`, `type`, `answer`, `analysis`, `is_domestic`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(966, 'app/233乐园.png', 'image', '233乐园', NULL, 1, 3, 100, NULL, 'app_icon', 1, '2025-05-24 14:32:51', '2025-05-24 14:32:51'),
(967, 'app/360手机卫士.png', 'image', '360手机卫士', NULL, 1, 3, 100, NULL, 'app_icon', 1, '2025-05-24 14:32:51', '2025-05-24 14:32:51'),
(968, 'app/4399游戏盒.png', 'image', '4399游戏盒', NULL, 1, 3, 100, NULL, 'app_icon', 1, '2025-05-24 14:32:51', '2025-05-24 14:32:51'),
(969, 'app/500网.png', 'image', '500网', NULL, 1, 3, 100, NULL, 'app_icon', 1, '2025-05-24 14:32:51', '2025-05-24 14:32:51'),
(1918, 'app/usapp/Zelle.png', 'image', 'Zelle', NULL, 0, 3, 100, NULL, 'app_icon', 0, '2025-05-24 14:32:52', '2025-05-24 14:32:52'),
(1919, 'app/usapp/Zillow.png', 'image', 'Zillow', NULL, 0, 3, 100, NULL, 'app_icon', 0, '2025-05-24 14:32:52', '2025-05-24 14:32:52'),
(1920, 'app/usapp/μBrowser.png', 'image', 'μBrowser', NULL, 0, 3, 100, NULL, 'app_icon', 0, '2025-05-24 14:32:52', '2025-05-24 14:32:52');

-- --------------------------------------------------------

--
-- 表的结构 `audio_animal_riddles`
--

CREATE TABLE `audio_animal_riddles` (
  `id` int(11) NOT NULL,
  `content` varchar(255) NOT NULL COMMENT '音频路径',
  `type` enum('audio') NOT NULL DEFAULT 'audio' COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '动物名称',
  `analysis` text COMMENT '解析说明',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'audio_animal' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动物音频题库';

--
-- 转存表中的数据 `audio_animal_riddles`
--

INSERT INTO `audio_animal_riddles` (`id`, `content`, `type`, `answer`, `analysis`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(1, '/static/audio/animal/cat.mp3', 'audio', '猫', '猫的叫声', 1, 100, NULL, 'audio_animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(2, '/static/audio/animal/dog.mp3', 'audio', '狗', '狗的叫声', 1, 100, NULL, 'audio_animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(3, '/static/audio/animal/cow.mp3', 'audio', '牛', '牛的叫声', 1, 100, NULL, 'audio_animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(4, '/static/audio/animal/horse.mp3', 'audio', '马', '马的嘶鸣', 1, 100, NULL, 'audio_animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(5, '/static/audio/animal/sheep.mp3', 'audio', '羊', '羊的叫声', 1, 100, NULL, 'audio_animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(6, '/static/audio/animal/duck.mp3', 'audio', '鸭子', '鸭子的叫声', 1, 100, NULL, 'audio_animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(7, '/static/audio/animal/chicken.mp3', 'audio', '鸡', '鸡的叫声', 1, 100, NULL, 'audio_animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(8, '/static/audio/animal/elephant.mp3', 'audio', '大象', '大象的叫声', 1, 100, NULL, 'audio_animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(9, '/static/audio/animal/lion.mp3', 'audio', '狮子', '狮子的吼声', 1, 100, NULL, 'audio_animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(10, '/static/audio/animal/monkey.mp3', 'audio', '猴子', '猴子的叫声', 1, 100, NULL, 'audio_animal', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `audio_song_riddles`
--

CREATE TABLE `audio_song_riddles` (
  `id` int(11) NOT NULL,
  `content` varchar(255) NOT NULL COMMENT '音频路径',
  `type` enum('audio') NOT NULL DEFAULT 'audio' COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '歌曲名称',
  `analysis` text COMMENT '解析说明',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'audio_song' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='歌曲音频题库';

--
-- 转存表中的数据 `audio_song_riddles`
--

INSERT INTO `audio_song_riddles` (`id`, `content`, `type`, `answer`, `analysis`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(1, '/static/audio/song/moon.mp3', 'audio', '月亮代表我的心', '邓丽君经典歌曲', 1, 100, NULL, 'audio_song', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(2, '/static/audio/song/qinghuaci.mp3', 'audio', '青花瓷', '周杰伦中国风歌曲', 1, 100, NULL, 'audio_song', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(3, '/static/audio/song/letitgo.mp3', 'audio', 'Let It Go', '冰雪奇缘主题曲', 1, 100, NULL, 'audio_song', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(4, '/static/audio/song/youth.mp3', 'audio', '青春修炼手册', 'TFBOYS代表作', 1, 100, NULL, 'audio_song', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(5, '/static/audio/song/shapeofyou.mp3', 'audio', 'Shape of You', 'Ed Sheeran流行歌曲', 1, 100, NULL, 'audio_song', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(6, '/static/audio/song/seeYouAgain.mp3', 'audio', 'See You Again', '速度与激情主题曲', 1, 100, NULL, 'audio_song', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(7, '/static/audio/song/hello.mp3', 'audio', 'Hello', 'Adele流行歌曲', 1, 100, NULL, 'audio_song', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(8, '/static/audio/song/uptownfunk.mp3', 'audio', 'Uptown Funk', 'Bruno Mars流行歌曲', 1, 100, NULL, 'audio_song', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(9, '/static/audio/song/tiankong.mp3', 'audio', '天空之城', '久石让经典钢琴曲', 1, 100, NULL, 'audio_song', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(10, '/static/audio/song/daoxiang.mp3', 'audio', '稻香', '周杰伦励志歌曲', 1, 100, NULL, 'audio_song', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `brand_logo_riddles`
--

CREATE TABLE `brand_logo_riddles` (
  `id` int(11) NOT NULL,
  `content` varchar(255) NOT NULL COMMENT '图片路径',
  `type` enum('image') NOT NULL DEFAULT 'image' COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '品牌名称',
  `analysis` text COMMENT '解析说明',
  `is_domestic` tinyint(4) DEFAULT '1' COMMENT '是否国内(1国内/0国外)',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'brand_logo' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='品牌logo题库';

--
-- 转存表中的数据 `brand_logo_riddles`
--

INSERT INTO `brand_logo_riddles` (`id`, `content`, `type`, `answer`, `analysis`, `is_domestic`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(1, '/static/images/brand_logo/apple.png', 'image', '苹果', '美国科技品牌', 0, 1, 100, NULL, 'brand_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(2, '/static/images/brand_logo/huawei.png', 'image', '华为', '中国科技品牌', 1, 1, 100, NULL, 'brand_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(3, '/static/images/brand_logo/samsung.png', 'image', '三星', '韩国科技品牌', 0, 1, 100, NULL, 'brand_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(4, '/static/images/brand_logo/lenovo.png', 'image', '联想', '中国科技品牌', 1, 1, 100, NULL, 'brand_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(5, '/static/images/brand_logo/sony.png', 'image', '索尼', '日本科技品牌', 0, 1, 100, NULL, 'brand_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(6, '/static/images/brand_logo/microsoft.png', 'image', '微软', '美国科技品牌', 0, 1, 100, NULL, 'brand_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(7, '/static/images/brand_logo/tencent.png', 'image', '腾讯', '中国互联网公司', 1, 1, 100, NULL, 'brand_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(8, '/static/images/brand_logo/alibaba.png', 'image', '阿里巴巴', '中国互联网公司', 1, 1, 100, NULL, 'brand_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(9, '/static/images/brand_logo/baidu.png', 'image', '百度', '中国互联网公司', 1, 1, 100, NULL, 'brand_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(10, '/static/images/brand_logo/google.png', 'image', '谷歌', '美国互联网公司', 0, 1, 100, NULL, 'brand_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `car_logo_riddles`
--

CREATE TABLE `car_logo_riddles` (
  `id` int(11) NOT NULL,
  `content` varchar(255) NOT NULL COMMENT '图片路径',
  `type` enum('image') NOT NULL DEFAULT 'image' COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '品牌名称',
  `analysis` text COMMENT '解析说明',
  `is_domestic` tinyint(4) DEFAULT '1' COMMENT '是否国内(1国内/0国外)',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'car_logo' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车标题库';

--
-- 转存表中的数据 `car_logo_riddles`
--

INSERT INTO `car_logo_riddles` (`id`, `content`, `type`, `answer`, `analysis`, `is_domestic`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(1, '/static/images/car_logo/bmw.png', 'image', '宝马', '德国汽车品牌', 0, 1, 100, NULL, 'car_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(2, '/static/images/car_logo/audi.png', 'image', '奥迪', '德国汽车品牌', 0, 1, 100, NULL, 'car_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(3, '/static/images/car_logo/benz.png', 'image', '奔驰', '德国汽车品牌', 0, 1, 100, NULL, 'car_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(4, '/static/images/car_logo/toyota.png', 'image', '丰田', '日本汽车品牌', 0, 1, 100, NULL, 'car_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(5, '/static/images/car_logo/honda.png', 'image', '本田', '日本汽车品牌', 0, 1, 100, NULL, 'car_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(6, '/static/images/car_logo/tesla.png', 'image', '特斯拉', '美国汽车品牌', 0, 1, 100, NULL, 'car_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(7, '/static/images/car_logo/ford.png', 'image', '福特', '美国汽车品牌', 0, 1, 100, NULL, 'car_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(8, '/static/images/car_logo/byd.png', 'image', '比亚迪', '中国汽车品牌', 1, 1, 100, NULL, 'car_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(9, '/static/images/car_logo/hongqi.png', 'image', '红旗', '中国汽车品牌', 1, 1, 100, NULL, 'car_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(10, '/static/images/car_logo/geely.png', 'image', '吉利', '中国汽车品牌', 1, 1, 100, NULL, 'car_logo', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `flag_riddles`
--

CREATE TABLE `flag_riddles` (
  `id` int(11) NOT NULL,
  `content` varchar(255) NOT NULL COMMENT '图片路径',
  `type` enum('image') NOT NULL DEFAULT 'image' COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '国家名称',
  `analysis` text COMMENT '解析说明',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'flag' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='国旗题库';

--
-- 转存表中的数据 `flag_riddles`
--

INSERT INTO `flag_riddles` (`id`, `content`, `type`, `answer`, `analysis`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(11, 'flag/不丹.png', 'image', '不丹', NULL, 3, 100, NULL, 'flag', 1, '2025-05-24 14:37:42', '2025-05-24 14:37:42'),
(12, 'flag/东帝汶.png', 'image', '东帝汶', NULL, 3, 100, NULL, 'flag', 1, '2025-05-24 14:37:42', '2025-05-24 14:37:42'),
(13, 'flag/中国.png', 'image', '中国', NULL, 3, 100, NULL, 'flag', 1, '2025-05-24 14:37:42', '2025-05-24 14:37:42'),
(14, 'flag/中国澳门.png', 'image', '中国澳门', NULL, 3, 100, NULL, 'flag', 1, '2025-05-24 14:37:42', '2025-05-24 14:37:42'),
(15, 'flag/中国香港.png', 'image', '中国香港', NULL, 3, 100, NULL, 'flag', 1, '2025-05-24 14:37:42', '2025-05-24 14:37:42'),
(16, 'flag/中非共和国.png', 'image', '中非共和国', NULL, 3, 100, NULL, 'flag', 1, '2025-05-24 14:37:42', '2025-05-24 14:37:42'),

-- --------------------------------------------------------

--
-- 表的结构 `fruit_riddles`
--

CREATE TABLE `fruit_riddles` (
  `id` int(11) NOT NULL,
  `content` text NOT NULL COMMENT '题目内容',
  `type` enum('text','image','audio') NOT NULL COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '正确答案',
  `analysis` text COMMENT '解析说明',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'fruit' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='水果题库';

--
-- 转存表中的数据 `fruit_riddles`
--

INSERT INTO `fruit_riddles` (`id`, `content`, `type`, `answer`, `analysis`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(1, '红红的，圆圆的，甜甜的，会流汁', 'text', '苹果', '描述苹果的特征', 1, 100, NULL, 'fruit', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(2, '黄黄的，弯弯的，甜甜的，会剥皮', 'text', '香蕉', '描述香蕉的特征', 1, 100, NULL, 'fruit', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(3, '红红的，圆圆的，酸酸的，会吐籽', 'text', '西瓜', '描述西瓜的特征', 1, 100, NULL, 'fruit', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(4, '紫色的，一串串的，皮薄多汁', 'text', '葡萄', '描述葡萄的特征', 1, 100, NULL, 'fruit', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(5, '外表毛茸茸，里面黄澄澄，味道酸甜', 'text', '猕猴桃', '描述猕猴桃的特征', 2, 100, NULL, 'fruit', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(6, '外皮橙色，果肉分瓣，富含维C', 'text', '橙子', '描述橙子的特征', 1, 100, NULL, 'fruit', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(7, '外皮红色，果肉白色，籽多，味甜', 'text', '荔枝', '描述荔枝的特征', 2, 100, NULL, 'fruit', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(8, '外皮黄绿，果肉脆甜，常见于秋季', 'text', '梨', '描述梨的特征', 1, 100, NULL, 'fruit', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(9, '外皮紫黑，果肉白，籽大', 'text', '山竹', '描述山竹的特征', 2, 100, NULL, 'fruit', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(10, '外皮红色，果肉多汁，常做果酱', 'text', '草莓', '描述草莓的特征', 1, 100, NULL, 'fruit', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `idiom_riddles`
--

CREATE TABLE `idiom_riddles` (
  `id` int(11) NOT NULL,
  `content` text NOT NULL COMMENT '题目内容',
  `type` enum('text','image','audio') NOT NULL COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '正确答案',
  `analysis` text COMMENT '解析说明',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'idiom' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成语题库';

--
-- 转存表中的数据 `idiom_riddles`
--

INSERT INTO `idiom_riddles` (`id`, `content`, `type`, `answer`, `analysis`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(1, '画蛇添足', 'text', '多此一举', '比喻做了多余的事，反而把事情弄糟', 2, 100, NULL, 'idiom', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(2, '守株待兔', 'text', '死守经验', '比喻死守狭隘经验，不知变通', 2, 100, NULL, 'idiom', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(3, '亡羊补牢', 'text', '及时补救', '比喻出了问题后及时补救', 2, 100, NULL, 'idiom', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(4, '掩耳盗铃', 'text', '自欺欺人', '比喻自己欺骗自己', 2, 100, NULL, 'idiom', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(5, '井底之蛙', 'text', '眼界狭小', '比喻见识狭窄', 2, 100, NULL, 'idiom', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(6, '狐假虎威', 'text', '仗势欺人', '比喻依仗别人的势力欺压人', 2, 100, NULL, 'idiom', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(7, '自相矛盾', 'text', '说话矛盾', '比喻自己说话做事前后抵触', 2, 100, NULL, 'idiom', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(8, '刻舟求剑', 'text', '死守教条', '比喻不懂变通', 2, 100, NULL, 'idiom', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(9, '杯弓蛇影', 'text', '疑神疑鬼', '比喻疑虑重重', 2, 100, NULL, 'idiom', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(10, '指鹿为马', 'text', '颠倒黑白', '比喻故意颠倒是非', 2, 100, NULL, 'idiom', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `login_logs`
--

CREATE TABLE `login_logs` (
  `id` int(11) NOT NULL,
  `user_id` varchar(64) NOT NULL COMMENT '用户openid',
  `login_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  `ip_address` varchar(45) DEFAULT '' COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录日志表';

--
-- 转存表中的数据 `login_logs`
--

INSERT INTO `login_logs` (`id`, `user_id`, `login_time`, `ip_address`, `user_agent`, `created_at`) VALUES
(18, 'oQH__6wrOuw0camNtJWY7nFLmJQo', '2025-05-23 13:54:28', '', '', '2025-05-23 13:54:28'),
(19, 'oQH__6wrOuw0camNtJWY7nFLmJQo', '2025-05-23 13:55:12', '', '', '2025-05-23 13:55:12'),
(20, 'oQH__6wrOuw0camNtJWY7nFLmJQo', '2025-05-23 14:16:40', '', '', '2025-05-23 14:16:40'),
(21, 'oQH__6wrOuw0camNtJWY7nFLmJQo', '2025-05-23 14:31:16', '', '', '2025-05-23 14:31:16'),
(22, 'oQH__6wrOuw0camNtJWY7nFLmJQo', '2025-05-23 14:31:25', '', '', '2025-05-23 14:31:25'),
(23, 'oQH__6wrOuw0camNtJWY7nFLmJQo', '2025-05-23 16:27:20', '', '', '2025-05-23 16:27:20');

-- --------------------------------------------------------

--
-- 表的结构 `map_outline_riddles`
--

CREATE TABLE `map_outline_riddles` (
  `id` int(11) NOT NULL,
  `content` varchar(255) NOT NULL COMMENT '图片路径',
  `type` enum('image') NOT NULL DEFAULT 'image' COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '地名',
  `analysis` text COMMENT '解析说明',
  `is_domestic` tinyint(4) DEFAULT '1' COMMENT '是否国内(1国内/0国外)',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'map_outline' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地图轮廓题库';

--
-- 转存表中的数据 `map_outline_riddles`
--

INSERT INTO `map_outline_riddles` (`id`, `content`, `type`, `answer`, `analysis`, `is_domestic`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(11, 'map/上海.png', 'image', '上海', NULL, 1, 3, 100, NULL, 'map_outline', 1, '2025-05-24 14:44:07', '2025-05-24 14:44:07'),
(12, 'map/乌鲁木齐.png', 'image', '乌鲁木齐', NULL, 1, 3, 100, NULL, 'map_outline', 1, '2025-05-24 14:44:07', '2025-05-24 14:44:07'),
(13, 'map/云南.png', 'image', '云南', NULL, 1, 3, 100, NULL, 'map_outline', 1, '2025-05-24 14:44:07', '2025-05-24 14:44:07'),
(14, 'map/兰州.png', 'image', '兰州', NULL, 1, 3, 100, NULL, 'map_outline', 1, '2025-05-24 14:44:07', '2025-05-24 14:44:07'),
(15, 'map/内蒙古.png', 'image', '内蒙古', NULL, 1, 3, 100, NULL, 'map_outline', 1, '2025-05-24 14:44:07', '2025-05-24 14:44:07'),
(16, 'map/北京.png', 'image', '北京', NULL, 1, 3, 100, NULL, 'map_outline', 1, '2025-05-24 14:44:07', '2025-05-24 14:44:07'),

-- --------------------------------------------------------

--
-- 表的结构 `movie_still_riddles`
--

CREATE TABLE `movie_still_riddles` (
  `id` int(11) NOT NULL,
  `content` varchar(255) NOT NULL COMMENT '图片路径',
  `type` enum('image') NOT NULL DEFAULT 'image' COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '电影名称',
  `analysis` text COMMENT '解析说明',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'movie_still' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电影剧照题库';

--
-- 转存表中的数据 `movie_still_riddles`
--

INSERT INTO `movie_still_riddles` (`id`, `content`, `type`, `answer`, `analysis`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(1, '/static/images/movie_still/titanic.jpg', 'image', '泰坦尼克号', '经典爱情电影', 1, 100, NULL, 'movie_still', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(2, '/static/images/movie_still/avatar.jpg', 'image', '阿凡达', '科幻电影', 1, 100, NULL, 'movie_still', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(3, '/static/images/movie_still/harrypotter.jpg', 'image', '哈利波特', '魔法题材电影', 1, 100, NULL, 'movie_still', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(4, '/static/images/movie_still/inception.jpg', 'image', '盗梦空间', '科幻悬疑电影', 1, 100, NULL, 'movie_still', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(5, '/static/images/movie_still/forrestgump.jpg', 'image', '阿甘正传', '励志电影', 1, 100, NULL, 'movie_still', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(6, '/static/images/movie_still/transformers.jpg', 'image', '变形金刚', '科幻动作电影', 1, 100, NULL, 'movie_still', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(7, '/static/images/movie_still/lotr.jpg', 'image', '指环王', '奇幻史诗电影', 1, 100, NULL, 'movie_still', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(8, '/static/images/movie_still/starwars.jpg', 'image', '星球大战', '科幻电影', 1, 100, NULL, 'movie_still', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(9, '/static/images/movie_still/avengers.jpg', 'image', '复仇者联盟', '超级英雄电影', 1, 100, NULL, 'movie_still', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(10, '/static/images/movie_still/spiritedaway.jpg', 'image', '千与千寻', '日本动画电影', 1, 100, NULL, 'movie_still', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `person_riddles`
--

CREATE TABLE `person_riddles` (
  `id` int(11) NOT NULL,
  `content` text NOT NULL COMMENT '题目内容',
  `type` enum('text','image','audio') NOT NULL COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '正确答案',
  `analysis` text COMMENT '解析说明',
  `is_domestic` tinyint(4) DEFAULT '1' COMMENT '是否国内(1国内/0国外)',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'person' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人名题库';

--
-- 转存表中的数据 `person_riddles`
--

INSERT INTO `person_riddles` (`id`, `content`, `type`, `answer`, `analysis`, `is_domestic`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(1, '诗仙', 'text', '李白', '中国唐代著名诗人', 1, 2, 100, NULL, 'person', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(2, '诗圣', 'text', '杜甫', '中国唐代著名诗人', 1, 2, 100, NULL, 'person', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(3, '书圣', 'text', '王羲之', '中国东晋著名书法家', 1, 2, 100, NULL, 'person', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(4, '发明大王', 'text', '爱迪生', '美国著名发明家', 0, 2, 100, NULL, 'person', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(5, '相对论创立者', 'text', '爱因斯坦', '德国著名物理学家', 0, 2, 100, NULL, 'person', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(6, '苹果公司创始人', 'text', '乔布斯', '美国著名企业家', 0, 2, 100, NULL, 'person', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(7, '中国航天之父', 'text', '钱学森', '中国著名科学家', 1, 2, 100, NULL, 'person', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(8, '中国女排主教练', 'text', '郎平', '中国著名排球运动员', 1, 2, 100, NULL, 'person', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(9, '钢琴诗人', 'text', '肖邦', '波兰著名作曲家', 0, 2, 100, NULL, 'person', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(10, '世界足球先生', 'text', '梅西', '阿根廷著名足球运动员', 0, 2, 100, NULL, 'person', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `place_riddles`
--

CREATE TABLE `place_riddles` (
  `id` int(11) NOT NULL,
  `content` text NOT NULL COMMENT '题目内容',
  `type` enum('text','image','audio') NOT NULL COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '正确答案',
  `analysis` text COMMENT '解析说明',
  `is_domestic` tinyint(4) DEFAULT '1' COMMENT '是否国内(1国内/0国外)',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'place' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地名题库';

--
-- 转存表中的数据 `place_riddles`
--

INSERT INTO `place_riddles` (`id`, `content`, `type`, `answer`, `analysis`, `is_domestic`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(1, '上有天堂，下有苏杭', 'text', '杭州', '中国著名旅游城市', 1, 2, 100, NULL, 'place', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(2, '东方巴黎', 'text', '上海', '中国经济中心', 1, 2, 100, NULL, 'place', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(3, '天府之国', 'text', '四川', '中国著名美食之都', 1, 2, 100, NULL, 'place', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(4, '自由女神像所在地', 'text', '纽约', '美国著名城市', 0, 2, 100, NULL, 'place', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(5, '铁塔之都', 'text', '巴黎', '法国首都', 0, 2, 100, NULL, 'place', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(6, '袋鼠之国', 'text', '澳大利亚', '大洋洲国家', 0, 2, 100, NULL, 'place', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(7, '金字塔之国', 'text', '埃及', '非洲著名国家', 0, 2, 100, NULL, 'place', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(8, '樱花之国', 'text', '日本', '亚洲岛国', 0, 2, 100, NULL, 'place', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(9, '冰雪之都', 'text', '哈尔滨', '中国著名冰雪城市', 1, 2, 100, NULL, 'place', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(10, '熊猫故乡', 'text', '成都', '中国四川省省会', 1, 2, 100, NULL, 'place', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `song_poetry_riddles`
--

CREATE TABLE `song_poetry_riddles` (
  `id` int(11) NOT NULL,
  `content` text NOT NULL COMMENT '题目内容',
  `type` enum('text') NOT NULL DEFAULT 'text' COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '词名',
  `author` varchar(11) NOT NULL COMMENT '作者',
  `analysis` text COMMENT '解析说明',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'song_poetry' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='宋词题库';

--
-- 转存表中的数据 `song_poetry_riddles`
--

INSERT INTO `song_poetry_riddles` (`id`, `content`, `type`, `answer`, `author`, `analysis`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(1552, '丙辰中秋，欢饮达旦，大醉，作此篇，兼怀子由。', 'text', '水调歌头', '苏轼', '哲理宋词三百首', 3, 100, NULL, 'song_poetry', 1, '2025-05-24 08:26:24', '2025-05-24 08:26:24'),
(1553, '明月几时有？把酒问青天。', 'text', '水调歌头', '苏轼', '哲理宋词三百首', 3, 100, NULL, 'song_poetry', 1, '2025-05-24 08:26:24', '2025-05-24 08:26:24'),
(1554, '不知天上宫阙，今夕是何年。', 'text', '水调歌头', '苏轼', '哲理宋词三百首', 3, 100, NULL, 'song_poetry', 1, '2025-05-24 08:26:24', '2025-05-24 08:26:24'),
(1555, '我欲乘风归去，又恐琼楼玉宇，高处不胜寒。', 'text', '水调歌头', '苏轼', '哲理宋词三百首', 3, 100, NULL, 'song_poetry', 1, '2025-05-24 08:26:24', '2025-05-24 08:26:24'),
(1556, '起舞弄清影，何似在人间。', 'text', '水调歌头', '苏轼', '哲理宋词三百首', 3, 100, NULL, 'song_poetry', 1, '2025-05-24 08:26:24', '2025-05-24 08:26:24'),
(1557, '转朱阁，低绮户，照无眠。', 'text', '水调歌头', '苏轼', '哲理宋词三百首', 3, 100, NULL, 'song_poetry', 1, '2025-05-24 08:26:24', '2025-05-24 08:26:24'),
(1558, '不应有恨，何事长向别时圆？', 'text', '水调歌头', '苏轼', '哲理宋词三百首', 3, 100, NULL, 'song_poetry', 1, '2025-05-24 08:26:24', '2025-05-24 08:26:24'),
(3099, '为盟誓。今生断不孤鸳被。', 'text', '玉女摇仙佩', '柳永', '', 3, 100, NULL, 'song_poetry', 1, '2025-05-24 08:26:26', '2025-05-24 08:26:26'),
(3100, '薄衾小枕凉天气，乍觉别离滋味。', 'text', '忆帝京', '柳永', '', 3, 100, NULL, 'song_poetry', 1, '2025-05-24 08:26:26', '2025-05-24 08:26:26'),
(3101, '展转数寒更，起了还重睡。', 'text', '忆帝京', '柳永', '', 3, 100, NULL, 'song_poetry', 1, '2025-05-24 08:26:26', '2025-05-24 08:26:26'),
(3102, '毕竟不成眠，一夜长如岁。', 'text', '忆帝京', '柳永', '', 3, 100, NULL, 'song_poetry', 1, '2025-05-24 08:26:26', '2025-05-24 08:26:26');

-- --------------------------------------------------------

--
-- 表的结构 `tang_poetry_riddles`
--

CREATE TABLE `tang_poetry_riddles` (
  `id` int(11) NOT NULL,
  `content` text NOT NULL COMMENT '题目内容',
  `type` enum('text') NOT NULL DEFAULT 'text' COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '诗名',
  `author` varchar(11) NOT NULL COMMENT '作者',
  `analysis` text COMMENT '解析说明',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'tang_poetry' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='唐诗题库';

--
-- 转存表中的数据 `tang_poetry_riddles`
--

INSERT INTO `tang_poetry_riddles` (`id`, `content`, `type`, `answer`, `author`, `analysis`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(1, '床前明月光，疑是地上霜。举头望明月，低头思故乡。', 'text', '静夜思', '李白', '李白的名作，描写思乡之情', 1, 100, NULL, 'tang_poetry', 1, '2025-05-23 11:47:57', '2025-05-24 08:34:36'),
(2, '春眠不觉晓，处处闻啼鸟。夜来风雨声，花落知多少。', 'text', '春晓', '孟浩然', '孟浩然的名作，描写春天早晨', 1, 100, NULL, 'tang_poetry', 1, '2025-05-23 11:47:57', '2025-05-24 08:34:45'),
(3, '白日依山尽，黄河入海流。欲穷千里目，更上一层楼。', 'text', '登鹳雀楼', '王之涣', '王之涣的名作，描写登高望远', 1, 100, NULL, 'tang_poetry', 1, '2025-05-23 11:47:57', '2025-05-24 08:34:53'),
(4, '两个黄鹂鸣翠柳，一行白鹭上青天。', 'text', '绝句', '杜甫', '杜甫的名作，描写春景', 1, 100, NULL, 'tang_poetry', 1, '2025-05-23 11:47:57', '2025-05-24 08:35:04'),
(5, '黄四娘家花满蹊，千朵万朵压枝低。', 'text', '江畔独步寻花', '杜甫', '杜甫的名作，描写春花', 1, 100, NULL, 'tang_poetry', 1, '2025-05-23 11:47:57', '2025-05-24 08:35:11'),
(6, '会当凌绝顶，一览众山小。', 'text', '望岳', '杜甫', '杜甫的名作，描写泰山壮丽', 1, 100, NULL, 'tang_poetry', 1, '2025-05-23 11:47:57', '2025-05-24 08:35:18'),
(4355, '日暮酒醒人已远，满天风雨下西楼。', 'text', '谢亭送别', '许浑', '', 3, 100, NULL, 'tang_poetry', 1, '2025-05-24 08:30:52', '2025-05-24 08:30:52');

-- --------------------------------------------------------

--
-- 表的结构 `users`
--

CREATE TABLE `users` (
  `id` varchar(50) NOT NULL COMMENT '用户ID(微信openid)',
  `nickname` varchar(100) DEFAULT NULL COMMENT '昵称',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `total_score` int(11) DEFAULT '0' COMMENT '总积分',
  `total_answers` int(11) DEFAULT '0' COMMENT '总答题数',
  `correct_answers` int(11) DEFAULT '0' COMMENT '正确答题数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

--
-- 转存表中的数据 `users`
--

INSERT INTO `users` (`id`, `nickname`, `avatar_url`, `total_score`, `total_answers`, `correct_answers`, `created_at`, `updated_at`) VALUES
('oQH__6wrOuw0camNtJWY7nFLmJQo', '哈哈哈', 'https://wx.izzs.cn/static/pic/oQH__6wrOuw0camNtJWY7nFLmJQo.jpg?t=1748012161502&t=1748098109427&t=1748098109427', 700, 9, 7, '2025-05-23 13:54:28', '2025-05-24 14:48:38'),
('user001', '小明', '/static/avatar/1.png', 1200, 50, 40, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user002', '小红', '/static/avatar/2.png', 1100, 48, 38, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user003', '小刚', '/static/avatar/3.png', 1050, 45, 35, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user004', '小美', '/static/avatar/4.png', 980, 40, 32, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user005', '小强', '/static/avatar/5.png', 900, 38, 30, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user006', '小丽', '/static/avatar/6.png', 850, 35, 28, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user007', '小军', '/static/avatar/7.png', 800, 33, 26, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user008', '小芳', '/static/avatar/8.png', 780, 32, 25, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user009', '小伟', '/static/avatar/9.png', 750, 30, 24, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user010', '小霞', '/static/avatar/10.png', 700, 28, 22, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user011', '猜谜高手', '/static/avatar/11.png', 2000, 80, 75, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user012', '智慧之星', '/static/avatar/12.png', 1800, 70, 65, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user013', '古诗达人', '/static/avatar/13.png', 1650, 60, 55, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user014', '成语专家', '/static/avatar/14.png', 1500, 65, 58, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user015', '音乐天才', '/static/avatar/15.png', 1350, 55, 48, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user016', '图片猜王', '/static/avatar/16.png', 1250, 52, 45, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user017', '文字高手', '/static/avatar/17.png', 1150, 50, 42, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user018', '知识达人', '/static/avatar/18.png', 1050, 45, 38, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user019', '题目杀手', '/static/avatar/19.png', 950, 42, 35, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
('user020', '挑战者', '/static/avatar/20.png', 850, 40, 32, '2025-05-23 11:47:57', '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `user_achievements`
--

CREATE TABLE `user_achievements` (
  `id` int(11) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `achievement_id` int(11) NOT NULL,
  `achieved_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户成就表';

--
-- 转存表中的数据 `user_achievements`
--

INSERT INTO `user_achievements` (`id`, `user_id`, `achievement_id`, `achieved_at`) VALUES
(1, 'user001', 1, '2025-05-23 11:47:57'),
(2, 'user001', 2, '2025-05-23 11:47:57'),
(3, 'user001', 3, '2025-05-23 11:47:57'),
(4, 'user001', 6, '2025-05-23 11:47:57'),
(5, 'user001', 8, '2025-05-23 11:47:57'),
(6, 'user002', 1, '2025-05-23 11:47:57'),
(7, 'user002', 2, '2025-05-23 11:47:57'),
(8, 'user002', 3, '2025-05-23 11:47:57'),
(9, 'user002', 7, '2025-05-23 11:47:57'),
(10, 'user003', 1, '2025-05-23 11:47:57'),
(11, 'user003', 2, '2025-05-23 11:47:57'),
(12, 'user003', 9, '2025-05-23 11:47:57'),
(13, 'user004', 1, '2025-05-23 11:47:57'),
(14, 'user004', 2, '2025-05-23 11:47:57'),
(15, 'user004', 10, '2025-05-23 11:47:57'),
(16, 'user005', 1, '2025-05-23 11:47:57'),
(17, 'user005', 2, '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `user_answers`
--

CREATE TABLE `user_answers` (
  `id` int(11) NOT NULL,
  `user_id` varchar(50) NOT NULL COMMENT '用户ID',
  `riddle_id` int(11) NOT NULL COMMENT '谜题ID',
  `table_name` varchar(50) DEFAULT NULL COMMENT '题目来源表名',
  `user_answer` varchar(255) NOT NULL COMMENT '用户答案',
  `is_correct` tinyint(4) DEFAULT '0' COMMENT '是否正确',
  `score` int(11) DEFAULT '0' COMMENT '得分',
  `time_used` int(11) DEFAULT '0' COMMENT '用时(秒)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户答题记录表';

--
-- 转存表中的数据 `user_answers`
--

INSERT INTO `user_answers` (`id`, `user_id`, `riddle_id`, `table_name`, `user_answer`, `is_correct`, `score`, `time_used`, `created_at`) VALUES
(38, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 2, NULL, '春晓', 1, 100, 5, '2025-05-23 13:02:09'),
(39, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 5, NULL, '坐井观天', 0, 0, 5, '2025-05-23 13:07:31'),
(40, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 3, NULL, '为时不晚', 0, 0, 8, '2025-05-23 13:07:41'),
(41, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 8, NULL, '缘木求鱼', 0, 0, 7, '2025-05-23 13:07:49'),
(42, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 10, NULL, '颠倒黑白', 1, 100, 14, '2025-05-23 13:08:05'),
(43, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 1, NULL, '多此一举', 1, 100, 11, '2025-05-23 13:08:18'),
(44, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 4, NULL, '微信', 0, 0, 6, '2025-05-23 13:10:25'),
(45, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 8, NULL, '猜谜识图小程序', 0, 0, 10, '2025-05-23 13:10:37'),
(46, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 1, NULL, '杭州', 1, 100, 9, '2025-05-23 16:30:56'),
(47, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 9, NULL, '哈尔滨', 1, 100, 4, '2025-05-23 16:31:02'),
(48, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 7, NULL, '荔枝', 1, 100, 27, '2025-05-23 16:31:30'),
(49, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 8, NULL, '日本', 1, 100, 5, '2025-05-23 16:31:37'),
(50, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 1, NULL, '牛', 1, 100, 6, '2025-05-23 16:31:44'),
(51, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 2, NULL, '喜鹊', 0, 0, 11, '2025-05-23 16:31:57'),
(52, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 6, NULL, '变色龙', 1, 100, 17, '2025-05-23 16:32:16'),
(53, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 6, NULL, '橘子', 0, 0, 9, '2025-05-23 16:32:27'),
(54, 'oQH__6wrOuw0camNtJWY7nFLmJQo', 4, NULL, '葡萄', 1, 100, 8, '2025-05-23 16:32:36');

-- --------------------------------------------------------

--
-- 表的结构 `user_stats`
--

CREATE TABLE `user_stats` (
  `id` int(11) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `date` date NOT NULL,
  `daily_score` int(11) DEFAULT '0' COMMENT '当日得分',
  `daily_answers` int(11) DEFAULT '0' COMMENT '当日答题数',
  `daily_correct` int(11) DEFAULT '0' COMMENT '当日正确数',
  `best_streak` int(11) DEFAULT '0' COMMENT '最佳连胜',
  `current_streak` int(11) DEFAULT '0' COMMENT '当前连胜',
  `favorite_type` varchar(50) DEFAULT NULL COMMENT '最喜欢的题型',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户每日统计表';

--
-- 转存表中的数据 `user_stats`
--

INSERT INTO `user_stats` (`id`, `user_id`, `date`, `daily_score`, `daily_answers`, `daily_correct`, `best_streak`, `current_streak`, `favorite_type`, `created_at`) VALUES
(1, 'user001', '2024-01-15', 500, 10, 8, 8, 3, 'idiom', '2025-05-23 11:47:57'),
(2, 'user001', '2024-01-14', 400, 8, 6, 6, 0, 'idiom', '2025-05-23 11:47:57'),
(3, 'user001', '2024-01-13', 300, 6, 5, 5, 5, 'animal', '2025-05-23 11:47:57'),
(4, 'user002', '2024-01-15', 450, 9, 7, 5, 2, 'xiehouyu', '2025-05-23 11:47:57'),
(5, 'user002', '2024-01-14', 350, 7, 5, 4, 0, 'fruit', '2025-05-23 11:47:57'),
(6, 'user003', '2024-01-15', 400, 8, 6, 6, 1, 'flag', '2025-05-23 11:47:57'),
(7, 'user004', '2024-01-15', 380, 7, 6, 4, 4, 'animal', '2025-05-23 11:47:57'),
(8, 'user005', '2024-01-15', 320, 6, 5, 3, 0, 'fruit', '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `word_riddles`
--

CREATE TABLE `word_riddles` (
  `id` int(11) NOT NULL,
  `riddle_text` text NOT NULL COMMENT '谜面',
  `answer` varchar(255) NOT NULL COMMENT '正确答案',
  `hint` text COMMENT '提示',
  `category` varchar(50) DEFAULT NULL COMMENT '分类(形声字/会意字等)',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `riddle_type` varchar(20) DEFAULT 'riddle' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字谜表';

--
-- 转存表中的数据 `word_riddles`
--

INSERT INTO `word_riddles` (`id`, `riddle_text`, `answer`, `hint`, `category`, `difficulty`, `points`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(1, '一口咬掉牛尾巴', '告', '口+牛去尾', '会意字', 2, 100, 'riddle', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(2, '有目共睹', '者', '有+目组成', '会意字', 2, 100, 'riddle', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(3, '十字对十字，太阳对月亮', '朝', '十+十+日+月', '会意字', 3, 100, 'riddle', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(4, '一人一张口，口下长只手', '拿', '人+口+手', '会意字', 2, 100, 'riddle', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(5, '山上还有山', '出', '山+山', '会意字', 1, 100, 'riddle', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(6, '十个哥哥', '克', '十+兄=克', '会意字', 2, 100, 'riddle', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(7, '四个人搬个木头', '杰', '四个人+木', '会意字', 2, 100, 'riddle', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(8, '一人在内', '肉', '人在内部', '会意字', 2, 100, 'riddle', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(9, '一加一', '王', '一+一=王', '会意字', 1, 100, 'riddle', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(10, '七十二小时', '晶', '三个日', '会意字', 3, 100, 'riddle', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57');

-- --------------------------------------------------------

--
-- 表的结构 `xiehouyu_riddles`
--

CREATE TABLE `xiehouyu_riddles` (
  `id` int(11) NOT NULL,
  `content` text NOT NULL COMMENT '题目内容',
  `type` enum('text','image','audio') NOT NULL COMMENT '题目类型',
  `answer` varchar(255) NOT NULL COMMENT '正确答案',
  `analysis` text COMMENT '解析说明',
  `difficulty` tinyint(4) DEFAULT '3' COMMENT '难度(1-5)',
  `points` int(11) DEFAULT '100' COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项（如有）',
  `riddle_type` varchar(20) DEFAULT 'xiehouyu' COMMENT '题目分类标识',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0禁用/1启用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='歇后语题库';

--
-- 转存表中的数据 `xiehouyu_riddles`
--

INSERT INTO `xiehouyu_riddles` (`id`, `content`, `type`, `answer`, `analysis`, `difficulty`, `points`, `options`, `riddle_type`, `status`, `created_at`, `updated_at`) VALUES
(1, '哑巴吃黄连', 'text', '有苦说不出', '比喻有苦说不出', 1, 100, NULL, 'xiehouyu', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(2, '泥菩萨过江', 'text', '自身难保', '比喻自身难保', 1, 100, NULL, 'xiehouyu', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(3, '和尚打伞', 'text', '无法无天', '比喻无法无天', 1, 100, NULL, 'xiehouyu', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(4, '老鼠掉进书箱里', 'text', '咬文嚼字', '比喻过分斟酌字句', 1, 100, NULL, 'xiehouyu', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(5, '外甥打灯笼', 'text', '照舅', '比喻照旧', 1, 100, NULL, 'xiehouyu', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(6, '八仙过海', 'text', '各显神通', '比喻各自有本领', 1, 100, NULL, 'xiehouyu', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(7, '兔子尾巴', 'text', '长不了', '比喻不能长久', 1, 100, NULL, 'xiehouyu', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(8, '竹篮打水', 'text', '一场空', '比喻白费力气', 1, 100, NULL, 'xiehouyu', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(9, '孔夫子搬家', 'text', '净是书', '比喻全是书', 1, 100, NULL, 'xiehouyu', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57'),
(10, '小葱拌豆腐', 'text', '一清二白', '比喻清白无瑕', 1, 100, NULL, 'xiehouyu', 1, '2025-05-23 11:47:57', '2025-05-23 11:47:57');

--
-- 转储表的索引
--

--
-- 表的索引 `achievements`
--
ALTER TABLE `achievements`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `animal_riddles`
--
ALTER TABLE `animal_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `app_icon_riddles`
--
ALTER TABLE `app_icon_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `audio_animal_riddles`
--
ALTER TABLE `audio_animal_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `audio_song_riddles`
--
ALTER TABLE `audio_song_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `brand_logo_riddles`
--
ALTER TABLE `brand_logo_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `car_logo_riddles`
--
ALTER TABLE `car_logo_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `flag_riddles`
--
ALTER TABLE `flag_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `fruit_riddles`
--
ALTER TABLE `fruit_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `idiom_riddles`
--
ALTER TABLE `idiom_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `login_logs`
--
ALTER TABLE `login_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_login_time` (`login_time`);

--
-- 表的索引 `map_outline_riddles`
--
ALTER TABLE `map_outline_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `movie_still_riddles`
--
ALTER TABLE `movie_still_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `person_riddles`
--
ALTER TABLE `person_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `place_riddles`
--
ALTER TABLE `place_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `song_poetry_riddles`
--
ALTER TABLE `song_poetry_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `tang_poetry_riddles`
--
ALTER TABLE `tang_poetry_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_total_score` (`total_score`);

--
-- 表的索引 `user_achievements`
--
ALTER TABLE `user_achievements`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_user_achievement` (`user_id`,`achievement_id`),
  ADD KEY `achievement_id` (`achievement_id`);

--
-- 表的索引 `user_answers`
--
ALTER TABLE `user_answers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_riddle_id` (`riddle_id`),
  ADD KEY `idx_table_name` (`table_name`);

--
-- 表的索引 `user_stats`
--
ALTER TABLE `user_stats`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_user_date` (`user_id`,`date`),
  ADD KEY `idx_date` (`date`);

--
-- 表的索引 `word_riddles`
--
ALTER TABLE `word_riddles`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_category` (`category`);

--
-- 表的索引 `xiehouyu_riddles`
--
ALTER TABLE `xiehouyu_riddles`
  ADD PRIMARY KEY (`id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `achievements`
--
ALTER TABLE `achievements`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `animal_riddles`
--
ALTER TABLE `animal_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `app_icon_riddles`
--
ALTER TABLE `app_icon_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1921;

--
-- 使用表AUTO_INCREMENT `audio_animal_riddles`
--
ALTER TABLE `audio_animal_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `audio_song_riddles`
--
ALTER TABLE `audio_song_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `brand_logo_riddles`
--
ALTER TABLE `brand_logo_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `car_logo_riddles`
--
ALTER TABLE `car_logo_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `flag_riddles`
--
ALTER TABLE `flag_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=246;

--
-- 使用表AUTO_INCREMENT `fruit_riddles`
--
ALTER TABLE `fruit_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `idiom_riddles`
--
ALTER TABLE `idiom_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `login_logs`
--
ALTER TABLE `login_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- 使用表AUTO_INCREMENT `map_outline_riddles`
--
ALTER TABLE `map_outline_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=73;

--
-- 使用表AUTO_INCREMENT `movie_still_riddles`
--
ALTER TABLE `movie_still_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `person_riddles`
--
ALTER TABLE `person_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `place_riddles`
--
ALTER TABLE `place_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `song_poetry_riddles`
--
ALTER TABLE `song_poetry_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3103;

--
-- 使用表AUTO_INCREMENT `tang_poetry_riddles`
--
ALTER TABLE `tang_poetry_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4356;

--
-- 使用表AUTO_INCREMENT `user_achievements`
--
ALTER TABLE `user_achievements`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- 使用表AUTO_INCREMENT `user_answers`
--
ALTER TABLE `user_answers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=55;

--
-- 使用表AUTO_INCREMENT `user_stats`
--
ALTER TABLE `user_stats`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- 使用表AUTO_INCREMENT `word_riddles`
--
ALTER TABLE `word_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `xiehouyu_riddles`
--
ALTER TABLE `xiehouyu_riddles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- 限制导出的表
--

--
-- 限制表 `login_logs`
--
ALTER TABLE `login_logs`
  ADD CONSTRAINT `login_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- 限制表 `user_achievements`
--
ALTER TABLE `user_achievements`
  ADD CONSTRAINT `user_achievements_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `user_achievements_ibfk_2` FOREIGN KEY (`achievement_id`) REFERENCES `achievements` (`id`);

--
-- 限制表 `user_stats`
--
ALTER TABLE `user_stats`
  ADD CONSTRAINT `user_stats_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
